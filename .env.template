# MCP Crawl4AI RAG Server Environment Configuration Template
# Copy this file to .env and fill in your actual values
# NEVER commit .env files with real secrets to version control

# Transport Configuration
TRANSPORT=sse
HOST=127.0.0.1  # Use 127.0.0.1 for local development, 0.0.0.0 for Docker
PORT=8051

# OpenAI API Configuration
# Get your API key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=sk-proj-your-actual-api-key-here
MODEL_CHOICE=gpt-4o-mini

# RAG Strategy Flags
USE_CONTEXTUAL_EMBEDDINGS=true
USE_HYBRID_SEARCH=true
USE_AGENTIC_RAG=true
USE_RERANKING=true
USE_KNOWLEDGE_GRAPH=false

# REQUIRED: Supabase Configuration for RAG functionality
# Without these, the server will start but RAG tools will be disabled
# Get credentials from: https://supabase.com/dashboard/project/YOUR_PROJECT/settings/api
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_SERVICE_KEY=your-service-role-secret-here

# Neo4j Configuration (optional, for knowledge graph features)
# Use bolt://localhost:7687 for local, or your cloud instance URL
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=your-secure-neo4j-password-here

# Security Notes:
# 1. Never commit this file with real values
# 2. Use strong passwords (minimum 12 characters)
# 3. Rotate API keys regularly
# 4. Use different credentials for different environments
# 5. Consider using a secrets management service for production