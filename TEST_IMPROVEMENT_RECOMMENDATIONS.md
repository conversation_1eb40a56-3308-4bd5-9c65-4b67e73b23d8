# Test Improvement Recommendations

## Overview

Based on the comprehensive testing analysis of the refactored MCP Crawl4AI RAG server, this document provides specific recommendations for improving test coverage, quality, and reliability.

## Critical Issues to Address

### 1. **Health Check Test Failures** (High Priority)

**Problem**: AsyncMock configuration issues with aiohttp session mocking  
**Impact**: 3/4 health check tests failing  
**Solution**:

```python
# Fix in tests/test_reliability.py
@pytest.mark.asyncio
async def test_check_url_health_success(self, health_checker):
    """Test successful health check."""
    with patch('aiohttp.ClientSession') as mock_session:
        # Create proper async context manager
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.__aenter__ = AsyncMock(return_value=mock_response)
        mock_response.__aexit__ = AsyncMock(return_value=None)
        
        mock_session.return_value.__aenter__.return_value.head.return_value = mock_response
        
        is_healthy, error = await health_checker.check_url_health("https://example.com")
        
        assert is_healthy is True
        assert error is None
```

### 2. **Circuit Breaker Integration Test** (High Priority)

**Problem**: Circuit breaker opening during retry test execution  
**Impact**: 1/4 retry integration test failing  
**Solution**:

```python
# Adjust circuit breaker configuration for testing
@pytest.fixture
def reliable_crawler_with_loose_circuit_breaker(self):
    """Create a reliable crawler with looser circuit breaker for testing."""
    return ReliableCrawler(
        retry_config=RetryConfig(max_retries=2, base_delay=0.1),
        timeout_config=TimeoutConfig(connection_timeout=1.0),
        circuit_breaker_config=CircuitBreakerConfig(
            failure_threshold=10,  # Higher threshold for testing
            recovery_timeout=0.5
        ),
        cache_config=CacheConfig(enabled=True, max_size=10, ttl_seconds=60)
    )
```

### 3. **Missing Dependencies** (Medium Priority)

**Problem**: Several test files cannot run due to missing dependencies  
**Impact**: 5/7 test files have import errors  
**Solution**:

```bash
# Install missing dependencies
pip install crawl4ai pydantic-ai sentence-transformers graphiti-core
```

## Recommended Test Enhancements

### 1. **Refactored Module Unit Tests**

Create comprehensive unit tests for each refactored module:

```python
# tests/test_refactored_modules.py
import pytest
from unittest.mock import Mock, AsyncMock, patch
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

class TestServerModules:
    """Test server layer modules."""
    
    def test_dependencies_dataclass(self):
        """Test Crawl4AIContext dataclass."""
        from server.dependencies import Crawl4AIContext
        
        context = Crawl4AIContext(
            supabase_client=Mock(),
            openai_client=Mock(),
            reranking_model=Mock()
        )
        
        assert context.supabase_client is not None
        assert context.openai_client is not None
        assert context.reranking_model is not None
    
    @pytest.mark.asyncio
    async def test_resource_management(self):
        """Test global resource management."""
        from server.dependencies import set_global_resources, get_global_resources
        
        mock_context = Mock()
        set_global_resources(mock_context)
        
        retrieved = get_global_resources()
        assert retrieved == mock_context

class TestToolsModules:
    """Test tools layer modules."""
    
    @pytest.mark.asyncio
    async def test_crawling_tools_signatures(self):
        """Test crawling tools have correct signatures."""
        from tools.crawling import crawl_single_page, smart_crawl_url
        import inspect
        
        # Check crawl_single_page
        sig = inspect.signature(crawl_single_page)
        assert 'url' in sig.parameters
        assert 'resources' in sig.parameters
        
        # Check smart_crawl_url
        sig = inspect.signature(smart_crawl_url)
        assert 'url' in sig.parameters
        assert 'resources' in sig.parameters
    
    @pytest.mark.asyncio
    async def test_search_tools_signatures(self):
        """Test search tools have correct signatures."""
        from tools.search import perform_rag_query, search_code_examples
        import inspect
        
        # Check perform_rag_query
        sig = inspect.signature(perform_rag_query)
        assert 'query' in sig.parameters
        assert 'resources' in sig.parameters
        
        # Check search_code_examples
        sig = inspect.signature(search_code_examples)
        assert 'query' in sig.parameters
        assert 'resources' in sig.parameters

class TestCoreModules:
    """Test core business logic modules."""
    
    def test_chunking_function(self):
        """Test smart chunking functionality."""
        from core.crawling import smart_chunk_markdown
        
        text = "# Header 1\n\nSome content here.\n\n## Header 2\n\nMore content here."
        chunks = smart_chunk_markdown(text, chunk_size=50)
        
        assert len(chunks) > 0
        assert all(isinstance(chunk, str) for chunk in chunks)
    
    def test_response_utilities(self):
        """Test response utility functions."""
        from core.utils import create_standard_error_response, create_standard_success_response
        
        # Test error response
        error_response = create_standard_error_response(
            error="Test error",
            context="Test context",
            error_type="test_error"
        )
        assert '"success": false' in error_response
        assert '"error": "Test error"' in error_response
        
        # Test success response
        success_response = create_standard_success_response(
            data={"test": "data"},
            context="Test context"
        )
        assert '"success": true' in success_response
        assert '"test": "data"' in success_response
```

### 2. **Integration Test Suite**

Create tests that validate the integration between refactored modules:

```python
# tests/test_integration.py
import pytest
from unittest.mock import Mock, AsyncMock, patch
import sys
from pathlib import Path

sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

class TestModuleIntegration:
    """Test integration between refactored modules."""
    
    @pytest.mark.asyncio
    async def test_tool_dependency_injection(self):
        """Test that tools properly receive injected dependencies."""
        from tools.crawling import crawl_single_page
        from server.dependencies import Crawl4AIContext, set_global_resources
        
        # Create mock context
        mock_context = Crawl4AIContext(
            supabase_client=Mock(),
            openai_client=Mock(),
            reranking_model=Mock()
        )
        
        # Set global resources
        set_global_resources(mock_context)
        
        # Mock the actual crawling logic
        with patch('tools.crawling.AsyncWebCrawler'):
            # Test that the tool can be called with proper context
            result = await crawl_single_page("https://example.com")
            assert result is not None
    
    def test_mcp_server_tool_registration(self):
        """Test that MCP server properly registers tools."""
        from server.mcp_server import mcp
        from tools.crawling import crawl_single_page
        
        # Register tool
        mcp.register_tool(crawl_single_page)
        
        # Verify tool is registered (basic check)
        assert hasattr(mcp, 'tools') or hasattr(mcp, '_tools')
```

### 3. **Performance and Load Testing**

Add performance tests for the refactored architecture:

```python
# tests/test_performance.py
import pytest
import time
import asyncio
from unittest.mock import Mock, AsyncMock

class TestPerformance:
    """Performance tests for refactored modules."""
    
    @pytest.mark.asyncio
    async def test_concurrent_tool_execution(self):
        """Test concurrent execution of multiple tools."""
        from tools.crawling import crawl_single_page
        from server.dependencies import Crawl4AIContext, set_global_resources
        
        # Setup mock context
        mock_context = Crawl4AIContext(
            supabase_client=Mock(),
            openai_client=Mock(),
            reranking_model=Mock()
        )
        set_global_resources(mock_context)
        
        # Mock crawling to return quickly
        with patch('tools.crawling.AsyncWebCrawler'):
            start_time = time.time()
            
            # Run multiple tools concurrently
            tasks = [
                crawl_single_page(f"https://example{i}.com")
                for i in range(10)
            ]
            
            results = await asyncio.gather(*tasks)
            
            end_time = time.time()
            
            # Should complete quickly with proper async handling
            assert end_time - start_time < 5.0
            assert len(results) == 10
    
    def test_memory_usage_improvement(self):
        """Test that refactored modules use less memory."""
        import psutil
        import os
        
        # Get current memory usage
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss
        
        # Import refactored modules
        from server.mcp_server import mcp
        from tools.crawling import crawl_single_page
        from tools.search import perform_rag_query
        from dashboard.routes import setup_dashboard_routes
        
        # Check memory usage after imports
        final_memory = process.memory_info().rss
        memory_increase = final_memory - initial_memory
        
        # Memory increase should be reasonable (less than 100MB)
        assert memory_increase < 100 * 1024 * 1024  # 100MB
```

### 4. **Error Handling and Edge Cases**

Enhance error handling tests:

```python
# tests/test_error_handling.py
import pytest
from unittest.mock import Mock, AsyncMock, patch
import sys
from pathlib import Path

sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

class TestErrorHandling:
    """Test error handling across refactored modules."""
    
    @pytest.mark.asyncio
    async def test_tool_error_propagation(self):
        """Test that tool errors are properly propagated."""
        from tools.crawling import crawl_single_page
        from server.dependencies import Crawl4AIContext, set_global_resources
        
        # Setup context with failing dependencies
        mock_context = Crawl4AIContext(
            supabase_client=Mock(),
            openai_client=Mock(),
            reranking_model=Mock()
        )
        set_global_resources(mock_context)
        
        # Mock crawler to raise exception
        with patch('tools.crawling.AsyncWebCrawler') as mock_crawler:
            mock_crawler.return_value.__aenter__.side_effect = Exception("Connection failed")
            
            result = await crawl_single_page("https://example.com")
            
            # Should return error response, not raise exception
            assert '"success": false' in result
            assert "Connection failed" in result
    
    def test_dependency_injection_failure(self):
        """Test behavior when dependency injection fails."""
        from server.dependencies import get_global_resources, _global_resources
        
        # Clear global resources
        _global_resources.clear()
        
        # Should raise appropriate error
        with pytest.raises(ValueError, match="Global resources not initialized"):
            get_global_resources()
```

### 5. **Test Configuration and Fixtures**

Create comprehensive test configuration:

```python
# tests/conftest.py
import pytest
from unittest.mock import Mock, AsyncMock
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

@pytest.fixture
def mock_supabase_client():
    """Mock Supabase client for testing."""
    client = Mock()
    client.from_.return_value.select.return_value.execute.return_value.data = []
    return client

@pytest.fixture
def mock_openai_client():
    """Mock OpenAI client for testing."""
    client = Mock()
    client.embeddings.create.return_value.data = [Mock(embedding=[0.1] * 768)]
    return client

@pytest.fixture
def mock_crawl4ai_context(mock_supabase_client, mock_openai_client):
    """Mock Crawl4AI context for testing."""
    from server.dependencies import Crawl4AIContext
    
    return Crawl4AIContext(
        supabase_client=mock_supabase_client,
        openai_client=mock_openai_client,
        reranking_model=Mock()
    )

@pytest.fixture(autouse=True)
def setup_test_environment(mock_crawl4ai_context):
    """Setup test environment with mocked dependencies."""
    from server.dependencies import set_global_resources
    
    # Set up global resources for testing
    set_global_resources(mock_crawl4ai_context)
    
    yield
    
    # Cleanup after test
    from server.dependencies import _global_resources
    _global_resources.clear()
```

## Test Automation Recommendations

### 1. **GitHub Actions Workflow**

Create `.github/workflows/test.yml`:

```yaml
name: Test Suite

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        python-version: [3.11, 3.12]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-cov pytest-asyncio
    
    - name: Run tests
      run: |
        pytest tests/ -v --cov=src --cov-report=xml --cov-report=html
    
    - name: Upload coverage
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
```

### 2. **Pre-commit Hooks**

Create `.pre-commit-config.yaml`:

```yaml
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files
  
  - repo: https://github.com/psf/black
    rev: 23.1.0
    hooks:
      - id: black
        language_version: python3
  
  - repo: https://github.com/pycqa/flake8
    rev: 6.0.0
    hooks:
      - id: flake8
        args: [--max-line-length=88, --extend-ignore=E203]
  
  - repo: local
    hooks:
      - id: pytest
        name: pytest
        entry: pytest
        language: system
        types: [python]
        pass_filenames: false
        always_run: true
```

### 3. **Test Coverage Goals**

Set coverage targets:

```ini
# .coveragerc
[run]
source = src
omit = 
    src/venv/*
    src/*/test_*
    src/*/conftest.py

[report]
exclude_lines =
    pragma: no cover
    def __repr__
    raise AssertionError
    raise NotImplementedError

[html]
directory = htmlcov

[xml]
output = coverage.xml
```

## Implementation Timeline

### **Phase 1: Critical Fixes** (1-2 days)
- Fix health check test failures
- Resolve circuit breaker integration test
- Install missing dependencies

### **Phase 2: Core Testing** (3-5 days)
- Create refactored module unit tests
- Add integration test suite
- Implement error handling tests

### **Phase 3: Performance & Quality** (5-7 days)
- Add performance tests
- Create test automation pipeline
- Implement coverage reporting

### **Phase 4: Documentation & Maintenance** (2-3 days)
- Document testing procedures
- Create testing guidelines
- Set up continuous monitoring

## Expected Outcomes

### **Immediate Benefits**
- 100% test suite pass rate
- Comprehensive error handling validation
- Improved code confidence

### **Long-term Benefits**
- Automated quality assurance
- Faster development cycles
- Reduced regression risk
- Better maintainability

## Conclusion

These recommendations will establish a robust testing framework for the refactored MCP Crawl4AI RAG server, ensuring high quality, reliability, and maintainability. The systematic approach addresses both immediate issues and long-term testing needs.