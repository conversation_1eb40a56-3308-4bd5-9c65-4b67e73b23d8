# Complete Setup Guide - MCP Crawl4AI RAG Server

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Environment Configuration](#environment-configuration)
3. [Installation Methods](#installation-methods)
4. [Database Setup](#database-setup)
5. [Server Configuration](#server-configuration)
6. [Deployment Options](#deployment-options)
7. [Verification and Testing](#verification-and-testing)
8. [Production Deployment](#production-deployment)
9. [Maintenance and Updates](#maintenance-and-updates)

## Prerequisites

### System Requirements
- **Operating System**: Linux, macOS, or Windows with WSL2
- **Python**: 3.12 or higher (for local development)
- **Docker**: 20.10+ and Docker Compose v2 (for containerized deployment)
- **Memory**: Minimum 4GB RAM, recommended 8GB+
- **Storage**: 10GB free space for dependencies and cache
- **Network**: Outbound internet access for API calls and crawling

### Required Accounts and API Keys
- **OpenAI Account**: API key for embeddings and LLM processing
- **Supabase Account**: Database URL and service key for vector storage
- **Neo4j Instance** (optional): For knowledge graph features

### Development Tools
- **uv**: Fast Python package installer (`pip install uv`)
- **Git**: Version control system
- **curl**: For testing HTTP endpoints
- **jq**: JSON processing tool (optional but recommended)

## Environment Configuration

### 1. Create Environment File

```bash
# Copy the example environment file
cp .env.example .env
```

### 2. Configure Required Variables

Edit your `.env` file with the following required settings:

```bash
# ===========================================
# CORE CONFIGURATION (REQUIRED)
# ===========================================

# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here
MODEL_CHOICE=gpt-4o-mini

# Supabase Database Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_KEY=your_supabase_service_key_here

# Server Configuration
HOST=0.0.0.0
PORT=8051
TRANSPORT=sse
```

### 3. Configure Optional Enhancements

```bash
# ===========================================
# RAG ENHANCEMENT FLAGS (OPTIONAL)
# ===========================================

# Basic RAG improvements (recommended)
USE_HYBRID_SEARCH=true
USE_RERANKING=true

# Advanced features (higher resource usage)
USE_CONTEXTUAL_EMBEDDINGS=false
USE_AGENTIC_RAG=false
USE_KNOWLEDGE_GRAPH=false

# ===========================================
# EMBEDDING CONFIGURATION (OPTIONAL)
# ===========================================

# Choose embedding strategy: "openai" or "ollama"
EMBEDDING_STRATEGY=openai

# OpenAI Embeddings (default)
OPENAI_EMBEDDING_MODEL=text-embedding-3-small

# Ollama Embeddings (alternative)
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_EMBEDDING_MODEL=nomic-embed-text

# ===========================================
# KNOWLEDGE GRAPH (OPTIONAL)
# ===========================================

# Neo4j Configuration (only if USE_KNOWLEDGE_GRAPH=true)
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=your_neo4j_password
```

### 4. Environment Variable Reference

| Variable | Purpose | Default | Required |
|----------|---------|---------|----------|
| `OPENAI_API_KEY` | OpenAI API access | - | ✅ |
| `SUPABASE_URL` | Supabase project URL | - | ✅ |
| `SUPABASE_SERVICE_KEY` | Supabase service key | - | ✅ |
| `HOST` | Server bind address | `0.0.0.0` | ✅ |
| `PORT` | Server port | `8051` | ✅ |
| `TRANSPORT` | Protocol (sse/stdio) | `sse` | ✅ |
| `MODEL_CHOICE` | LLM model | `gpt-4o-mini` | ✅ |
| `USE_HYBRID_SEARCH` | Enable hybrid search | `false` | ❌ |
| `USE_RERANKING` | Enable result reranking | `false` | ❌ |
| `USE_CONTEXTUAL_EMBEDDINGS` | Enhanced embeddings | `false` | ❌ |
| `USE_AGENTIC_RAG` | Code example extraction | `false` | ❌ |
| `USE_KNOWLEDGE_GRAPH` | Neo4j integration | `false` | ❌ |

## Installation Methods

### Method 1: Docker Compose (Recommended)

This method provides the easiest setup with all dependencies managed automatically.

#### Step 1: Clone Repository
```bash
git clone https://github.com/your-org/mcp-crawl4ai-rag.git
cd mcp-crawl4ai-rag
```

#### Step 2: Build and Start Services
```bash
# Build and start in development mode
docker-compose up --build -d

# View logs
docker-compose logs -f crawl4ai-rag-mcp

# Check status
docker-compose ps
```

#### Step 3: Verify Installation
```bash
# Test server health
curl -f http://localhost:8051/health

# Test SSE endpoint
curl -H "Accept: text/event-stream" http://localhost:8051/sse
```

### Method 2: Local Development Setup

For development and customization, install dependencies locally.

#### Step 1: Install Dependencies
```bash
# Install Python dependencies
uv pip install -e .

# Setup Crawl4AI with browser dependencies
uv run crawl4ai-setup

# If Playwright installation fails, run manually
python -m playwright install
python -m playwright install-deps
```

#### Step 2: Start Server
```bash
# SSE transport (for web clients)
TRANSPORT=sse uv run src/main.py

# stdio transport (for direct MCP clients)
TRANSPORT=stdio uv run src/main.py
```

#### Step 3: Verify Installation
```bash
# Test server (SSE mode)
curl -f http://localhost:8051/health

# Test tools availability
curl -X POST http://localhost:8051/tools/list \
  -H "Content-Type: application/json"
```

### Method 3: Production Docker Build

For production deployment with custom configurations.

#### Step 1: Build Production Image
```bash
# Build image with specific port
docker build -t crawl4ai-rag-mcp:prod --build-arg PORT=8051 .

# Or build with GPU support
docker build -f Dockerfile.gpu -t crawl4ai-rag-mcp:gpu .
```

#### Step 2: Run Container
```bash
# Run with environment file
docker run -d \
  --name crawl4ai-rag-mcp \
  --env-file .env \
  -p 8051:8051 \
  crawl4ai-rag-mcp:prod

# Run with inline environment
docker run -d \
  --name crawl4ai-rag-mcp \
  -e OPENAI_API_KEY=your_key \
  -e SUPABASE_URL=your_url \
  -e SUPABASE_SERVICE_KEY=your_key \
  -p 8051:8051 \
  crawl4ai-rag-mcp:prod
```

## Database Setup

### 1. Supabase Configuration

#### Create Supabase Project
1. Go to [Supabase Dashboard](https://app.supabase.com)
2. Create a new project
3. Note down the project URL and service key

#### Configure Database Schema
1. Open Supabase SQL Editor
2. Execute the schema from `crawled_pages.sql`:

```sql
-- Enable pgvector extension
CREATE EXTENSION IF NOT EXISTS vector;

-- Create sources table
CREATE TABLE IF NOT EXISTS sources (
    id TEXT PRIMARY KEY,
    url TEXT NOT NULL,
    summary TEXT,
    word_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create crawled_pages table
CREATE TABLE IF NOT EXISTS crawled_pages (
    id SERIAL PRIMARY KEY,
    source_id TEXT REFERENCES sources(id) ON DELETE CASCADE,
    url TEXT NOT NULL,
    title TEXT,
    content TEXT NOT NULL,
    chunk_index INTEGER DEFAULT 0,
    embedding VECTOR(1536),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create code_examples table (for agentic RAG)
CREATE TABLE IF NOT EXISTS code_examples (
    id SERIAL PRIMARY KEY,
    source_id TEXT REFERENCES sources(id) ON DELETE CASCADE,
    url TEXT NOT NULL,
    title TEXT,
    content TEXT NOT NULL,
    summary TEXT,
    language TEXT,
    embedding VECTOR(1536),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_crawled_pages_source_id ON crawled_pages(source_id);
CREATE INDEX IF NOT EXISTS idx_crawled_pages_embedding ON crawled_pages USING ivfflat (embedding vector_cosine_ops);
CREATE INDEX IF NOT EXISTS idx_code_examples_source_id ON code_examples(source_id);
CREATE INDEX IF NOT EXISTS idx_code_examples_embedding ON code_examples USING ivfflat (embedding vector_cosine_ops);
CREATE INDEX IF NOT EXISTS idx_code_examples_language ON code_examples(language);

-- Create RPC functions for hybrid search
CREATE OR REPLACE FUNCTION match_pages_hybrid(
    query_embedding VECTOR(1536),
    search_query TEXT,
    source_filter TEXT DEFAULT NULL,
    match_threshold FLOAT DEFAULT 0.5,
    match_count INT DEFAULT 10
)
RETURNS TABLE (
    id INT,
    source_id TEXT,
    url TEXT,
    title TEXT,
    content TEXT,
    similarity FLOAT,
    metadata JSONB
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        cp.id,
        cp.source_id,
        cp.url,
        cp.title,
        cp.content,
        1 - (cp.embedding <=> query_embedding) AS similarity,
        cp.metadata
    FROM crawled_pages cp
    WHERE 
        (source_filter IS NULL OR cp.source_id = source_filter)
        AND (
            (cp.embedding <=> query_embedding) < (1 - match_threshold)
            OR (
                search_query IS NOT NULL 
                AND (
                    cp.content ILIKE '%' || search_query || '%'
                    OR cp.title ILIKE '%' || search_query || '%'
                )
            )
        )
    ORDER BY similarity DESC
    LIMIT match_count;
END;
$$;
```

### 2. Neo4j Setup (Optional)

Only required if `USE_KNOWLEDGE_GRAPH=true`.

#### Install Neo4j
```bash
# Docker installation
docker run -d \
    --name neo4j \
    -p 7474:7474 -p 7687:7687 \
    -e NEO4J_AUTH=neo4j/your_password \
    neo4j:latest

# Or use Neo4j Desktop or AuraDB
```

#### Configure Neo4j Connection
Update `.env` with Neo4j credentials:
```bash
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=your_password
```

## Server Configuration

### 1. Transport Configuration

#### SSE Transport (Web Clients)
```bash
TRANSPORT=sse
HOST=0.0.0.0
PORT=8051
```

Best for:
- Claude Desktop official integration
- Claude Code remote connections
- Web-based MCP clients

#### stdio Transport (Direct Clients)
```bash
TRANSPORT=stdio
```

Best for:
- Local MCP client development
- Direct command-line integration
- Custom MCP implementations

### 2. Performance Tuning

#### Memory Configuration
```bash
# Adjust based on available system memory
MAX_MEMORY_MB=4096
CACHE_SIZE_MB=1024
MAX_CONCURRENT_CRAWLS=3
```

#### Database Optimization
```bash
# Connection pooling
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30
```

#### Crawling Configuration
```bash
# Browser settings
BROWSER_TIMEOUT=30
BROWSER_MEMORY_LIMIT=512
ENABLE_BROWSER_CACHE=true
```

## Deployment Options

### Development Deployment

Perfect for testing and development:

```bash
# Start development server
docker-compose up -d

# View logs in real-time
docker-compose logs -f

# Access server
curl http://localhost:8051/health
```

### Production Deployment

#### Option 1: Docker Compose Production
```bash
# Use production configuration
docker-compose -f docker-compose.prod.yml up -d

# Enable restart policies
docker-compose -f docker-compose.prod.yml up -d --restart=unless-stopped
```

#### Option 2: Kubernetes Deployment
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: crawl4ai-rag-mcp
spec:
  replicas: 3
  selector:
    matchLabels:
      app: crawl4ai-rag-mcp
  template:
    metadata:
      labels:
        app: crawl4ai-rag-mcp
    spec:
      containers:
      - name: crawl4ai-rag-mcp
        image: crawl4ai-rag-mcp:prod
        ports:
        - containerPort: 8051
        env:
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: crawl4ai-secrets
              key: openai-api-key
        - name: SUPABASE_URL
          valueFrom:
            secretKeyRef:
              name: crawl4ai-secrets
              key: supabase-url
        - name: SUPABASE_SERVICE_KEY
          valueFrom:
            secretKeyRef:
              name: crawl4ai-secrets
              key: supabase-service-key
        resources:
          requests:
            memory: "2Gi"
            cpu: "500m"
          limits:
            memory: "4Gi"
            cpu: "1000m"
```

#### Option 3: Cloud Deployment

##### AWS ECS
```bash
# Build and push to ECR
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin your-account.dkr.ecr.us-east-1.amazonaws.com
docker build -t crawl4ai-rag-mcp:prod .
docker tag crawl4ai-rag-mcp:prod your-account.dkr.ecr.us-east-1.amazonaws.com/crawl4ai-rag-mcp:latest
docker push your-account.dkr.ecr.us-east-1.amazonaws.com/crawl4ai-rag-mcp:latest

# Deploy with ECS task definition
aws ecs create-task-definition --cli-input-json file://task-definition.json
```

##### Google Cloud Run
```bash
# Build and push to GCR
gcloud builds submit --tag gcr.io/your-project/crawl4ai-rag-mcp

# Deploy to Cloud Run
gcloud run deploy crawl4ai-rag-mcp \
  --image gcr.io/your-project/crawl4ai-rag-mcp \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --port 8051 \
  --memory 4Gi \
  --cpu 2
```

### HTTPS and SSL Configuration

#### nginx Reverse Proxy
```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /etc/ssl/certs/your-domain.crt;
    ssl_certificate_key /etc/ssl/private/your-domain.key;
    
    location / {
        proxy_pass http://localhost:8051;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # SSE specific headers
        proxy_buffering off;
        proxy_cache off;
        proxy_set_header Connection '';
        proxy_http_version 1.1;
        chunked_transfer_encoding off;
    }
}
```

#### Let's Encrypt SSL
```bash
# Install certbot
sudo apt install certbot python3-certbot-nginx

# Get SSL certificate
sudo certbot --nginx -d your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

## Verification and Testing

### 1. Basic Health Checks

```bash
# Server health
curl -f http://localhost:8051/health

# Expected response:
# {"status": "healthy", "timestamp": "2025-01-17T10:00:00Z"}

# SSE endpoint
curl -H "Accept: text/event-stream" http://localhost:8051/sse

# Expected: SSE connection established
```

### 2. MCP Tools Testing

```bash
# List available tools
curl -X POST http://localhost:8051/tools/list \
  -H "Content-Type: application/json"

# Test crawling
curl -X POST http://localhost:8051/tools/crawl_single_page \
  -H "Content-Type: application/json" \
  -d '{"url": "https://example.com"}'

# Test search
curl -X POST http://localhost:8051/tools/perform_rag_query \
  -H "Content-Type: application/json" \
  -d '{"query": "test search", "count": 5}'
```

### 3. Integration Testing

```bash
# Test with Claude Code
claude mcp add --transport sse crawl4ai-rag http://localhost:8051/sse
claude mcp test crawl4ai-rag

# Test basic functionality
claude "Use crawl4ai-rag to get available sources"
```

### 4. Load Testing

```bash
# Install wrk
sudo apt install wrk

# Load test health endpoint
wrk -t4 -c100 -d30s http://localhost:8051/health

# Load test with real queries
wrk -t2 -c10 -d30s -s post.lua http://localhost:8051/tools/perform_rag_query
```

## Production Deployment

### 1. Production Checklist

#### Environment Configuration
- [ ] All API keys configured and valid
- [ ] Database connection tested and optimized
- [ ] SSL certificates installed and configured
- [ ] Firewall rules configured
- [ ] Monitoring and logging enabled
- [ ] Backup procedures established

#### Security Configuration
- [ ] HTTPS enabled with valid certificates
- [ ] Rate limiting configured
- [ ] Input validation enabled
- [ ] Security headers configured
- [ ] Container security hardened
- [ ] Regular security updates scheduled

#### Performance Configuration
- [ ] Resource limits configured
- [ ] Caching enabled and optimized
- [ ] Database indexes created
- [ ] Connection pooling configured
- [ ] Memory limits set appropriately
- [ ] Concurrent processing limits set

### 2. Production Environment Variables

```bash
# Production optimized configuration
NODE_ENV=production
DEBUG=false
LOG_LEVEL=info

# Security
SECURE_COOKIES=true
HELMET_ENABLED=true
RATE_LIMIT_ENABLED=true
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW=3600

# Performance
CACHE_ENABLED=true
CACHE_TTL=3600
MAX_CONCURRENT_REQUESTS=50
REQUEST_TIMEOUT=30
DB_POOL_SIZE=20

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090
HEALTH_CHECK_INTERVAL=30
```

### 3. Monitoring and Alerting

#### Prometheus Metrics
```yaml
# prometheus.yml
scrape_configs:
  - job_name: 'crawl4ai-rag-mcp'
    static_configs:
      - targets: ['localhost:9090']
    metrics_path: '/metrics'
    scrape_interval: 30s
```

#### Grafana Dashboard
```json
{
  "dashboard": {
    "title": "Crawl4AI RAG MCP Server",
    "panels": [
      {
        "title": "Request Rate",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])"
          }
        ]
      },
      {
        "title": "Response Time",
        "targets": [
          {
            "expr": "http_request_duration_seconds"
          }
        ]
      },
      {
        "title": "Error Rate",
        "targets": [
          {
            "expr": "rate(http_requests_total{status=~\"5..\"}[5m])"
          }
        ]
      }
    ]
  }
}
```

#### Log Aggregation
```yaml
# docker-compose.logging.yml
version: '3.8'
services:
  crawl4ai-rag-mcp:
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        
  # Add ELK stack for centralized logging
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.5.0
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
    ports:
      - "9200:9200"
      
  logstash:
    image: docker.elastic.co/logstash/logstash:8.5.0
    volumes:
      - ./logstash.conf:/usr/share/logstash/pipeline/logstash.conf
    depends_on:
      - elasticsearch
      
  kibana:
    image: docker.elastic.co/kibana/kibana:8.5.0
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
```

### 4. Backup and Recovery

#### Database Backups
```bash
#!/bin/bash
# backup-db.sh
BACKUP_DIR="/backups/$(date +%Y%m%d)"
mkdir -p $BACKUP_DIR

# Backup Supabase data
pg_dump $SUPABASE_URL > $BACKUP_DIR/supabase_backup.sql

# Backup Neo4j data (if used)
docker exec neo4j neo4j-admin backup --to=$BACKUP_DIR/neo4j_backup

# Compress backups
tar -czf $BACKUP_DIR.tar.gz $BACKUP_DIR
rm -rf $BACKUP_DIR

# Upload to S3 (optional)
aws s3 cp $BACKUP_DIR.tar.gz s3://your-backup-bucket/
```

#### Configuration Backups
```bash
#!/bin/bash
# backup-config.sh
CONFIG_BACKUP_DIR="/config-backups/$(date +%Y%m%d)"
mkdir -p $CONFIG_BACKUP_DIR

# Backup environment files
cp .env $CONFIG_BACKUP_DIR/
cp docker-compose.prod.yml $CONFIG_BACKUP_DIR/
cp nginx.conf $CONFIG_BACKUP_DIR/

# Backup SSL certificates
cp -r /etc/ssl/certs/your-domain.* $CONFIG_BACKUP_DIR/

# Compress and store
tar -czf $CONFIG_BACKUP_DIR.tar.gz $CONFIG_BACKUP_DIR
```

## Maintenance and Updates

### 1. Regular Maintenance Tasks

#### Daily Tasks
```bash
# Check system health
docker-compose ps
curl -f http://localhost:8051/health

# Check disk usage
df -h
docker system df

# Check logs for errors
docker-compose logs --tail=100 | grep -i error
```

#### Weekly Tasks
```bash
# Update dependencies
docker-compose pull
docker-compose up -d

# Clean up unused resources
docker system prune -f
docker volume prune -f

# Backup database
./backup-db.sh
```

#### Monthly Tasks
```bash
# Security updates
apt update && apt upgrade -y
docker pull crawl4ai-rag-mcp:latest

# Certificate renewal
certbot renew --quiet

# Performance review
# Check metrics and optimize if needed
```

### 2. Update Procedures

#### Application Updates
```bash
# 1. Backup current state
./backup-config.sh
./backup-db.sh

# 2. Stop services
docker-compose -f docker-compose.prod.yml down

# 3. Pull latest code
git pull origin main

# 4. Update dependencies
docker-compose -f docker-compose.prod.yml pull

# 5. Start services
docker-compose -f docker-compose.prod.yml up -d

# 6. Verify deployment
curl -f http://localhost:8051/health
```

#### Rolling Updates (Zero Downtime)
```bash
# 1. Update one instance at a time
docker-compose -f docker-compose.prod.yml up -d --scale crawl4ai-rag-mcp=2

# 2. Update load balancer to remove old instance
# 3. Stop old instance
# 4. Start new instance
# 5. Update load balancer to include new instance
```

### 3. Troubleshooting Common Issues

#### Container Won't Start
```bash
# Check logs
docker-compose logs crawl4ai-rag-mcp

# Check resource usage
docker stats

# Check port conflicts
netstat -tlnp | grep 8051
```

#### Database Connection Issues
```bash
# Test database connection
docker exec crawl4ai-rag-mcp python -c "
import os
from supabase import create_client
client = create_client(os.getenv('SUPABASE_URL'), os.getenv('SUPABASE_SERVICE_KEY'))
print('Database connection successful')
"
```

#### Performance Issues
```bash
# Monitor resource usage
docker stats crawl4ai-rag-mcp

# Check database performance
# Connect to Supabase and run:
# SELECT * FROM pg_stat_activity;
# SELECT * FROM pg_stat_user_tables;
```

#### SSL Certificate Issues
```bash
# Check certificate validity
openssl x509 -in /etc/ssl/certs/your-domain.crt -text -noout

# Renew certificates
certbot renew --force-renewal
```

---

**Setup Guide Version**: 2.0  
**Last Updated**: 2025-01-17  
**Compatible Server Version**: 1.0+  
**Next Review**: 2025-02-17  

For troubleshooting specific issues, please refer to the [Troubleshooting Guide](TROUBLESHOOTING_GUIDE.md).  
For integration with MCP clients, see the [Integration Guide](INTEGRATION_GUIDE.md).  
For security implementation, consult the [Security Guide](SECURITY_GUIDE.md).