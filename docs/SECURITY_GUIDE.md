# Security Guide - MCP Crawl4AI RAG Server

## Table of Contents

1. [Security Overview](#security-overview)
2. [Authentication and Authorization](#authentication-and-authorization)
3. [Network Security](#network-security)
4. [Input Validation and Sanitization](#input-validation-and-sanitization)
5. [Container Security](#container-security)
6. [Data Protection](#data-protection)
7. [Monitoring and Auditing](#monitoring-and-auditing)
8. [Incident Response](#incident-response)
9. [Security Best Practices](#security-best-practices)
10. [Compliance and Governance](#compliance-and-governance)

## Security Overview

The MCP Crawl4AI RAG server implements comprehensive security measures to protect against common vulnerabilities and ensure secure operation in production environments. This guide covers all security aspects from deployment to ongoing maintenance.

### Security Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   MCP Clients   │    │  Security Layer  │    │  Core Services  │
│                 │    │                  │    │                 │
│ • Authentication│◄──►│ • Input Validation│◄──►│ • Web Crawling  │
│ • Authorization │    │ • Rate Limiting  │    │ • Vector Search │
│ • Encryption    │    │ • CORS Policies  │    │ • Database Ops  │
└─────────────────┘    │ • Security Headers│    │ • API Calls     │
                       └──────────────────┘    └─────────────────┘
```

### Security Principles

1. **Defense in Depth**: Multiple layers of security controls
2. **Least Privilege**: Minimal permissions for all components
3. **Zero Trust**: Verify everything, trust nothing
4. **Secure by Default**: Security enabled out of the box
5. **Continuous Monitoring**: Real-time security monitoring
6. **Incident Response**: Rapid response to security events

## Authentication and Authorization

### API Key Management

#### Environment-Based Authentication
```bash
# Primary API keys (required)
OPENAI_API_KEY=sk-your-openai-key
SUPABASE_SERVICE_KEY=your-supabase-service-key

# Custom authentication (optional)
MCP_API_KEY=your-custom-mcp-api-key
JWT_SECRET=your-jwt-secret-key
```

#### API Key Rotation
```bash
#!/bin/bash
# rotate-api-keys.sh
# Automated key rotation script

# Generate new API key
NEW_API_KEY=$(openssl rand -hex 32)

# Update environment
sed -i "s/MCP_API_KEY=.*/MCP_API_KEY=$NEW_API_KEY/" .env

# Restart services
docker-compose restart crawl4ai-rag-mcp

# Notify monitoring systems
curl -X POST "$MONITORING_WEBHOOK" \
  -H "Content-Type: application/json" \
  -d '{"event": "api_key_rotation", "timestamp": "'$(date -u +%Y-%m-%dT%H:%M:%SZ)'"}'
```

### JWT Authentication Implementation

#### Token Generation
```python
import jwt
import datetime
from flask import request, jsonify

def generate_token(user_id, permissions=None):
    payload = {
        'user_id': user_id,
        'permissions': permissions or ['read', 'write'],
        'exp': datetime.datetime.utcnow() + datetime.timedelta(hours=24),
        'iat': datetime.datetime.utcnow(),
        'iss': 'crawl4ai-rag-mcp'
    }
    return jwt.encode(payload, current_app.config['JWT_SECRET'], algorithm='HS256')

def verify_token(token):
    try:
        payload = jwt.decode(token, current_app.config['JWT_SECRET'], algorithms=['HS256'])
        return payload
    except jwt.ExpiredSignatureError:
        return None
    except jwt.InvalidTokenError:
        return None
```

#### Authentication Middleware
```python
from functools import wraps

def require_auth(permissions=None):
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            token = request.headers.get('Authorization')
            if not token:
                return jsonify({'error': 'Authentication required'}), 401
            
            if token.startswith('Bearer '):
                token = token[7:]
            
            payload = verify_token(token)
            if not payload:
                return jsonify({'error': 'Invalid or expired token'}), 401
            
            # Check permissions
            if permissions:
                user_permissions = payload.get('permissions', [])
                if not any(perm in user_permissions for perm in permissions):
                    return jsonify({'error': 'Insufficient permissions'}), 403
            
            request.current_user = payload
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator

# Usage example
@app.route('/tools/crawl_single_page', methods=['POST'])
@require_auth(['write'])
def crawl_single_page():
    # Tool implementation
    pass
```

### Role-Based Access Control (RBAC)

#### Role Definitions
```python
ROLES = {
    'admin': {
        'permissions': ['read', 'write', 'delete', 'admin'],
        'description': 'Full system access'
    },
    'user': {
        'permissions': ['read', 'write'],
        'description': 'Standard user access'
    },
    'readonly': {
        'permissions': ['read'],
        'description': 'Read-only access'
    }
}

def check_permission(user_role, required_permission):
    return required_permission in ROLES.get(user_role, {}).get('permissions', [])
```

## Network Security

### HTTPS Configuration

#### SSL/TLS Certificate Setup
```bash
# Generate self-signed certificate (development only)
openssl req -x509 -newkey rsa:4096 -keyout key.pem -out cert.pem -days 365 -nodes

# Production certificate with Let's Encrypt
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com

# Certificate renewal automation
echo "0 12 * * * /usr/bin/certbot renew --quiet" | sudo crontab -e
```

#### nginx SSL Configuration
```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
    
    # Security headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self'; style-src 'self' 'unsafe-inline';" always;
    
    # SSL Security
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 1d;
    ssl_session_tickets off;
    
    # OCSP Stapling
    ssl_stapling on;
    ssl_stapling_verify on;
    
    location / {
        proxy_pass http://localhost:8051;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # SSE specific headers
        proxy_buffering off;
        proxy_cache off;
        proxy_set_header Connection '';
        proxy_http_version 1.1;
        chunked_transfer_encoding off;
    }
}

# Redirect HTTP to HTTPS
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}
```

### Firewall Configuration

#### UFW (Ubuntu Firewall)
```bash
# Enable firewall
sudo ufw enable

# Allow SSH
sudo ufw allow ssh

# Allow HTTP/HTTPS
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# Allow specific IP for admin access
sudo ufw allow from ************* to any port 22

# Block direct access to application port
sudo ufw deny 8051/tcp

# Check status
sudo ufw status verbose
```

#### iptables Rules
```bash
#!/bin/bash
# firewall-rules.sh
# Production firewall configuration

# Flush existing rules
iptables -F
iptables -X
iptables -t nat -F
iptables -t nat -X

# Default policies
iptables -P INPUT DROP
iptables -P FORWARD DROP
iptables -P OUTPUT ACCEPT

# Allow loopback
iptables -A INPUT -i lo -j ACCEPT
iptables -A OUTPUT -o lo -j ACCEPT

# Allow established connections
iptables -A INPUT -m state --state ESTABLISHED,RELATED -j ACCEPT

# Allow SSH from specific IPs
iptables -A INPUT -p tcp --dport 22 -s ***********/24 -j ACCEPT

# Allow HTTP/HTTPS
iptables -A INPUT -p tcp --dport 80 -j ACCEPT
iptables -A INPUT -p tcp --dport 443 -j ACCEPT

# Rate limiting for HTTP
iptables -A INPUT -p tcp --dport 80 -m limit --limit 25/minute --limit-burst 100 -j ACCEPT
iptables -A INPUT -p tcp --dport 443 -m limit --limit 25/minute --limit-burst 100 -j ACCEPT

# Block direct access to application port
iptables -A INPUT -p tcp --dport 8051 -j DROP

# Save rules
iptables-save > /etc/iptables/rules.v4
```

### Rate Limiting

#### nginx Rate Limiting
```nginx
http {
    # Rate limiting zones
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/m;
    limit_req_zone $binary_remote_addr zone=general:10m rate=1r/s;
    
    server {
        location /tools/ {
            limit_req zone=api burst=5 nodelay;
            limit_req_status 429;
            # Tool endpoints
        }
        
        location / {
            limit_req zone=general burst=10 nodelay;
            # General endpoints
        }
    }
}
```

#### Application-Level Rate Limiting
```python
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address

limiter = Limiter(
    app,
    key_func=get_remote_address,
    default_limits=["200 per day", "50 per hour"]
)

@app.route('/tools/crawl_single_page', methods=['POST'])
@limiter.limit("5 per minute")
def crawl_single_page():
    # Tool implementation
    pass

@app.route('/tools/perform_rag_query', methods=['POST'])
@limiter.limit("20 per minute")
def perform_rag_query():
    # Tool implementation
    pass
```

## Input Validation and Sanitization

### URL Validation

#### Safe URL Validation
```python
import re
from urllib.parse import urlparse, urljoin
from typing import List, Optional

class URLValidator:
    # Allowed protocols
    ALLOWED_SCHEMES = ['http', 'https']
    
    # Blocked domains (internal networks, etc.)
    BLOCKED_DOMAINS = [
        'localhost',
        '127.0.0.1',
        '0.0.0.0',
        '::1',
        '***********/16',  # Link-local
        '10.0.0.0/8',      # Private
        '**********/12',   # Private
        '***********/16',  # Private
    ]
    
    def __init__(self, allowed_domains: Optional[List[str]] = None):
        self.allowed_domains = allowed_domains or []
    
    def validate_url(self, url: str) -> bool:
        """Validate URL for security and safety"""
        try:
            parsed = urlparse(url)
            
            # Check scheme
            if parsed.scheme not in self.ALLOWED_SCHEMES:
                return False
            
            # Check domain restrictions
            if self.allowed_domains and parsed.netloc not in self.allowed_domains:
                return False
            
            # Check for blocked domains
            if any(self._is_blocked_domain(parsed.netloc, blocked) 
                   for blocked in self.BLOCKED_DOMAINS):
                return False
            
            # Check for URL length
            if len(url) > 2048:
                return False
            
            return True
            
        except Exception:
            return False
    
    def _is_blocked_domain(self, domain: str, blocked: str) -> bool:
        """Check if domain matches blocked pattern"""
        if '/' in blocked:  # CIDR notation
            # IP range checking logic
            return self._is_ip_in_range(domain, blocked)
        return domain == blocked or domain.endswith('.' + blocked)
    
    def _is_ip_in_range(self, ip: str, cidr: str) -> bool:
        """Check if IP is in CIDR range"""
        import ipaddress
        try:
            return ipaddress.ip_address(ip) in ipaddress.ip_network(cidr, strict=False)
        except ValueError:
            return False

# Usage
validator = URLValidator(allowed_domains=['example.com', 'docs.example.com'])
if validator.validate_url(user_input_url):
    # Process URL
    pass
```

### Query Sanitization

#### Neo4j Query Sanitization
```python
import re
from typing import Dict, Any

class CypherQuerySanitizer:
    # Dangerous keywords that should be blocked
    DANGEROUS_KEYWORDS = [
        'CREATE', 'DELETE', 'REMOVE', 'SET', 'MERGE',
        'DROP', 'DETACH', 'CALL', 'LOAD', 'USING',
        'PERIODIC', 'COMMIT', 'FOREACH'
    ]
    
    def sanitize_query(self, query: str) -> str:
        """Sanitize Cypher query for read-only operations"""
        # Convert to uppercase for keyword checking
        query_upper = query.upper()
        
        # Block dangerous keywords
        for keyword in self.DANGEROUS_KEYWORDS:
            if keyword in query_upper:
                raise ValueError(f"Query contains dangerous keyword: {keyword}")
        
        # Ensure query starts with MATCH or RETURN
        if not query_upper.strip().startswith(('MATCH', 'RETURN', 'WITH')):
            raise ValueError("Query must start with MATCH, RETURN, or WITH")
        
        # Limit query length
        if len(query) > 1000:
            raise ValueError("Query too long")
        
        return query
    
    def sanitize_parameters(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Sanitize query parameters"""
        sanitized = {}
        
        for key, value in params.items():
            # Validate parameter names
            if not re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', key):
                raise ValueError(f"Invalid parameter name: {key}")
            
            # Sanitize string values
            if isinstance(value, str):
                if len(value) > 1000:
                    raise ValueError(f"Parameter value too long: {key}")
                # Remove or escape dangerous characters
                value = re.sub(r'[<>"\']', '', value)
            
            sanitized[key] = value
        
        return sanitized
```

### Input Length Limits

```python
class InputValidator:
    LIMITS = {
        'url': 2048,
        'query': 1000,
        'source_id': 100,
        'title': 200,
        'content': 100000,
        'metadata': 10000
    }
    
    def validate_input(self, field: str, value: str) -> bool:
        """Validate input length and content"""
        if field not in self.LIMITS:
            return False
        
        if len(value) > self.LIMITS[field]:
            return False
        
        # Additional validation based on field type
        if field == 'url':
            return self._validate_url(value)
        elif field == 'query':
            return self._validate_query(value)
        
        return True
    
    def _validate_url(self, url: str) -> bool:
        """URL-specific validation"""
        return URLValidator().validate_url(url)
    
    def _validate_query(self, query: str) -> bool:
        """Query-specific validation"""
        # Check for SQL injection patterns
        dangerous_patterns = [
            r';.*--',
            r'union.*select',
            r'drop.*table',
            r'exec.*sp_',
            r'xp_cmdshell'
        ]
        
        query_lower = query.lower()
        return not any(re.search(pattern, query_lower) for pattern in dangerous_patterns)
```

## Container Security

### Docker Security Configuration

#### Dockerfile Security Best Practices
```dockerfile
# Use specific version tags
FROM python:3.12-slim-bullseye

# Create non-root user
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Set working directory
WORKDIR /app

# Install security updates
RUN apt-get update && apt-get upgrade -y && \
    apt-get install -y --no-install-recommends curl && \
    apt-get clean && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY --chown=appuser:appuser . .

# Remove unnecessary files
RUN rm -rf tests/ docs/ .git/

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 8051

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8051/health || exit 1

# Start application
CMD ["python", "src/main.py"]
```

#### Docker Compose Security
```yaml
version: '3.8'
services:
  crawl4ai-rag-mcp:
    build: .
    ports:
      - "127.0.0.1:8051:8051"  # Bind to localhost only
    environment:
      - TRANSPORT=sse
      - HOST=0.0.0.0
      - PORT=8051
    env_file:
      - .env
    volumes:
      - crawl4ai_cache:/app/.crawl4ai_cache:rw
      - /etc/passwd:/etc/passwd:ro
      - /etc/group:/etc/group:ro
    restart: unless-stopped
    
    # Security options
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    cap_add:
      - NET_BIND_SERVICE
    
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    
    # Health check
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8051/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # Logging
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

volumes:
  crawl4ai_cache:
    driver: local
```

### Runtime Security

#### AppArmor Profile
```bash
# /etc/apparmor.d/docker-crawl4ai
#include <tunables/global>

profile docker-crawl4ai flags=(attach_disconnected,mediate_deleted) {
  #include <abstractions/base>
  
  # Network access
  network inet tcp,
  network inet udp,
  network inet6 tcp,
  network inet6 udp,
  
  # File system access
  /app/** r,
  /app/.crawl4ai_cache/** rw,
  /tmp/** rw,
  /var/tmp/** rw,
  
  # Deny dangerous capabilities
  deny capability sys_admin,
  deny capability sys_ptrace,
  deny capability sys_module,
  deny capability sys_rawio,
  
  # Allow necessary capabilities
  capability net_bind_service,
  capability setuid,
  capability setgid,
}
```

#### Seccomp Profile
```json
{
  "defaultAction": "SCMP_ACT_ERRNO",
  "architectures": ["SCMP_ARCH_X86_64"],
  "syscalls": [
    {
      "name": "accept",
      "action": "SCMP_ACT_ALLOW"
    },
    {
      "name": "accept4",
      "action": "SCMP_ACT_ALLOW"
    },
    {
      "name": "bind",
      "action": "SCMP_ACT_ALLOW"
    },
    {
      "name": "listen",
      "action": "SCMP_ACT_ALLOW"
    },
    {
      "name": "connect",
      "action": "SCMP_ACT_ALLOW"
    },
    {
      "name": "socket",
      "action": "SCMP_ACT_ALLOW"
    },
    {
      "name": "read",
      "action": "SCMP_ACT_ALLOW"
    },
    {
      "name": "write",
      "action": "SCMP_ACT_ALLOW"
    },
    {
      "name": "open",
      "action": "SCMP_ACT_ALLOW"
    },
    {
      "name": "openat",
      "action": "SCMP_ACT_ALLOW"
    },
    {
      "name": "close",
      "action": "SCMP_ACT_ALLOW"
    },
    {
      "name": "stat",
      "action": "SCMP_ACT_ALLOW"
    },
    {
      "name": "fstat",
      "action": "SCMP_ACT_ALLOW"
    },
    {
      "name": "exit",
      "action": "SCMP_ACT_ALLOW"
    },
    {
      "name": "exit_group",
      "action": "SCMP_ACT_ALLOW"
    }
  ]
}
```

## Data Protection

### Encryption

#### Data at Rest
```python
from cryptography.fernet import Fernet
import os

class DataEncryption:
    def __init__(self):
        # Generate or load encryption key
        self.key = self._get_or_generate_key()
        self.cipher = Fernet(self.key)
    
    def _get_or_generate_key(self):
        """Get encryption key from environment or generate new one"""
        key = os.environ.get('ENCRYPTION_KEY')
        if key:
            return key.encode()
        else:
            key = Fernet.generate_key()
            print(f"Generated new encryption key: {key.decode()}")
            return key
    
    def encrypt_data(self, data: str) -> bytes:
        """Encrypt sensitive data"""
        return self.cipher.encrypt(data.encode())
    
    def decrypt_data(self, encrypted_data: bytes) -> str:
        """Decrypt sensitive data"""
        return self.cipher.decrypt(encrypted_data).decode()
    
    def encrypt_file(self, file_path: str, output_path: str):
        """Encrypt file contents"""
        with open(file_path, 'rb') as file:
            data = file.read()
        
        encrypted_data = self.cipher.encrypt(data)
        
        with open(output_path, 'wb') as file:
            file.write(encrypted_data)

# Usage for sensitive configuration
encryption = DataEncryption()
encrypted_api_key = encryption.encrypt_data(os.environ['OPENAI_API_KEY'])
```

#### Data in Transit
```python
import ssl
import aiohttp
from aiohttp import TCPConnector

class SecureHTTPClient:
    def __init__(self):
        # Create SSL context with security settings
        self.ssl_context = ssl.create_default_context()
        self.ssl_context.check_hostname = True
        self.ssl_context.verify_mode = ssl.CERT_REQUIRED
        self.ssl_context.minimum_version = ssl.TLSVersion.TLSv1_2
        
        # Configure cipher suites
        self.ssl_context.set_ciphers('ECDHE+AESGCM:ECDHE+CHACHA20:DHE+AESGCM:DHE+CHACHA20:!aNULL:!MD5:!DSS')
        
    async def make_request(self, url: str, **kwargs):
        """Make secure HTTP request"""
        connector = TCPConnector(ssl=self.ssl_context)
        
        async with aiohttp.ClientSession(connector=connector) as session:
            async with session.get(url, **kwargs) as response:
                return await response.json()
```

### Secrets Management

#### HashiCorp Vault Integration
```python
import hvac
from typing import Dict, Any

class VaultSecretManager:
    def __init__(self, vault_url: str, token: str):
        self.client = hvac.Client(url=vault_url, token=token)
    
    def get_secret(self, path: str) -> Dict[str, Any]:
        """Retrieve secret from Vault"""
        try:
            response = self.client.secrets.kv.v2.read_secret_version(path=path)
            return response['data']['data']
        except Exception as e:
            raise Exception(f"Failed to retrieve secret {path}: {str(e)}")
    
    def set_secret(self, path: str, secret: Dict[str, Any]):
        """Store secret in Vault"""
        try:
            self.client.secrets.kv.v2.create_or_update_secret(
                path=path,
                secret=secret
            )
        except Exception as e:
            raise Exception(f"Failed to store secret {path}: {str(e)}")
    
    def rotate_secret(self, path: str, new_secret: Dict[str, Any]):
        """Rotate existing secret"""
        # Store new secret
        self.set_secret(path, new_secret)
        
        # Invalidate old secret (implementation depends on secret type)
        # This might involve API calls to revoke old keys
        pass

# Usage
vault = VaultSecretManager('https://vault.example.com', os.environ['VAULT_TOKEN'])
secrets = vault.get_secret('secret/crawl4ai-rag')
openai_key = secrets['openai_api_key']
```

#### AWS Secrets Manager
```python
import boto3
import json
from botocore.exceptions import ClientError

class AWSSecretManager:
    def __init__(self, region_name: str = 'us-east-1'):
        self.client = boto3.client('secretsmanager', region_name=region_name)
    
    def get_secret(self, secret_name: str) -> Dict[str, Any]:
        """Retrieve secret from AWS Secrets Manager"""
        try:
            response = self.client.get_secret_value(SecretId=secret_name)
            return json.loads(response['SecretString'])
        except ClientError as e:
            raise Exception(f"Failed to retrieve secret {secret_name}: {str(e)}")
    
    def rotate_secret(self, secret_name: str):
        """Initiate secret rotation"""
        try:
            self.client.rotate_secret(SecretId=secret_name)
        except ClientError as e:
            raise Exception(f"Failed to rotate secret {secret_name}: {str(e)}")

# Usage
aws_secrets = AWSSecretManager()
secrets = aws_secrets.get_secret('prod/crawl4ai-rag/api-keys')
```

## Monitoring and Auditing

### Security Monitoring

#### Log Configuration
```python
import logging
import json
from datetime import datetime
from typing import Dict, Any

class SecurityLogger:
    def __init__(self, log_file: str = '/var/log/crawl4ai-security.log'):
        self.logger = logging.getLogger('security')
        self.logger.setLevel(logging.INFO)
        
        # Create file handler
        handler = logging.FileHandler(log_file)
        handler.setLevel(logging.INFO)
        
        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        
        self.logger.addHandler(handler)
    
    def log_security_event(self, event_type: str, details: Dict[str, Any], 
                          severity: str = 'INFO'):
        """Log security event"""
        event = {
            'timestamp': datetime.utcnow().isoformat(),
            'event_type': event_type,
            'severity': severity,
            'details': details
        }
        
        log_method = getattr(self.logger, severity.lower())
        log_method(json.dumps(event))
    
    def log_authentication_attempt(self, username: str, success: bool, 
                                 ip_address: str):
        """Log authentication attempt"""
        self.log_security_event(
            'authentication_attempt',
            {
                'username': username,
                'success': success,
                'ip_address': ip_address
            },
            'WARNING' if not success else 'INFO'
        )
    
    def log_suspicious_activity(self, activity: str, ip_address: str, 
                              details: Dict[str, Any]):
        """Log suspicious activity"""
        self.log_security_event(
            'suspicious_activity',
            {
                'activity': activity,
                'ip_address': ip_address,
                **details
            },
            'WARNING'
        )

# Usage
security_logger = SecurityLogger()
security_logger.log_authentication_attempt('admin', False, '*************')
```

#### Intrusion Detection
```python
import re
from collections import defaultdict, deque
from datetime import datetime, timedelta
from typing import Dict, List

class IntrusionDetectionSystem:
    def __init__(self):
        self.failed_attempts = defaultdict(deque)
        self.blocked_ips = set()
        self.suspicious_patterns = [
            r'union.*select',
            r'drop.*table',
            r'<script.*>',
            r'javascript:',
            r'eval\(',
            r'exec\(',
        ]
    
    def check_request(self, ip_address: str, user_agent: str, 
                     request_data: str) -> bool:
        """Check request for security threats"""
        if ip_address in self.blocked_ips:
            return False
        
        # Check for suspicious patterns
        if self._contains_suspicious_pattern(request_data):
            self._log_suspicious_activity(ip_address, 'suspicious_pattern', request_data)
            return False
        
        # Check for unusual user agent
        if self._is_suspicious_user_agent(user_agent):
            self._log_suspicious_activity(ip_address, 'suspicious_user_agent', user_agent)
            return False
        
        return True
    
    def record_failed_attempt(self, ip_address: str):
        """Record failed authentication attempt"""
        now = datetime.utcnow()
        self.failed_attempts[ip_address].append(now)
        
        # Remove old attempts (older than 1 hour)
        while (self.failed_attempts[ip_address] and 
               now - self.failed_attempts[ip_address][0] > timedelta(hours=1)):
            self.failed_attempts[ip_address].popleft()
        
        # Block IP if too many failures
        if len(self.failed_attempts[ip_address]) >= 5:
            self.blocked_ips.add(ip_address)
            self._log_suspicious_activity(ip_address, 'brute_force_attempt', 
                                        f'{len(self.failed_attempts[ip_address])} failed attempts')
    
    def _contains_suspicious_pattern(self, data: str) -> bool:
        """Check if data contains suspicious patterns"""
        data_lower = data.lower()
        return any(re.search(pattern, data_lower) for pattern in self.suspicious_patterns)
    
    def _is_suspicious_user_agent(self, user_agent: str) -> bool:
        """Check if user agent is suspicious"""
        suspicious_agents = [
            'sqlmap',
            'nmap',
            'burp',
            'dirb',
            'gobuster',
            'nikto'
        ]
        return any(agent in user_agent.lower() for agent in suspicious_agents)
    
    def _log_suspicious_activity(self, ip_address: str, activity_type: str, 
                               details: str):
        """Log suspicious activity"""
        security_logger.log_suspicious_activity(
            activity_type,
            ip_address,
            {'details': details}
        )

# Usage
ids = IntrusionDetectionSystem()
if ids.check_request('*************', 'Mozilla/5.0...', request_body):
    # Process request
    pass
```

### Audit Logging

#### Audit Trail Implementation
```python
import json
from datetime import datetime
from typing import Any, Dict, Optional

class AuditLogger:
    def __init__(self, audit_file: str = '/var/log/crawl4ai-audit.log'):
        self.audit_file = audit_file
    
    def log_audit_event(self, event_type: str, user_id: str, 
                       resource: str, action: str, 
                       details: Optional[Dict[str, Any]] = None,
                       success: bool = True):
        """Log audit event"""
        event = {
            'timestamp': datetime.utcnow().isoformat(),
            'event_type': event_type,
            'user_id': user_id,
            'resource': resource,
            'action': action,
            'success': success,
            'details': details or {}
        }
        
        with open(self.audit_file, 'a') as f:
            f.write(json.dumps(event) + '\n')
    
    def log_data_access(self, user_id: str, resource: str, 
                       access_type: str, success: bool = True):
        """Log data access event"""
        self.log_audit_event(
            'data_access',
            user_id,
            resource,
            access_type,
            success=success
        )
    
    def log_configuration_change(self, user_id: str, setting: str, 
                               old_value: str, new_value: str):
        """Log configuration change"""
        self.log_audit_event(
            'configuration_change',
            user_id,
            setting,
            'update',
            details={
                'old_value': old_value,
                'new_value': new_value
            }
        )
    
    def log_system_event(self, event_type: str, details: Dict[str, Any]):
        """Log system event"""
        self.log_audit_event(
            'system_event',
            'system',
            'server',
            event_type,
            details=details
        )

# Usage
audit_logger = AuditLogger()
audit_logger.log_data_access('user123', 'crawled_pages', 'read')
audit_logger.log_system_event('server_start', {'version': '1.0.0'})
```

## Incident Response

### Incident Response Plan

#### Automated Response System
```python
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from typing import List, Dict, Any

class IncidentResponseSystem:
    def __init__(self, smtp_server: str, smtp_port: int, 
                 email_user: str, email_password: str):
        self.smtp_server = smtp_server
        self.smtp_port = smtp_port
        self.email_user = email_user
        self.email_password = email_password
        
        # Define incident severity levels
        self.severity_levels = {
            'critical': {
                'response_time': 15,  # minutes
                'escalation_time': 30,
                'notify': ['<EMAIL>', '<EMAIL>']
            },
            'high': {
                'response_time': 60,
                'escalation_time': 120,
                'notify': ['<EMAIL>']
            },
            'medium': {
                'response_time': 240,
                'escalation_time': 480,
                'notify': ['<EMAIL>']
            },
            'low': {
                'response_time': 1440,
                'escalation_time': 2880,
                'notify': ['<EMAIL>']
            }
        }
    
    def handle_incident(self, incident_type: str, severity: str, 
                       details: Dict[str, Any]):
        """Handle security incident"""
        incident_id = self._generate_incident_id()
        
        # Log incident
        self._log_incident(incident_id, incident_type, severity, details)
        
        # Determine response actions
        response_config = self.severity_levels.get(severity, self.severity_levels['medium'])
        
        # Notify relevant parties
        self._notify_incident(incident_id, incident_type, severity, details, 
                            response_config['notify'])
        
        # Execute automated response
        self._execute_automated_response(incident_type, severity, details)
        
        return incident_id
    
    def _generate_incident_id(self) -> str:
        """Generate unique incident ID"""
        import uuid
        return f"INC-{datetime.utcnow().strftime('%Y%m%d-%H%M%S')}-{str(uuid.uuid4())[:8]}"
    
    def _log_incident(self, incident_id: str, incident_type: str, 
                     severity: str, details: Dict[str, Any]):
        """Log incident details"""
        security_logger.log_security_event(
            'incident',
            {
                'incident_id': incident_id,
                'incident_type': incident_type,
                'severity': severity,
                'details': details
            },
            'ERROR'
        )
    
    def _notify_incident(self, incident_id: str, incident_type: str, 
                        severity: str, details: Dict[str, Any], 
                        recipients: List[str]):
        """Send incident notification"""
        subject = f"Security Incident [{severity.upper()}] - {incident_type}"
        body = f"""
        Incident ID: {incident_id}
        Type: {incident_type}
        Severity: {severity}
        Time: {datetime.utcnow().isoformat()}
        
        Details:
        {json.dumps(details, indent=2)}
        
        Please respond according to incident response procedures.
        """
        
        self._send_email(recipients, subject, body)
    
    def _execute_automated_response(self, incident_type: str, severity: str, 
                                  details: Dict[str, Any]):
        """Execute automated response actions"""
        if incident_type == 'brute_force_attack':
            self._block_ip_address(details.get('ip_address'))
        elif incident_type == 'suspicious_activity':
            self._increase_monitoring(details.get('ip_address'))
        elif incident_type == 'data_breach':
            self._lock_down_system()
    
    def _block_ip_address(self, ip_address: str):
        """Block IP address using firewall"""
        import subprocess
        subprocess.run(['iptables', '-A', 'INPUT', '-s', ip_address, '-j', 'DROP'])
    
    def _increase_monitoring(self, ip_address: str):
        """Increase monitoring for specific IP"""
        # Implementation depends on monitoring system
        pass
    
    def _lock_down_system(self):
        """Lock down system in case of serious breach"""
        # Implementation depends on requirements
        pass
    
    def _send_email(self, recipients: List[str], subject: str, body: str):
        """Send email notification"""
        msg = MIMEMultipart()
        msg['From'] = self.email_user
        msg['To'] = ', '.join(recipients)
        msg['Subject'] = subject
        
        msg.attach(MIMEText(body, 'plain'))
        
        try:
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            server.starttls()
            server.login(self.email_user, self.email_password)
            server.send_message(msg)
            server.quit()
        except Exception as e:
            print(f"Failed to send email: {e}")
```

### Incident Response Playbooks

#### Brute Force Attack Response
```bash
#!/bin/bash
# brute-force-response.sh
# Automated response to brute force attacks

IP_ADDRESS=$1
INCIDENT_ID=$2

echo "Responding to brute force attack from $IP_ADDRESS (Incident: $INCIDENT_ID)"

# 1. Block IP address
iptables -A INPUT -s $IP_ADDRESS -j DROP

# 2. Log the action
echo "$(date): Blocked IP $IP_ADDRESS due to brute force attack" >> /var/log/security-actions.log

# 3. Gather evidence
tcpdump -i any -n host $IP_ADDRESS -w /tmp/evidence-$INCIDENT_ID.pcap &
TCPDUMP_PID=$!

# 4. Analyze recent logs
grep $IP_ADDRESS /var/log/auth.log | tail -20 > /tmp/auth-evidence-$INCIDENT_ID.log

# 5. Check for other suspicious IPs
awk '{print $1}' /var/log/auth.log | sort | uniq -c | sort -nr | head -20 > /tmp/suspicious-ips-$INCIDENT_ID.log

# 6. Kill tcpdump after 5 minutes
sleep 300
kill $TCPDUMP_PID

echo "Brute force response completed for $IP_ADDRESS"
```

#### Data Breach Response
```bash
#!/bin/bash
# data-breach-response.sh
# Automated response to data breach

INCIDENT_ID=$1
BREACH_TYPE=$2

echo "Responding to data breach (Incident: $INCIDENT_ID, Type: $BREACH_TYPE)"

# 1. Stop all services
docker-compose down

# 2. Create forensic image
dd if=/dev/sda of=/forensics/disk-image-$INCIDENT_ID.dd bs=4096 conv=noerror,sync

# 3. Preserve logs
cp -r /var/log /forensics/logs-$INCIDENT_ID/

# 4. Notify authorities (if required)
# Implementation depends on legal requirements

# 5. Change all passwords/keys
./rotate-all-credentials.sh $INCIDENT_ID

# 6. Document incident
echo "$(date): Data breach response initiated for incident $INCIDENT_ID" >> /var/log/incident-response.log

echo "Data breach response completed for incident $INCIDENT_ID"
```

## Security Best Practices

### Development Security

#### Secure Coding Guidelines
1. **Input Validation**: Validate all inputs at entry points
2. **Output Encoding**: Encode outputs to prevent XSS
3. **Authentication**: Implement proper authentication mechanisms
4. **Authorization**: Use principle of least privilege
5. **Error Handling**: Don't expose sensitive information in errors
6. **Logging**: Log security events without exposing sensitive data
7. **Dependencies**: Keep dependencies updated and scan for vulnerabilities
8. **Testing**: Include security testing in development workflow

#### Security Testing
```bash
#!/bin/bash
# security-testing.sh
# Automated security testing

echo "Starting security testing..."

# 1. Static analysis
bandit -r src/
safety check

# 2. Dependency scanning
pip-audit

# 3. Container scanning
trivy image crawl4ai-rag-mcp:latest

# 4. Configuration scanning
checkov -f docker-compose.yml

# 5. Network scanning
nmap -sS -O localhost

# 6. Web application scanning
nikto -h http://localhost:8051

echo "Security testing completed"
```

### Deployment Security

#### Pre-deployment Checklist
- [ ] All default passwords changed
- [ ] SSL/TLS certificates installed and configured
- [ ] Firewall rules configured and tested
- [ ] Security headers implemented
- [ ] Rate limiting configured
- [ ] Monitoring and alerting set up
- [ ] Backup and recovery procedures tested
- [ ] Incident response plan documented
- [ ] Security policies documented and communicated

#### Post-deployment Security
1. **Regular Security Assessments**: Monthly vulnerability scans
2. **Penetration Testing**: Quarterly professional assessments
3. **Security Awareness Training**: Staff training on security practices
4. **Compliance Audits**: Regular compliance checks
5. **Threat Intelligence**: Monitor for new threats and vulnerabilities

### Operational Security

#### Daily Security Tasks
```bash
#!/bin/bash
# daily-security-tasks.sh
# Daily security maintenance

echo "Starting daily security tasks..."

# 1. Check for failed login attempts
grep "Failed password" /var/log/auth.log | tail -10

# 2. Check disk usage
df -h | awk '$5 > 80 {print $0}'

# 3. Check for suspicious processes
ps aux | grep -E "(nc|netcat|telnet|wget|curl)" | grep -v grep

# 4. Check network connections
netstat -tuln | grep -E ":(22|80|443|8051)"

# 5. Review security logs
tail -20 /var/log/crawl4ai-security.log

# 6. Check certificate expiration
openssl x509 -in /etc/ssl/certs/your-domain.crt -text -noout | grep "Not After"

echo "Daily security tasks completed"
```

## Compliance and Governance

### Regulatory Compliance

#### GDPR Compliance
```python
class GDPRCompliance:
    def __init__(self):
        self.retention_periods = {
            'crawled_content': 365,  # days
            'user_queries': 30,
            'access_logs': 90,
            'audit_logs': 2555  # 7 years
        }
    
    def anonymize_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Anonymize personal data"""
        # Remove or hash PII
        if 'email' in data:
            data['email'] = self._hash_email(data['email'])
        if 'ip_address' in data:
            data['ip_address'] = self._anonymize_ip(data['ip_address'])
        
        return data
    
    def delete_expired_data(self):
        """Delete data that has exceeded retention period"""
        # Implementation depends on data store
        pass
    
    def generate_data_inventory(self) -> Dict[str, Any]:
        """Generate data inventory for compliance"""
        return {
            'data_categories': [
                'crawled_web_content',
                'user_queries',
                'access_logs',
                'audit_logs'
            ],
            'retention_periods': self.retention_periods,
            'processing_purposes': [
                'content_indexing',
                'search_functionality',
                'security_monitoring'
            ]
        }
    
    def _hash_email(self, email: str) -> str:
        """Hash email address"""
        import hashlib
        return hashlib.sha256(email.encode()).hexdigest()
    
    def _anonymize_ip(self, ip: str) -> str:
        """Anonymize IP address"""
        parts = ip.split('.')
        return f"{parts[0]}.{parts[1]}.{parts[2]}.0"
```

#### SOC 2 Controls
```python
class SOC2Controls:
    def __init__(self):
        self.controls = {
            'access_controls': self._implement_access_controls,
            'logical_access': self._implement_logical_access,
            'encryption': self._implement_encryption,
            'monitoring': self._implement_monitoring,
            'backup': self._implement_backup_controls
        }
    
    def _implement_access_controls(self):
        """Implement access controls"""
        # User access management
        # Role-based access control
        # Regular access reviews
        pass
    
    def _implement_logical_access(self):
        """Implement logical access controls"""
        # Multi-factor authentication
        # Password policies
        # Session management
        pass
    
    def _implement_encryption(self):
        """Implement encryption controls"""
        # Data at rest encryption
        # Data in transit encryption
        # Key management
        pass
    
    def _implement_monitoring(self):
        """Implement monitoring controls"""
        # Continuous monitoring
        # Alerting systems
        # Log analysis
        pass
    
    def _implement_backup_controls(self):
        """Implement backup controls"""
        # Regular backups
        # Backup testing
        # Recovery procedures
        pass
```

### Security Governance

#### Security Policy Template
```yaml
# security-policy.yml
security_policy:
  version: "1.0"
  effective_date: "2025-01-17"
  review_cycle: "annual"
  
  access_control:
    authentication:
      - multi_factor_authentication: required
      - password_complexity: high
      - session_timeout: 30_minutes
    
    authorization:
      - principle: least_privilege
      - role_based_access: enabled
      - regular_reviews: quarterly
  
  data_protection:
    classification:
      - public: low_security
      - internal: medium_security
      - confidential: high_security
      - restricted: highest_security
    
    encryption:
      - at_rest: required
      - in_transit: required
      - key_management: centralized
  
  incident_response:
    response_time:
      - critical: 15_minutes
      - high: 1_hour
      - medium: 4_hours
      - low: 24_hours
    
    communication:
      - internal: required
      - external: as_needed
      - authorities: if_required
  
  monitoring:
    continuous_monitoring: enabled
    log_retention: 90_days
    alerting: real_time
    
  compliance:
    frameworks:
      - gdpr: applicable
      - soc2: applicable
      - iso27001: planned
    
    audits:
      - internal: quarterly
      - external: annually
```

---

**Security Guide Version**: 2.0  
**Last Updated**: 2025-01-17  
**Compatible Server Version**: 1.0+  
**Next Review**: 2025-04-17  

For setup instructions, see the [Complete Setup Guide](COMPLETE_SETUP_GUIDE.md).  
For integration guidance, refer to the [Integration Guide](INTEGRATION_GUIDE.md).  
For troubleshooting security issues, consult the [Troubleshooting Guide](TROUBLESHOOTING_GUIDE.md).