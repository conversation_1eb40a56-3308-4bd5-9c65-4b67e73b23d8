# MCP Crawl4AI RAG Server - Consolidated Documentation

## Overview

This directory contains the consolidated, streamlined documentation for the MCP Crawl4AI RAG server. The documentation has been restructured to eliminate redundancy, improve clarity, and provide a more logical information hierarchy.

## Documentation Structure

### 📖 Core Documentation

| Document | Purpose | Target Audience |
|----------|---------|-----------------|
| **[Complete Setup Guide](COMPLETE_SETUP_GUIDE.md)** | Comprehensive installation, configuration, and deployment instructions | Developers, DevOps Engineers |
| **[Integration Guide](INTEGRATION_GUIDE.md)** | Claude Desktop, Claude Code, and MCP client integration | End Users, Developers |
| **[Security & Best Practices](SECURITY_GUIDE.md)** | Security implementation, deployment practices, and compliance | Security Engineers, DevOps |
| **[Troubleshooting Guide](TROUBLESHOOTING_GUIDE.md)** | Common issues, debugging, and maintenance procedures | Support Engineers, Developers |
| **[Feature Documentation](FEATURES.md)** | Detailed feature explanations and usage examples | All Users |

### 🎯 Quick Start Paths

#### For Developers
1. **[Complete Setup Guide](COMPLETE_SETUP_GUIDE.md)** - Full installation and configuration
2. **[Integration Guide](INTEGRATION_GUIDE.md)** - Connect to your preferred MCP client
3. **[Troubleshooting Guide](TROUBLESHOOTING_GUIDE.md)** - Resolve common issues

#### For End Users
1. **[Integration Guide](INTEGRATION_GUIDE.md)** - Connect to Claude Desktop/Code
2. **[Feature Documentation](FEATURES.md)** - Learn about available capabilities
3. **[Troubleshooting Guide](TROUBLESHOOTING_GUIDE.md)** - Get help with issues

#### For Production Deployment
1. **[Complete Setup Guide](COMPLETE_SETUP_GUIDE.md)** → Production Deployment section
2. **[Security & Best Practices](SECURITY_GUIDE.md)** - Security implementation
3. **[Troubleshooting Guide](TROUBLESHOOTING_GUIDE.md)** → Monitoring section

## Key Features

### 🔍 Smart Web Crawling
- **Intelligent URL Detection**: Automatically handles sitemaps, text files, and individual pages
- **Depth Control**: Configurable crawling depth with transparency reporting
- **Content Processing**: Advanced chunking and preprocessing for optimal RAG performance

### 🧠 Advanced RAG Capabilities
- **Vector Search**: Semantic search using OpenAI or Ollama embeddings
- **Hybrid Search**: Combines vector similarity with keyword matching
- **Reranking**: Cross-encoder reranking for improved relevance
- **Contextual Embeddings**: LLM-enhanced context for better retrieval

### 🔧 Specialized Tools
- **Code Example Search**: Dedicated code snippet extraction and search
- **Knowledge Graph Integration**: AI hallucination detection with Neo4j
- **Real-time Monitoring**: Performance metrics and health monitoring

### 🚀 Flexible Deployment
- **Multiple Transport Options**: SSE (web) and stdio (direct) protocols
- **Container Support**: Docker and Docker Compose deployment
- **Cloud-Ready**: Production-ready configuration with security hardening

## Architecture Overview

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  MCP Clients    │    │  Crawl4AI RAG    │    │  External APIs  │
│                 │    │  MCP Server      │    │                 │
│ • Claude Desktop│◄──►│                  │◄──►│ • OpenAI API    │
│ • Claude Code   │    │ • Web Crawling   │    │ • Supabase DB   │
│ • Other Clients │    │ • Vector Search  │    │ • Neo4j Graph   │
└─────────────────┘    │ • RAG Processing │    │ • Ollama (opt)  │
                       └──────────────────┘    └─────────────────┘
```

## Environment Configuration

### Required Variables
```bash
# Core API Keys
OPENAI_API_KEY=your_openai_api_key
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_KEY=your_supabase_service_key

# Server Configuration
HOST=0.0.0.0
PORT=8051
TRANSPORT=sse
```

### Optional Enhancements
```bash
# RAG Strategy Flags
USE_HYBRID_SEARCH=true
USE_RERANKING=true
USE_CONTEXTUAL_EMBEDDINGS=false
USE_AGENTIC_RAG=false
USE_KNOWLEDGE_GRAPH=false
```

## Quick Command Reference

### Docker Commands
```bash
# Start development server
docker-compose up -d

# Start production server
docker-compose -f docker-compose.prod.yml up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### Direct Execution
```bash
# Install dependencies
uv pip install -e .
crawl4ai-setup

# Start server (SSE transport)
TRANSPORT=sse uv run src/main.py

# Start server (stdio transport)
TRANSPORT=stdio uv run src/main.py
```

### Claude Integration
```bash
# Add to Claude Code
claude mcp add --transport sse crawl4ai-rag http://localhost:8051/sse

# Test connection
claude mcp test crawl4ai-rag
```

## Available MCP Tools

| Tool | Purpose | Usage |
|------|---------|-------|
| `crawl_single_page` | Crawl and store a single webpage | Basic content ingestion |
| `smart_crawl_url` | Intelligent crawling with depth control | Bulk content ingestion |
| `get_available_sources` | List crawled domains and content | Content discovery |
| `perform_rag_query` | Semantic search over crawled content | Information retrieval |
| `search_code_examples` | Find code snippets and examples | Developer assistance |
| `parse_github_repository` | Add GitHub repos to knowledge graph | Repository analysis |
| `check_ai_script_hallucinations` | Validate AI-generated code | Quality assurance |
| `query_knowledge_graph` | Explore knowledge graph relationships | Advanced analysis |

## Performance Optimization

### Resource Management
- **Memory Monitoring**: Adaptive memory cleanup and garbage collection
- **Connection Pooling**: Efficient database connection management
- **Caching Strategy**: Multi-level caching for embeddings and queries
- **Concurrent Processing**: Controlled parallelism for crawling operations

### Scalability Features
- **Horizontal Scaling**: Multiple server instances with load balancing
- **Database Optimization**: Efficient indexing and query optimization
- **Content Deduplication**: Intelligent duplicate detection and handling
- **Resource Limits**: Configurable limits for memory and processing

## Security Implementation

### Built-in Security
- **Input Validation**: Comprehensive sanitization of all inputs
- **Rate Limiting**: Per-client and global rate limiting
- **Secure Headers**: Complete security header implementation
- **Container Hardening**: Non-root execution and security constraints

### Production Security
- **HTTPS Enforcement**: SSL/TLS with modern cipher suites
- **Authentication**: API key and token-based authentication
- **Network Security**: Firewall configuration and access controls
- **Monitoring**: Security event logging and alerting

## Support and Maintenance

### Getting Help
- **Documentation**: Comprehensive guides in this directory
- **Troubleshooting**: Detailed issue resolution procedures
- **Logs**: Structured logging for debugging and monitoring
- **Health Checks**: Built-in health monitoring and status reporting

### Maintenance Tasks
- **Regular Updates**: Dependency and security updates
- **Database Maintenance**: Index optimization and cleanup
- **Log Management**: Log rotation and archival
- **Performance Monitoring**: Continuous performance tracking

## Contributing and Development

### Development Setup
1. Clone repository and install dependencies
2. Configure environment variables
3. Set up Supabase database
4. Run tests and validation

### Code Quality
- **Testing**: Comprehensive test suite with coverage reporting
- **Linting**: Code quality enforcement with automated checks
- **Documentation**: Inline documentation and API documentation
- **Security**: Static analysis and vulnerability scanning

---

**Documentation Version**: 2.0  
**Last Updated**: 2025-01-17  
**Compatible Server Version**: 1.0+  
**Maintenance Schedule**: Monthly updates and reviews  

For specific implementation details, please refer to the individual guide documents in this directory.