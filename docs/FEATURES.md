# Features Guide - MCP Crawl4AI RAG Server

## Table of Contents

1. [Core Features Overview](#core-features-overview)
2. [Web Crawling Capabilities](#web-crawling-capabilities)
3. [RAG and Search Features](#rag-and-search-features)
4. [Knowledge Graph Integration](#knowledge-graph-integration)
5. [MCP Tools Reference](#mcp-tools-reference)
6. [Advanced Features](#advanced-features)
7. [Feature Configuration](#feature-configuration)
8. [Performance Optimization](#performance-optimization)
9. [Use Cases and Examples](#use-cases-and-examples)

## Core Features Overview

The MCP Crawl4AI RAG server provides comprehensive web crawling and retrieval-augmented generation capabilities through the Model Context Protocol. Here's what makes it powerful:

### Key Capabilities

| Feature Category | Description | Benefits |
|------------------|-------------|----------|
| **Smart Web Crawling** | Intelligent URL detection and content extraction | Handles sitemaps, text files, and individual pages automatically |
| **Advanced RAG** | Multiple search strategies and optimization techniques | Improved search relevance and accuracy |
| **Vector Database** | Supabase-powered vector storage with pgvector | Scalable, production-ready vector search |
| **MCP Integration** | Native Model Context Protocol support | Seamless integration with Claude and other MCP clients |
| **Knowledge Graph** | AI hallucination detection with Neo4j | Validates AI-generated code against real repositories |
| **Real-time Processing** | Streaming responses and live updates | Responsive user experience |

### Architecture Highlights

```
┌─────────────────────────────────────────────────────────────────┐
│                    MCP Crawl4AI RAG Server                     │
├─────────────────────────────────────────────────────────────────┤
│  🔍 Smart Crawling  │  🧠 Advanced RAG  │  📊 Knowledge Graph  │
│  • URL Detection    │  • Vector Search  │  • Code Validation   │
│  • Content Extract  │  • Hybrid Search  │  • Repository Graph  │
│  • Depth Control    │  • Reranking      │  • Hallucination Det │
├─────────────────────────────────────────────────────────────────┤
│  🔧 MCP Tools  │  ⚡ Performance  │  🔒 Security  │  🚀 Deploy  │
│  • 8 Core Tools   │  • Caching        │  • Input Valid.    │  • Docker      │
│  • Streaming      │  • Concurrency    │  • Rate Limiting   │  • Cloud       │
│  • Error Handling │  • Optimization   │  • Authentication  │  • Kubernetes  │
└─────────────────────────────────────────────────────────────────┘
```

## Web Crawling Capabilities

### 1. Smart URL Detection

The server automatically detects and handles different types of URLs:

#### Sitemap Processing
```json
{
  "url": "https://example.com/sitemap.xml",
  "detected_type": "sitemap",
  "pages_found": 150,
  "crawl_strategy": "sitemap_parallel"
}
```

**Features:**
- **XML Sitemap Support**: Automatically parses XML sitemaps
- **Sitemap Index Support**: Handles nested sitemap structures
- **Parallel Processing**: Crawls multiple URLs simultaneously
- **Priority Handling**: Respects sitemap priority and change frequency

#### Text File Processing
```json
{
  "url": "https://example.com/urls.txt",
  "detected_type": "text_file",
  "pages_found": 50,
  "crawl_strategy": "text_file_sequential"
}
```

**Features:**
- **Plain Text URLs**: Processes newline-separated URL lists
- **Comment Support**: Ignores lines starting with `#`
- **URL Validation**: Validates each URL before crawling
- **Error Handling**: Continues processing if individual URLs fail

#### Individual Page Crawling
```json
{
  "url": "https://example.com/page",
  "detected_type": "webpage",
  "crawl_strategy": "recursive_depth",
  "depth_control": {
    "max_depth": 3,
    "follow_internal_links": true
  }
}
```

**Features:**
- **Recursive Crawling**: Follows internal links up to specified depth
- **Link Discovery**: Extracts and follows relevant links
- **Content Filtering**: Focuses on main content areas
- **Depth Transparency**: Reports crawling depth and decisions

### 2. Advanced Content Extraction

#### JavaScript Rendering
```python
# Browser configuration for JavaScript-heavy sites
{
    "browser_config": {
        "headless": True,
        "javascript_enabled": True,
        "wait_for_selector": ".content-loaded",
        "timeout": 30
    }
}
```

**Capabilities:**
- **Dynamic Content**: Renders JavaScript-generated content
- **SPA Support**: Handles Single Page Applications
- **Custom Selectors**: Waits for specific elements to load
- **Timeout Control**: Configurable page load timeouts

#### Content Processing Pipeline
```
Raw HTML → Clean HTML → Text Extraction → Chunking → Embedding
```

**Processing Steps:**
1. **HTML Cleaning**: Removes scripts, styles, and unnecessary elements
2. **Text Extraction**: Extracts meaningful text content
3. **Structure Preservation**: Maintains document structure and hierarchy
4. **Chunking**: Splits content into manageable chunks
5. **Metadata Extraction**: Captures titles, URLs, and timestamps

### 3. Depth Control and Transparency

#### Crawl Depth Management
```json
{
  "depth_info": {
    "requested_depth": 3,
    "achieved_depth": 2,
    "stop_reason": "no_more_links",
    "links_found": 25,
    "links_crawled": 12,
    "skip_reasons": {
      "already_visited": 10,
      "depth_limit": 0,
      "no_more_links": 3
    }
  }
}
```

**Features:**
- **Depth Tracking**: Monitors crawling depth in real-time
- **Stop Reason Reporting**: Explains why crawling stopped
- **Link Analysis**: Provides detailed link statistics
- **Skip Reason Classification**: Categorizes why links were skipped

#### Transparency Reporting
```json
{
  "transparency_report": {
    "total_pages_discovered": 50,
    "total_pages_crawled": 35,
    "total_chunks_created": 140,
    "average_chunk_size": 1200,
    "crawl_duration": "2m 30s",
    "errors_encountered": 2
  }
}
```

## RAG and Search Features

### 1. Vector Search Engine

#### Embedding Strategies
```bash
# OpenAI Embeddings (default)
EMBEDDING_STRATEGY=openai
OPENAI_EMBEDDING_MODEL=text-embedding-3-small
EMBEDDING_DIMENSIONS=1536

# Ollama Embeddings (local)
EMBEDDING_STRATEGY=ollama
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_EMBEDDING_MODEL=nomic-embed-text
EMBEDDING_DIMENSIONS=768
```

**Supported Models:**
- **OpenAI**: `text-embedding-3-small`, `text-embedding-3-large`, `text-embedding-ada-002`
- **Ollama**: `nomic-embed-text`, `all-minilm`, `mxbai-embed-large`

#### Vector Search Capabilities
```python
# Example search query
search_result = {
    "query": "machine learning algorithms",
    "results": [
        {
            "content": "Machine learning algorithms are...",
            "similarity": 0.89,
            "source": "ml-docs.example.com",
            "metadata": {
                "title": "Introduction to ML",
                "url": "https://ml-docs.example.com/intro"
            }
        }
    ]
}
```

### 2. Hybrid Search System

#### Configuration
```bash
USE_HYBRID_SEARCH=true
HYBRID_SEARCH_WEIGHTS=0.7,0.3  # vector_weight, keyword_weight
```

#### How It Works
1. **Vector Search**: Semantic similarity using embeddings
2. **Keyword Search**: Traditional text matching
3. **Score Fusion**: Combines results using weighted scores
4. **Ranking**: Orders results by combined relevance

#### Example Results
```json
{
  "hybrid_search_results": [
    {
      "content": "Neural networks are...",
      "vector_score": 0.85,
      "keyword_score": 0.92,
      "combined_score": 0.87,
      "match_type": "both"
    },
    {
      "content": "Machine learning basics...",
      "vector_score": 0.78,
      "keyword_score": 0.0,
      "combined_score": 0.55,
      "match_type": "vector_only"
    }
  ]
}
```

### 3. Advanced Reranking

#### Cross-Encoder Reranking
```bash
USE_RERANKING=true
RERANKING_MODEL=cross-encoder/ms-marco-MiniLM-L-6-v2
```

**Process:**
1. **Initial Retrieval**: Get top-k results from vector/hybrid search
2. **Reranking**: Use cross-encoder to rerank results
3. **Score Adjustment**: Adjust relevance scores based on reranking
4. **Final Ranking**: Return reranked results

#### Reranking Benefits
- **Improved Relevance**: Better understanding of query-document relationships
- **Context Awareness**: Considers full query context
- **Quality Filtering**: Removes low-quality results
- **User Satisfaction**: Higher quality search results

### 4. Contextual Embeddings

#### Enhancement Process
```bash
USE_CONTEXTUAL_EMBEDDINGS=true
CONTEXTUAL_MODEL=gpt-4o-mini
```

**How It Works:**
1. **Context Analysis**: Analyzes surrounding content
2. **Summary Generation**: Creates contextual summary
3. **Enhanced Embedding**: Combines original + contextual information
4. **Improved Retrieval**: Better search results due to richer context

#### Example Enhancement
```json
{
  "original_chunk": "The algorithm performs well...",
  "contextual_enhancement": "In the context of machine learning classification tasks, the algorithm performs well on structured datasets...",
  "embedding_quality": "improved"
}
```

## Knowledge Graph Integration

### 1. Repository Analysis

#### GitHub Repository Processing
```python
# Example repository analysis
{
    "repository": "https://github.com/user/repo",
    "analysis_result": {
        "total_files": 250,
        "python_files": 180,
        "classes_extracted": 45,
        "functions_extracted": 320,
        "dependencies_mapped": 25
    }
}
```

**Features:**
- **Code Structure Analysis**: Extracts classes, functions, and methods
- **Dependency Mapping**: Maps import relationships
- **Documentation Extraction**: Captures docstrings and comments
- **File Type Support**: Python, JavaScript, TypeScript, and more

### 2. Hallucination Detection

#### AI Code Validation
```python
# Validation example
validation_result = {
    "script_path": "ai_generated_script.py",
    "validation_results": {
        "classes_valid": 8,
        "classes_invalid": 2,
        "functions_valid": 15,
        "functions_invalid": 3,
        "overall_accuracy": 0.85
    },
    "issues_found": [
        {
            "type": "missing_class",
            "description": "Class 'DataProcessor' not found in repository",
            "confidence": 0.95
        }
    ]
}
```

**Validation Types:**
- **Class Existence**: Verifies classes exist in repository
- **Method Validation**: Checks method signatures and availability
- **Import Validation**: Validates import statements
- **Documentation Consistency**: Checks against actual documentation

### 3. Knowledge Graph Queries

#### Cypher Query Support
```cypher
// Example knowledge graph query
MATCH (repo:Repository)-[:CONTAINS]->(file:File)-[:DEFINES]->(class:Class)
WHERE repo.name = 'my-project'
RETURN class.name, file.path, class.methods
```

**Query Types:**
- **Structure Queries**: Find code structures and relationships
- **Dependency Queries**: Trace dependencies and imports
- **Usage Queries**: Find how code is used across the repository
- **Documentation Queries**: Search code documentation and comments

## MCP Tools Reference

### 1. Core Crawling Tools

#### `crawl_single_page`
```json
{
  "name": "crawl_single_page",
  "description": "Crawl and store a single webpage",
  "parameters": {
    "url": "https://example.com/page",
    "extract_links": true,
    "follow_robots": true
  },
  "response": {
    "success": true,
    "chunks_stored": 5,
    "links_found": 12
  }
}
```

#### `smart_crawl_url`
```json
{
  "name": "smart_crawl_url",
  "description": "Intelligently crawl based on URL type",
  "parameters": {
    "url": "https://example.com/sitemap.xml",
    "max_depth": 3,
    "max_pages": 100
  },
  "response": {
    "crawl_type": "sitemap",
    "pages_crawled": 75,
    "chunks_stored": 300
  }
}
```

### 2. Search and Retrieval Tools

#### `perform_rag_query`
```json
{
  "name": "perform_rag_query",
  "description": "Perform semantic search over crawled content",
  "parameters": {
    "query": "machine learning algorithms",
    "count": 10,
    "source_filter": "docs.ml-site.com"
  },
  "response": {
    "results": [
      {
        "content": "Machine learning algorithms...",
        "similarity": 0.89,
        "source": "docs.ml-site.com",
        "url": "https://docs.ml-site.com/algorithms"
      }
    ]
  }
}
```

#### `search_code_examples`
```json
{
  "name": "search_code_examples",
  "description": "Search for code examples and snippets",
  "parameters": {
    "query": "async function examples",
    "language": "javascript",
    "count": 5
  },
  "response": {
    "code_examples": [
      {
        "code": "async function fetchData() { ... }",
        "language": "javascript",
        "summary": "Asynchronous data fetching example"
      }
    ]
  }
}
```

### 3. Management Tools

#### `get_available_sources`
```json
{
  "name": "get_available_sources",
  "description": "List all crawled sources",
  "parameters": {},
  "response": {
    "sources": [
      {
        "id": "docs.example.com",
        "url": "https://docs.example.com",
        "summary": "API documentation",
        "word_count": 15000,
        "last_updated": "2025-01-17T10:00:00Z"
      }
    ]
  }
}
```

### 4. Knowledge Graph Tools

#### `parse_github_repository`
```json
{
  "name": "parse_github_repository",
  "description": "Add GitHub repository to knowledge graph",
  "parameters": {
    "repository_url": "https://github.com/user/repo",
    "branch": "main"
  },
  "response": {
    "success": true,
    "files_processed": 150,
    "nodes_created": 500,
    "relationships_created": 800
  }
}
```

#### `check_ai_script_hallucinations`
```json
{
  "name": "check_ai_script_hallucinations",
  "description": "Validate AI-generated code against repository",
  "parameters": {
    "script_content": "class DataProcessor: ...",
    "repository_context": "my-project"
  },
  "response": {
    "validation_score": 0.85,
    "issues_found": 3,
    "recommendations": [
      "Class 'DataProcessor' not found in repository"
    ]
  }
}
```

## Advanced Features

### 1. Performance Optimization

#### Intelligent Caching
```python
# Cache configuration
cache_config = {
    "embedding_cache": {
        "enabled": True,
        "ttl": 3600,  # 1 hour
        "max_size": 10000
    },
    "query_cache": {
        "enabled": True,
        "ttl": 1800,  # 30 minutes
        "max_size": 5000
    }
}
```

#### Concurrent Processing
```python
# Concurrency settings
concurrency_config = {
    "max_concurrent_crawls": 3,
    "max_concurrent_embeddings": 5,
    "max_concurrent_queries": 10,
    "semaphore_limits": {
        "browser_instances": 2,
        "api_calls": 10
    }
}
```

### 2. Error Handling and Resilience

#### Automatic Retry Logic
```python
# Retry configuration
retry_config = {
    "max_retries": 3,
    "backoff_factor": 2,
    "retry_on_errors": [
        "ConnectionError",
        "TimeoutError",
        "RateLimitError"
    ]
}
```

#### Graceful Degradation
```python
# Fallback strategies
fallback_config = {
    "openai_api_down": "use_cached_embeddings",
    "supabase_slow": "use_local_cache",
    "browser_crash": "use_requests_fallback"
}
```

### 3. Real-time Monitoring

#### Performance Metrics
```json
{
  "metrics": {
    "requests_per_minute": 45,
    "average_response_time": 0.85,
    "cache_hit_rate": 0.72,
    "error_rate": 0.02,
    "active_crawls": 2,
    "database_connections": 5
  }
}
```

#### Health Monitoring
```json
{
  "health_status": {
    "overall": "healthy",
    "components": {
      "database": "healthy",
      "openai_api": "healthy",
      "browser_engine": "healthy",
      "cache_system": "healthy"
    },
    "last_check": "2025-01-17T10:00:00Z"
  }
}
```

## Feature Configuration

### 1. Basic Configuration

#### Essential Settings
```bash
# Core APIs
OPENAI_API_KEY=your_openai_key
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_KEY=your_supabase_key

# Server settings
HOST=0.0.0.0
PORT=8051
TRANSPORT=sse
```

#### RAG Enhancement Flags
```bash
# Recommended for production
USE_HYBRID_SEARCH=true
USE_RERANKING=true

# Advanced features (higher resource usage)
USE_CONTEXTUAL_EMBEDDINGS=false
USE_AGENTIC_RAG=false
USE_KNOWLEDGE_GRAPH=false
```

### 2. Advanced Configuration

#### Performance Tuning
```bash
# Concurrency settings
MAX_CONCURRENT_CRAWLS=3
MAX_CONCURRENT_EMBEDDINGS=5
MAX_CONCURRENT_QUERIES=10

# Memory management
MAX_MEMORY_USAGE=4096  # MB
CACHE_SIZE=1024        # MB
CLEANUP_INTERVAL=3600  # seconds
```

#### Quality Settings
```bash
# Search quality
SEARCH_SIMILARITY_THRESHOLD=0.5
RERANKING_TOP_K=50
HYBRID_SEARCH_WEIGHTS=0.7,0.3

# Content quality
MIN_CONTENT_LENGTH=100
MAX_CONTENT_LENGTH=10000
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
```

### 3. Feature Flags

#### Experimental Features
```bash
# Experimental features (use with caution)
ENABLE_EXPERIMENTAL_FEATURES=false
EXPERIMENTAL_EMBEDDING_MODELS=true
EXPERIMENTAL_SEARCH_ALGORITHMS=false
```

#### Debug and Development
```bash
# Debug settings
DEBUG_MODE=false
VERBOSE_LOGGING=false
PERFORMANCE_PROFILING=false
TRACE_REQUESTS=false
```

## Performance Optimization

### 1. Embedding Optimization

#### Batch Processing
```python
# Batch embedding configuration
batch_config = {
    "batch_size": 100,
    "max_tokens_per_batch": 8000,
    "parallel_batches": 3
}
```

#### Caching Strategy
```python
# Multi-level caching
cache_hierarchy = {
    "l1_memory": {
        "size": "256MB",
        "ttl": 300
    },
    "l2_disk": {
        "size": "2GB",
        "ttl": 3600
    },
    "l3_database": {
        "size": "unlimited",
        "ttl": 86400
    }
}
```

### 2. Database Optimization

#### Index Strategy
```sql
-- Performance indexes
CREATE INDEX IF NOT EXISTS idx_crawled_pages_embedding 
ON crawled_pages USING ivfflat (embedding vector_cosine_ops);

CREATE INDEX IF NOT EXISTS idx_crawled_pages_source_id_created 
ON crawled_pages(source_id, created_at);

CREATE INDEX IF NOT EXISTS idx_crawled_pages_content_gin 
ON crawled_pages USING gin(to_tsvector('english', content));
```

#### Connection Pooling
```python
# Database connection optimization
db_config = {
    "pool_size": 10,
    "max_overflow": 20,
    "pool_timeout": 30,
    "pool_recycle": 3600
}
```

### 3. Crawling Optimization

#### Browser Optimization
```python
# Browser performance settings
browser_config = {
    "headless": True,
    "memory_limit": "512MB",
    "timeout": 30,
    "user_agent": "Crawl4AI-MCP/1.0",
    "extra_args": [
        "--no-sandbox",
        "--disable-dev-shm-usage",
        "--disable-gpu"
    ]
}
```

#### Content Processing
```python
# Content processing optimization
processing_config = {
    "parallel_processing": True,
    "chunk_size": 1000,
    "overlap": 200,
    "max_chunks_per_page": 50
}
```

## Use Cases and Examples

### 1. Documentation Indexing

#### Scenario
Index and search through technical documentation for a software project.

#### Implementation
```bash
# 1. Configure for documentation
USE_HYBRID_SEARCH=true
USE_RERANKING=true
USE_CONTEXTUAL_EMBEDDINGS=true

# 2. Crawl documentation
{
  "tool": "smart_crawl_url",
  "parameters": {
    "url": "https://docs.myproject.com/sitemap.xml",
    "max_depth": 3
  }
}

# 3. Search documentation
{
  "tool": "perform_rag_query",
  "parameters": {
    "query": "How to configure authentication?",
    "count": 5,
    "source_filter": "docs.myproject.com"
  }
}
```

### 2. Code Example Search

#### Scenario
Search for code examples and snippets across multiple repositories.

#### Implementation
```bash
# 1. Enable agentic RAG
USE_AGENTIC_RAG=true

# 2. Crawl code repositories
{
  "tool": "crawl_single_page",
  "parameters": {
    "url": "https://github.com/user/repo"
  }
}

# 3. Search for code examples
{
  "tool": "search_code_examples",
  "parameters": {
    "query": "async function with error handling",
    "language": "javascript",
    "count": 10
  }
}
```

### 3. Knowledge Validation

#### Scenario
Validate AI-generated code against a knowledge base.

#### Implementation
```bash
# 1. Enable knowledge graph
USE_KNOWLEDGE_GRAPH=true

# 2. Index repository
{
  "tool": "parse_github_repository",
  "parameters": {
    "repository_url": "https://github.com/myorg/myproject",
    "branch": "main"
  }
}

# 3. Validate AI code
{
  "tool": "check_ai_script_hallucinations",
  "parameters": {
    "script_content": "class DataProcessor: ...",
    "repository_context": "myproject"
  }
}
```

### 4. Multi-Source Research

#### Scenario
Research across multiple sources with comprehensive search.

#### Implementation
```bash
# 1. Configure for comprehensive search
USE_HYBRID_SEARCH=true
USE_RERANKING=true
USE_CONTEXTUAL_EMBEDDINGS=true

# 2. Crawl multiple sources
urls = [
    "https://source1.com/sitemap.xml",
    "https://source2.com/docs/",
    "https://source3.com/api/"
]

# 3. Perform comprehensive search
{
  "tool": "perform_rag_query",
  "parameters": {
    "query": "machine learning best practices",
    "count": 20
  }
}
```

---

**Features Guide Version**: 2.0  
**Last Updated**: 2025-01-17  
**Compatible Server Version**: 1.0+  
**Next Review**: 2025-04-17  

For setup instructions, see the [Complete Setup Guide](COMPLETE_SETUP_GUIDE.md).  
For integration guidance, refer to the [Integration Guide](INTEGRATION_GUIDE.md).  
For troubleshooting, consult the [Troubleshooting Guide](TROUBLESHOOTING_GUIDE.md).  