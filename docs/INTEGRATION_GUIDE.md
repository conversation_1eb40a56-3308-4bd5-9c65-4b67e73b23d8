# Integration Guide - MCP Crawl4AI RAG Server

## Table of Contents

1. [Overview](#overview)
2. [Claude Desktop Integration](#claude-desktop-integration)
3. [Claude Code Integration](#claude-code-integration)
4. [MCP Client Integration](#mcp-client-integration)
5. [Configuration Examples](#configuration-examples)
6. [Testing Integration](#testing-integration)
7. [Common Integration Issues](#common-integration-issues)
8. [Advanced Integration Scenarios](#advanced-integration-scenarios)

## Overview

The MCP Crawl4AI RAG server supports multiple integration methods to connect with various MCP clients. This guide provides detailed instructions for integrating with popular MCP clients and troubleshooting common integration issues.

### Supported Transport Protocols

| Protocol | Use Case | Advantages | Disadvantages |
|----------|----------|------------|---------------|
| **SSE (Server-Sent Events)** | Web-based clients, remote connections | HTTP-based, firewall-friendly, supports remote deployment | Requires HTTP server setup |
| **stdio** | Direct command-line clients | Simple, direct communication | Local only, requires process management |

### Supported MCP Clients

- **<PERSON>** (Official Anthropic client)
- **Claude Code** (VS Code extension)
- **Custom MCP Clients** (Any MCP-compatible client)
- **Programmatic Integration** (Direct API calls)

## Claude Desktop Integration

### Method 1: Official Integration (Recommended)

Claude Desktop now supports direct MCP server connections through the official integrations interface.

#### Setup Steps

1. **Start the MCP Server**
   ```bash
   # Using Docker
   docker-compose up -d
   
   # Or locally
   TRANSPORT=sse uv run src/main.py
   ```

2. **Open Claude Desktop**
   - Navigate to **Settings** → **Integrations**
   - Click **Add Integration**

3. **Configure Integration**
   - **Name**: `Crawl4AI RAG MCP Server`
   - **Type**: `MCP Server`
   - **URL**: `http://localhost:8051/sse` (or your server URL)
   - **Description**: `Web crawling and RAG capabilities`

4. **Test Integration**
   - Look for the MCP tools button in the chat interface
   - Try: *"Please list the available crawling sources"*

#### Remote Server Configuration

For remote servers, replace `localhost` with your server's IP or domain:

```
http://your-server-ip:8051/sse
https://your-domain.com/sse  (for HTTPS)
```

### Method 2: JSON Configuration

For advanced users who prefer configuration files or need custom settings.

#### Configuration File Location

| OS | Configuration Path |
|----|--------------------|
| **macOS** | `~/Library/Application Support/Claude/claude_desktop_config.json` |
| **Windows** | `%APPDATA%\Claude\claude_desktop_config.json` |
| **Linux** | `~/.config/Claude/claude_desktop_config.json` |

#### Local Server Configuration

```json
{
  "mcpServers": {
    "crawl4ai-rag-local": {
      "command": "npx",
      "args": [
        "mcp-remote",
        "http://localhost:8051/sse"
      ]
    }
  }
}
```

#### Remote Server Configuration (HTTP)

```json
{
  "mcpServers": {
    "crawl4ai-rag-remote": {
      "command": "npx",
      "args": [
        "mcp-remote",
        "--allow-http",
        "http://your-server-ip:8051/sse"
      ]
    }
  }
}
```

#### Remote Server Configuration (HTTPS)

```json
{
  "mcpServers": {
    "crawl4ai-rag-secure": {
      "command": "npx",
      "args": [
        "mcp-remote",
        "https://your-domain.com/sse"
      ]
    }
  }
}
```

#### Complete Configuration Example

```json
{
  "mcpServers": {
    "crawl4ai-rag": {
      "command": "npx",
      "args": [
        "mcp-remote",
        "--allow-http",
        "http://localhost:8051/sse"
      ]
    },
    "filesystem": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-filesystem",
        "/Users/<USER>/Documents"
      ]
    }
  }
}
```

#### Configuration Validation

```bash
# Validate JSON syntax
python -m json.tool ~/.config/Claude/claude_desktop_config.json

# Test mcp-remote connection
npx mcp-remote --allow-http http://localhost:8051/sse
```

## Claude Code Integration

Claude Code provides the most seamless integration experience with direct MCP server management.

### Method 1: Command Line Integration

#### Add Server
```bash
# Add SSE transport server
claude mcp add --transport sse crawl4ai-rag http://localhost:8051/sse

# Add remote server
claude mcp add --transport sse crawl4ai-rag http://your-server-ip:8051/sse

# Add HTTPS server
claude mcp add --transport sse crawl4ai-rag https://your-domain.com/sse
```

#### Test Connection
```bash
# List configured servers
claude mcp list

# Test specific server
claude mcp test crawl4ai-rag

# Remove server if needed
claude mcp remove crawl4ai-rag
```

### Method 2: JSON Configuration

#### Add to Claude Code Configuration

```json
{
  "mcpServers": {
    "crawl4ai-rag": {
      "type": "http",
      "url": "http://localhost:8051/sse"
    }
  }
}
```

#### Remote Server Configuration

```json
{
  "mcpServers": {
    "crawl4ai-rag-remote": {
      "type": "http",
      "url": "http://your-server-ip:8051/sse"
    }
  }
}
```

#### HTTPS Configuration

```json
{
  "mcpServers": {
    "crawl4ai-rag-secure": {
      "type": "http",
      "url": "https://your-domain.com/sse"
    }
  }
}
```

### Method 3: Supergateway Integration

For advanced users who need protocol bridging or custom routing.

#### Install Supergateway
```bash
npm install -g @supercorp-ai/supergateway
```

#### Configure Supergateway
```bash
# Run supergateway to bridge SSE to stdio
supergateway --sse-to-stdio http://localhost:8051/sse
```

#### Claude Code Configuration
```json
{
  "mcpServers": {
    "crawl4ai-rag-gateway": {
      "command": "supergateway",
      "args": ["--sse-to-stdio", "http://localhost:8051/sse"]
    }
  }
}
```

## MCP Client Integration

### Direct stdio Integration

For custom MCP clients or command-line tools.

#### Client Implementation Example

```python
import asyncio
import json
from mcp import ClientSession, stdio_client

async def main():
    # Create stdio client
    client = stdio_client("uv", "run", "src/main.py")
    
    async with ClientSession(client) as session:
        # List available tools
        tools = await session.list_tools()
        print("Available tools:", [tool.name for tool in tools])
        
        # Call a tool
        result = await session.call_tool(
            "get_available_sources",
            arguments={}
        )
        print("Sources:", result)
        
        # Perform RAG query
        result = await session.call_tool(
            "perform_rag_query",
            arguments={
                "query": "What is machine learning?",
                "count": 5
            }
        )
        print("Search results:", result)

if __name__ == "__main__":
    asyncio.run(main())
```

### HTTP/SSE Integration

For web-based clients or remote integrations.

#### Client Implementation Example

```python
import asyncio
import aiohttp
import json

class MCPSSEClient:
    def __init__(self, base_url):
        self.base_url = base_url
        
    async def list_tools(self):
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.base_url}/tools/list",
                headers={"Content-Type": "application/json"}
            ) as response:
                return await response.json()
    
    async def call_tool(self, name, arguments):
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.base_url}/tools/{name}",
                json=arguments,
                headers={"Content-Type": "application/json"}
            ) as response:
                return await response.json()

# Usage
async def main():
    client = MCPSSEClient("http://localhost:8051")
    
    # List tools
    tools = await client.list_tools()
    print("Available tools:", tools)
    
    # Call tool
    result = await client.call_tool("get_available_sources", {})
    print("Sources:", result)

if __name__ == "__main__":
    asyncio.run(main())
```

## Configuration Examples

### Development Environment

#### docker-compose.yml
```yaml
version: '3.8'
services:
  crawl4ai-rag-mcp:
    build: .
    ports:
      - "8051:8051"
    environment:
      - TRANSPORT=sse
      - HOST=0.0.0.0
      - PORT=8051
    env_file:
      - .env
    volumes:
      - ./logs:/app/logs
      - crawl4ai_cache:/app/.crawl4ai_cache
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8051/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  crawl4ai_cache:
```

#### Claude Desktop Configuration
```json
{
  "mcpServers": {
    "crawl4ai-rag-dev": {
      "command": "npx",
      "args": [
        "mcp-remote",
        "http://localhost:8051/sse"
      ]
    }
  }
}
```

### Production Environment

#### docker-compose.prod.yml
```yaml
version: '3.8'
services:
  crawl4ai-rag-mcp:
    build: .
    ports:
      - "127.0.0.1:8051:8051"
    environment:
      - TRANSPORT=sse
      - HOST=0.0.0.0
      - PORT=8051
      - NODE_ENV=production
    env_file:
      - .env.prod
    volumes:
      - ./logs:/app/logs
      - crawl4ai_cache:/app/.crawl4ai_cache
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8051/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
    
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/ssl/certs
    depends_on:
      - crawl4ai-rag-mcp
    restart: unless-stopped

volumes:
  crawl4ai_cache:
```

#### nginx.conf
```nginx
events {
    worker_connections 1024;
}

http {
    upstream mcp_backend {
        server crawl4ai-rag-mcp:8051;
    }
    
    server {
        listen 80;
        server_name your-domain.com;
        return 301 https://$server_name$request_uri;
    }
    
    server {
        listen 443 ssl http2;
        server_name your-domain.com;
        
        ssl_certificate /etc/ssl/certs/your-domain.crt;
        ssl_certificate_key /etc/ssl/certs/your-domain.key;
        
        location / {
            proxy_pass http://mcp_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # SSE specific headers
            proxy_buffering off;
            proxy_cache off;
            proxy_set_header Connection '';
            proxy_http_version 1.1;
            chunked_transfer_encoding off;
        }
    }
}
```

#### Claude Desktop Production Configuration
```json
{
  "mcpServers": {
    "crawl4ai-rag-prod": {
      "command": "npx",
      "args": [
        "mcp-remote",
        "https://your-domain.com/sse"
      ]
    }
  }
}
```

### Multi-Environment Configuration

#### .env.development
```bash
# Development environment
OPENAI_API_KEY=your_dev_key
SUPABASE_URL=your_dev_supabase_url
SUPABASE_SERVICE_KEY=your_dev_supabase_key
HOST=0.0.0.0
PORT=8051
TRANSPORT=sse
DEBUG=true
LOG_LEVEL=debug
USE_HYBRID_SEARCH=true
USE_RERANKING=true
```

#### .env.production
```bash
# Production environment
OPENAI_API_KEY=your_prod_key
SUPABASE_URL=your_prod_supabase_url
SUPABASE_SERVICE_KEY=your_prod_supabase_key
HOST=0.0.0.0
PORT=8051
TRANSPORT=sse
DEBUG=false
LOG_LEVEL=info
USE_HYBRID_SEARCH=true
USE_RERANKING=true
USE_CONTEXTUAL_EMBEDDINGS=true
RATE_LIMIT_ENABLED=true
SECURE_COOKIES=true
```

## Testing Integration

### 1. Health Check Testing

```bash
# Test server health
curl -f http://localhost:8051/health

# Expected response:
# {"status": "healthy", "timestamp": "2025-01-17T10:00:00Z", "version": "1.0.0"}

# Test SSE endpoint
curl -H "Accept: text/event-stream" http://localhost:8051/sse

# Expected: SSE connection established with initial handshake
```

### 2. MCP Protocol Testing

```bash
# Test tool listing
curl -X POST http://localhost:8051/tools/list \
  -H "Content-Type: application/json"

# Expected: List of available MCP tools

# Test specific tool
curl -X POST http://localhost:8051/tools/get_available_sources \
  -H "Content-Type: application/json" \
  -d '{}'

# Expected: List of available sources (may be empty initially)
```

### 3. Integration Testing with Claude

#### Claude Desktop Testing
1. **Verify Configuration**: Check that the MCP server appears in Claude Desktop settings
2. **Test Basic Functionality**: Try simple queries like "List available sources"
3. **Test Advanced Features**: Try crawling and searching operations
4. **Monitor Performance**: Check response times and resource usage

#### Claude Code Testing
```bash
# Test connection
claude mcp test crawl4ai-rag

# Test basic tool
claude "Use crawl4ai-rag to get available sources"

# Test crawling
claude "Use crawl4ai-rag to crawl https://example.com"

# Test searching
claude "Use crawl4ai-rag to search for 'machine learning' in crawled content"
```

### 4. Load Testing

```bash
# Install siege for load testing
sudo apt install siege

# Test concurrent connections
siege -c 10 -r 5 http://localhost:8051/health

# Test with tool calls
siege -c 5 -r 3 -H "Content-Type: application/json" \
  -p POST http://localhost:8051/tools/get_available_sources < empty_payload.json
```

## Common Integration Issues

### 1. Claude Desktop Issues

#### Issue: MCP Server Not Appearing
**Symptoms**: Server not visible in Claude Desktop integrations
**Solutions**:
```bash
# Check server is running
curl -f http://localhost:8051/health

# Verify JSON configuration syntax
python -m json.tool ~/.config/Claude/claude_desktop_config.json

# Restart Claude Desktop completely
killall Claude
open -a Claude
```

#### Issue: "Non-HTTPS URLs are only allowed for localhost"
**Symptoms**: Error when connecting to remote HTTP servers
**Solutions**:
```json
{
  "mcpServers": {
    "crawl4ai-rag": {
      "command": "npx",
      "args": [
        "mcp-remote",
        "--allow-http",
        "http://your-server-ip:8051/sse"
      ]
    }
  }
}
```

#### Issue: Connection Timeout
**Symptoms**: Server connection fails or times out
**Solutions**:
```bash
# Check server accessibility
curl -v http://your-server-ip:8051/sse

# Check firewall rules
sudo ufw status

# Check network connectivity
ping your-server-ip
telnet your-server-ip 8051
```

### 2. Claude Code Issues

#### Issue: Server Not Listed
**Symptoms**: Server doesn't appear in `claude mcp list`
**Solutions**:
```bash
# Add server again
claude mcp add --transport sse crawl4ai-rag http://localhost:8051/sse

# Check Claude Code configuration
claude mcp list
claude mcp test crawl4ai-rag
```

#### Issue: Tool Calls Failing
**Symptoms**: MCP tools return errors or no response
**Solutions**:
```bash
# Check server logs
docker-compose logs crawl4ai-rag-mcp

# Test tools directly
curl -X POST http://localhost:8051/tools/get_available_sources \
  -H "Content-Type: application/json" -d '{}'

# Verify environment variables
docker-compose exec crawl4ai-rag-mcp env | grep -E "(OPENAI|SUPABASE)"
```

### 3. Network and Connectivity Issues

#### Issue: Port Already in Use
**Symptoms**: Server fails to start with port binding error
**Solutions**:
```bash
# Find process using port
lsof -i :8051
netstat -tlnp | grep 8051

# Kill process or change port
kill -9 <process_id>
# Or modify PORT in .env file
```

#### Issue: SSL Certificate Problems
**Symptoms**: HTTPS connections fail with certificate errors
**Solutions**:
```bash
# Check certificate validity
openssl x509 -in /etc/ssl/certs/your-domain.crt -text -noout

# Renew certificate
certbot renew --force-renewal

# Test SSL configuration
curl -k https://your-domain.com/sse
```

### 4. Authentication and Authorization Issues

#### Issue: Invalid API Keys
**Symptoms**: Server starts but tools return authentication errors
**Solutions**:
```bash
# Verify API keys in environment
echo $OPENAI_API_KEY
echo $SUPABASE_SERVICE_KEY

# Test API key validity
curl -H "Authorization: Bearer $OPENAI_API_KEY" \
  https://api.openai.com/v1/models

# Test Supabase connection
curl -H "Authorization: Bearer $SUPABASE_SERVICE_KEY" \
  -H "apikey: $SUPABASE_SERVICE_KEY" \
  "$SUPABASE_URL/rest/v1/sources"
```

## Advanced Integration Scenarios

### 1. Multi-Instance Deployment

#### Load Balancer Configuration
```nginx
upstream mcp_backend {
    server crawl4ai-rag-mcp-1:8051;
    server crawl4ai-rag-mcp-2:8051;
    server crawl4ai-rag-mcp-3:8051;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://mcp_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Sticky sessions for SSE
        proxy_set_header Connection '';
        proxy_http_version 1.1;
        chunked_transfer_encoding off;
    }
}
```

#### docker-compose.scale.yml
```yaml
version: '3.8'
services:
  crawl4ai-rag-mcp:
    build: .
    environment:
      - TRANSPORT=sse
      - HOST=0.0.0.0
      - PORT=8051
    env_file:
      - .env
    deploy:
      replicas: 3
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
    volumes:
      - crawl4ai_cache:/app/.crawl4ai_cache
```

### 2. Kubernetes Deployment

#### deployment.yaml
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: crawl4ai-rag-mcp
spec:
  replicas: 3
  selector:
    matchLabels:
      app: crawl4ai-rag-mcp
  template:
    metadata:
      labels:
        app: crawl4ai-rag-mcp
    spec:
      containers:
      - name: crawl4ai-rag-mcp
        image: crawl4ai-rag-mcp:latest
        ports:
        - containerPort: 8051
        env:
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: crawl4ai-secrets
              key: openai-api-key
        - name: SUPABASE_URL
          valueFrom:
            secretKeyRef:
              name: crawl4ai-secrets
              key: supabase-url
        - name: SUPABASE_SERVICE_KEY
          valueFrom:
            secretKeyRef:
              name: crawl4ai-secrets
              key: supabase-service-key
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8051
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8051
          initialDelaySeconds: 5
          periodSeconds: 5
```

#### service.yaml
```yaml
apiVersion: v1
kind: Service
metadata:
  name: crawl4ai-rag-mcp-service
spec:
  selector:
    app: crawl4ai-rag-mcp
  ports:
  - port: 8051
    targetPort: 8051
  type: LoadBalancer
```

### 3. API Gateway Integration

#### AWS API Gateway Configuration
```yaml
openapi: 3.0.0
info:
  title: Crawl4AI RAG MCP API
  version: 1.0.0
paths:
  /sse:
    get:
      summary: SSE endpoint for MCP communication
      responses:
        '200':
          description: SSE stream
          content:
            text/event-stream:
              schema:
                type: string
      x-amazon-apigateway-integration:
        type: http_proxy
        httpMethod: GET
        uri: http://your-alb-endpoint:8051/sse
        
  /health:
    get:
      summary: Health check endpoint
      responses:
        '200':
          description: Server health status
      x-amazon-apigateway-integration:
        type: http_proxy
        httpMethod: GET
        uri: http://your-alb-endpoint:8051/health
```

### 4. Custom Authentication

#### JWT Authentication Middleware
```python
from functools import wraps
from flask import request, jsonify
import jwt

def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = request.headers.get('Authorization')
        if not token:
            return jsonify({'error': 'Token is missing'}), 401
        
        try:
            token = token.replace('Bearer ', '')
            data = jwt.decode(token, current_app.config['SECRET_KEY'], algorithms=['HS256'])
            current_user = data['user']
        except:
            return jsonify({'error': 'Token is invalid'}), 401
        
        return f(current_user, *args, **kwargs)
    return decorated

@app.route('/tools/<tool_name>', methods=['POST'])
@token_required
def call_tool(current_user, tool_name):
    # Tool execution logic
    pass
```

#### API Key Authentication
```python
@app.before_request
def check_api_key():
    if request.endpoint not in ['health', 'static']:
        api_key = request.headers.get('X-API-Key')
        if not api_key or api_key not in valid_api_keys:
            return jsonify({'error': 'Invalid API key'}), 401
```

---

**Integration Guide Version**: 2.0  
**Last Updated**: 2025-01-17  
**Compatible Server Version**: 1.0+  
**Next Review**: 2025-02-17  

For setup instructions, see the [Complete Setup Guide](COMPLETE_SETUP_GUIDE.md).  
For troubleshooting integration issues, refer to the [Troubleshooting Guide](TROUBLESHOOTING_GUIDE.md).  
For security considerations, consult the [Security Guide](SECURITY_GUIDE.md).