# Troubleshooting Guide - MCP Crawl4AI RAG Server

## Table of Contents

1. [Common Issues](#common-issues)
2. [Installation Problems](#installation-problems)
3. [Configuration Issues](#configuration-issues)
4. [Connection Problems](#connection-problems)
5. [Performance Issues](#performance-issues)
6. [Database Problems](#database-problems)
7. [Authentication Errors](#authentication-errors)
8. [Crawling Issues](#crawling-issues)
9. [Search and RAG Problems](#search-and-rag-problems)
10. [Diagnostic Tools](#diagnostic-tools)
11. [Monitoring and Maintenance](#monitoring-and-maintenance)
12. [Recovery Procedures](#recovery-procedures)

## Common Issues

### Quick Diagnosis Checklist

Before diving into specific troubleshooting, run through this quick checklist:

```bash
# 1. Check server health
curl -f http://localhost:8051/health
# Expected: {"status": "healthy", "timestamp": "..."}

# 2. Check Docker containers
docker-compose ps
# Expected: All services "Up"

# 3. Check logs for errors
docker-compose logs --tail=50 | grep -i error

# 4. Test database connection
docker-compose exec crawl4ai-rag-mcp python -c "
import os
from supabase import create_client
client = create_client(os.getenv('SUPABASE_URL'), os.getenv('SUPABASE_SERVICE_KEY'))
print('Database connection successful')
"

# 5. Check environment variables
docker-compose exec crawl4ai-rag-mcp env | grep -E "(OPENAI|SUPABASE|TRANSPORT)"
```

### Issue Priority Matrix

| Severity | Impact | Response Time | Examples |
|----------|--------|---------------|----------|
| **Critical** | Service down, data loss | Immediate | Server won't start, database corruption |
| **High** | Major functionality broken | 1 hour | Authentication failing, crawling not working |
| **Medium** | Reduced functionality | 4 hours | Search slow, some tools failing |
| **Low** | Minor issues | 24 hours | Logging issues, cosmetic problems |

## Installation Problems

### Issue: Python Dependencies Not Installing

**Symptoms:**
- `pip install` or `uv pip install` fails
- Missing module errors when starting server
- Version conflicts

**Solutions:**

```bash
# 1. Clear pip cache
pip cache purge

# 2. Upgrade pip and setuptools
pip install --upgrade pip setuptools wheel

# 3. Install with verbose output to see errors
pip install -v -e .

# 4. Use uv for faster installation
pip install uv
uv pip install -e .

# 5. Install system dependencies (Ubuntu/Debian)
sudo apt update
sudo apt install -y python3-dev build-essential libffi-dev libssl-dev

# 6. Install system dependencies (macOS)
brew install python@3.12
brew install libffi openssl
```

**Common Fixes:**
- **For cryptography errors**: `pip install --upgrade cryptography`
- **For lxml errors**: `sudo apt install libxml2-dev libxslt-dev`
- **For Pillow errors**: `sudo apt install libjpeg-dev zlib1g-dev`

### Issue: Playwright Installation Fails

**Symptoms:**
- Browser installation fails
- "Executable doesn't exist" errors
- Crawling fails with browser errors

**Solutions:**

```bash
# 1. Install Playwright manually
python -m playwright install

# 2. Install with dependencies
python -m playwright install --with-deps

# 3. Install specific browser
python -m playwright install chromium

# 4. Check browser installation
python -m playwright install --dry-run

# 5. Set environment variables
export PLAYWRIGHT_BROWSERS_PATH=/home/<USER>/.cache/ms-playwright

# 6. For Docker, use pre-built image
docker pull mcr.microsoft.com/playwright/python:v1.40.0-focal
```

**Docker-specific fixes:**
```dockerfile
# Add to Dockerfile
RUN apt-get update && apt-get install -y \
    libnss3 \
    libatk-bridge2.0-0 \
    libdrm2 \
    libgtk-3-0 \
    libgbm1 \
    libasound2

# Install Playwright after dependencies
RUN python -m playwright install --with-deps chromium
```

### Issue: Docker Build Fails

**Symptoms:**
- `docker build` command fails
- "No space left on device" errors
- Image build hangs

**Solutions:**

```bash
# 1. Check Docker disk usage
docker system df

# 2. Clean up Docker resources
docker system prune -a

# 3. Build with specific platform
docker build --platform linux/amd64 -t crawl4ai-rag-mcp .

# 4. Build with more memory
docker build --memory=4g -t crawl4ai-rag-mcp .

# 5. Use BuildKit for better performance
DOCKER_BUILDKIT=1 docker build -t crawl4ai-rag-mcp .

# 6. Build with no cache
docker build --no-cache -t crawl4ai-rag-mcp .
```

## Configuration Issues

### Issue: Environment Variables Not Loading

**Symptoms:**
- "Environment variable not set" errors
- API authentication failures
- Server fails to start

**Solutions:**

```bash
# 1. Check if .env file exists
ls -la .env

# 2. Verify .env file format
cat .env
# Ensure no spaces around = signs
# OPENAI_API_KEY=your_key (correct)
# OPENAI_API_KEY = your_key (incorrect)

# 3. Check Docker Compose env_file
docker-compose config
# Shows resolved configuration

# 4. Test environment in container
docker-compose exec crawl4ai-rag-mcp env

# 5. Load environment manually
set -a && source .env && set +a
```

**Common .env file issues:**
```bash
# ❌ Wrong format
OPENAI_API_KEY = "your_key"
SUPABASE_URL = your_url

# ✅ Correct format
OPENAI_API_KEY=your_key
SUPABASE_URL=your_url

# ❌ Missing quotes for complex values
DATABASE_URL=************************************/db name

# ✅ Correct escaping
DATABASE_URL=************************************/db%20name
```

### Issue: Port Already in Use

**Symptoms:**
- "Port 8051 is already in use" error
- Server fails to start
- Connection refused errors

**Solutions:**

```bash
# 1. Find process using port
lsof -i :8051
netstat -tlnp | grep 8051

# 2. Kill process using port
sudo kill -9 <PID>

# 3. Use different port
echo "PORT=8052" >> .env
docker-compose up -d

# 4. Check for Docker port conflicts
docker ps --filter "publish=8051"

# 5. Use dynamic port assignment
docker-compose -f docker-compose.yml -f docker-compose.override.yml up -d
```

### Issue: SSL Certificate Problems

**Symptoms:**
- HTTPS connections fail
- Certificate validation errors
- Browser security warnings

**Solutions:**

```bash
# 1. Check certificate validity
openssl x509 -in /etc/ssl/certs/your-domain.crt -text -noout

# 2. Verify certificate chain
openssl verify -CAfile /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/your-domain.crt

# 3. Test SSL connection
openssl s_client -connect your-domain.com:443

# 4. Renew Let's Encrypt certificate
sudo certbot renew --force-renewal

# 5. Check certificate permissions
ls -la /etc/ssl/certs/your-domain.*
sudo chown root:root /etc/ssl/certs/your-domain.*
sudo chmod 644 /etc/ssl/certs/your-domain.crt
sudo chmod 600 /etc/ssl/certs/your-domain.key
```

## Connection Problems

### Issue: Claude Desktop Integration Fails

**Symptoms:**
- MCP server not appearing in Claude Desktop
- Connection timeout errors
- Tools not available in Claude

**Solutions:**

```bash
# 1. Verify server is running
curl -f http://localhost:8051/health

# 2. Test SSE endpoint
curl -H "Accept: text/event-stream" http://localhost:8051/sse

# 3. Check Claude Desktop configuration
cat ~/.config/Claude/claude_desktop_config.json

# 4. Validate JSON syntax
python -m json.tool ~/.config/Claude/claude_desktop_config.json

# 5. Test mcp-remote connection
npx mcp-remote --allow-http http://localhost:8051/sse
```

**Common Claude Desktop fixes:**
```json
{
  "mcpServers": {
    "crawl4ai-rag": {
      "command": "npx",
      "args": [
        "mcp-remote",
        "--allow-http",
        "http://localhost:8051/sse"
      ]
    }
  }
}
```

**For remote connections:**
```json
{
  "mcpServers": {
    "crawl4ai-rag": {
      "command": "npx",
      "args": [
        "mcp-remote",
        "--allow-http",
        "http://your-server-ip:8051/sse"
      ]
    }
  }
}
```

### Issue: Claude Code Connection Problems

**Symptoms:**
- Server not listed in `claude mcp list`
- Connection test fails
- Tools not available in Claude Code

**Solutions:**

```bash
# 1. Check Claude Code MCP servers
claude mcp list

# 2. Add server again
claude mcp add --transport sse crawl4ai-rag http://localhost:8051/sse

# 3. Test connection
claude mcp test crawl4ai-rag

# 4. Check server logs
docker-compose logs crawl4ai-rag-mcp

# 5. Verify Claude Code configuration
claude mcp config
```

### Issue: Network Connectivity Problems

**Symptoms:**
- Connection refused errors
- Timeout errors
- DNS resolution failures

**Solutions:**

```bash
# 1. Test network connectivity
ping your-server-ip

# 2. Test port accessibility
telnet your-server-ip 8051
nc -v your-server-ip 8051

# 3. Check firewall rules
sudo ufw status
sudo iptables -L

# 4. Test DNS resolution
nslookup your-domain.com
dig your-domain.com

# 5. Check routing
traceroute your-server-ip

# 6. Test from different network
curl -I http://your-server-ip:8051/health
```

## Performance Issues

### Issue: Slow Response Times

**Symptoms:**
- Server responds slowly to requests
- Crawling takes too long
- Search queries timeout

**Solutions:**

```bash
# 1. Check resource usage
docker stats crawl4ai-rag-mcp
htop

# 2. Monitor database performance
# Connect to Supabase and run:
SELECT * FROM pg_stat_activity WHERE state = 'active';

# 3. Check disk I/O
iostat -x 1

# 4. Analyze logs for bottlenecks
docker-compose logs crawl4ai-rag-mcp | grep -i "slow"

# 5. Optimize database queries
# Add indexes if needed
CREATE INDEX IF NOT EXISTS idx_crawled_pages_embedding ON crawled_pages USING ivfflat (embedding vector_cosine_ops);
```

**Performance tuning:**
```bash
# Environment variables for optimization
MAX_CONCURRENT_CRAWLS=2
BROWSER_TIMEOUT=30
CACHE_SIZE_MB=1024
DB_POOL_SIZE=10
```

### Issue: High Memory Usage

**Symptoms:**
- Out of memory errors
- Container restarts frequently
- System becomes unresponsive

**Solutions:**

```bash
# 1. Check memory usage
docker stats crawl4ai-rag-mcp --no-stream
free -h

# 2. Increase Docker memory limit
docker-compose up -d --memory=4g

# 3. Enable memory monitoring
docker-compose exec crawl4ai-rag-mcp python -c "
import psutil
print(f'Memory usage: {psutil.virtual_memory().percent}%')
"

# 4. Clear cache
docker-compose exec crawl4ai-rag-mcp python -c "
import gc
gc.collect()
print('Memory cleared')
"

# 5. Restart container
docker-compose restart crawl4ai-rag-mcp
```

**Memory optimization:**
```yaml
# docker-compose.yml
services:
  crawl4ai-rag-mcp:
    deploy:
      resources:
        limits:
          memory: 4G
        reservations:
          memory: 2G
```

### Issue: High CPU Usage

**Symptoms:**
- CPU usage consistently high
- System becomes slow
- Crawling operations slow

**Solutions:**

```bash
# 1. Check CPU usage
docker stats crawl4ai-rag-mcp --no-stream
top -p $(docker inspect -f '{{.State.Pid}}' crawl4ai-rag-mcp)

# 2. Reduce concurrent operations
echo "MAX_CONCURRENT_CRAWLS=1" >> .env
docker-compose restart crawl4ai-rag-mcp

# 3. Enable CPU limiting
docker-compose up -d --cpus="1.0"

# 4. Check for infinite loops
docker-compose exec crawl4ai-rag-mcp python -c "
import threading
print('Active threads:', threading.active_count())
"
```

## Database Problems

### Issue: Supabase Connection Fails

**Symptoms:**
- Database connection errors
- "Invalid API key" errors
- Timeout connecting to database

**Solutions:**

```bash
# 1. Test API key validity
curl -H "Authorization: Bearer $SUPABASE_SERVICE_KEY" \
     -H "apikey: $SUPABASE_SERVICE_KEY" \
     "$SUPABASE_URL/rest/v1/"

# 2. Check network connectivity
ping your-project.supabase.co

# 3. Verify environment variables
echo $SUPABASE_URL
echo $SUPABASE_SERVICE_KEY

# 4. Test database connection
psql "$SUPABASE_URL" -c "SELECT version();"

# 5. Check Supabase project status
# Login to Supabase dashboard and check project status
```

### Issue: Database Schema Missing

**Symptoms:**
- "Table doesn't exist" errors
- "Column doesn't exist" errors
- Database operations fail

**Solutions:**

```bash
# 1. Check if tables exist
psql "$SUPABASE_URL" -c "SELECT tablename FROM pg_tables WHERE schemaname = 'public';"

# 2. Run schema creation script
psql "$SUPABASE_URL" -f crawled_pages.sql

# 3. Check pgvector extension
psql "$SUPABASE_URL" -c "SELECT * FROM pg_extension WHERE extname = 'vector';"

# 4. Install pgvector if missing
psql "$SUPABASE_URL" -c "CREATE EXTENSION IF NOT EXISTS vector;"

# 5. Verify indexes
psql "$SUPABASE_URL" -c "SELECT indexname FROM pg_indexes WHERE tablename = 'crawled_pages';"
```

### Issue: Database Performance Problems

**Symptoms:**
- Slow search queries
- Database timeouts
- High database CPU usage

**Solutions:**

```sql
-- 1. Check query performance
EXPLAIN ANALYZE SELECT * FROM crawled_pages WHERE embedding <=> '[1,2,3...]' LIMIT 10;

-- 2. Add missing indexes
CREATE INDEX IF NOT EXISTS idx_crawled_pages_source_id ON crawled_pages(source_id);
CREATE INDEX IF NOT EXISTS idx_crawled_pages_embedding ON crawled_pages USING ivfflat (embedding vector_cosine_ops);

-- 3. Update table statistics
ANALYZE crawled_pages;

-- 4. Check for table bloat
SELECT schemaname, tablename, n_tup_ins, n_tup_upd, n_tup_del 
FROM pg_stat_user_tables 
WHERE schemaname = 'public';

-- 5. Vacuum tables
VACUUM ANALYZE crawled_pages;
```

## Authentication Errors

### Issue: OpenAI API Key Invalid

**Symptoms:**
- "Invalid API key" errors
- Authentication failures with OpenAI
- Embedding generation fails

**Solutions:**

```bash
# 1. Test API key directly
curl -H "Authorization: Bearer $OPENAI_API_KEY" \
     https://api.openai.com/v1/models

# 2. Check key format
echo $OPENAI_API_KEY | grep -E '^sk-[a-zA-Z0-9]{48}$'

# 3. Verify key hasn't expired
# Check OpenAI dashboard for key status

# 4. Test embeddings endpoint
curl -X POST "https://api.openai.com/v1/embeddings" \
     -H "Authorization: Bearer $OPENAI_API_KEY" \
     -H "Content-Type: application/json" \
     -d '{"input": "test", "model": "text-embedding-3-small"}'

# 5. Check rate limits
curl -i -H "Authorization: Bearer $OPENAI_API_KEY" \
     https://api.openai.com/v1/models
```

### Issue: Supabase Authentication Problems

**Symptoms:**
- "Invalid service key" errors
- Database access denied
- RLS policy errors

**Solutions:**

```bash
# 1. Test service key
curl -H "Authorization: Bearer $SUPABASE_SERVICE_KEY" \
     -H "apikey: $SUPABASE_SERVICE_KEY" \
     "$SUPABASE_URL/rest/v1/sources"

# 2. Check key permissions
# In Supabase dashboard, verify service key permissions

# 3. Test different endpoints
curl -H "Authorization: Bearer $SUPABASE_SERVICE_KEY" \
     "$SUPABASE_URL/rest/v1/crawled_pages?limit=1"

# 4. Check RLS policies
psql "$SUPABASE_URL" -c "SELECT * FROM pg_policies WHERE tablename = 'crawled_pages';"

# 5. Disable RLS temporarily (for testing)
psql "$SUPABASE_URL" -c "ALTER TABLE crawled_pages DISABLE ROW LEVEL SECURITY;"
```

## Crawling Issues

### Issue: Crawling Fails with Browser Errors

**Symptoms:**
- "Browser not found" errors
- Crawling timeouts
- JavaScript errors during crawling

**Solutions:**

```bash
# 1. Check browser installation
docker-compose exec crawl4ai-rag-mcp python -c "
from playwright.async_api import async_playwright
import asyncio

async def test_browser():
    async with async_playwright() as p:
        browser = await p.chromium.launch()
        page = await browser.new_page()
        await page.goto('https://example.com')
        print('Browser test successful')
        await browser.close()

asyncio.run(test_browser())
"

# 2. Test with different browser settings
# Add to environment
BROWSER_HEADLESS=true
BROWSER_TIMEOUT=60

# 3. Check browser process
docker-compose exec crawl4ai-rag-mcp ps aux | grep chrome

# 4. Test with simple page
curl -X POST http://localhost:8051/tools/crawl_single_page \
     -H "Content-Type: application/json" \
     -d '{"url": "https://example.com"}'
```

### Issue: Crawling Blocked by Robots.txt

**Symptoms:**
- "Robots.txt disallows" errors
- Crawling returns no content
- 403 Forbidden errors

**Solutions:**

```bash
# 1. Check robots.txt
curl https://example.com/robots.txt

# 2. Override robots.txt (use responsibly)
# Add to crawling configuration
RESPECT_ROBOTS_TXT=false

# 3. Use different user agent
USER_AGENT="Mozilla/5.0 (compatible; MCP-Crawler/1.0)"

# 4. Add delay between requests
CRAWL_DELAY=2

# 5. Check if site allows crawling
curl -I https://example.com/
```

### Issue: Content Not Extracting Properly

**Symptoms:**
- Empty content returned
- Only HTML returned, no text
- Missing important content

**Solutions:**

```bash
# 1. Test with different extraction methods
# Enable JavaScript rendering
ENABLE_JAVASCRIPT=true

# 2. Increase page load timeout
PAGE_LOAD_TIMEOUT=30

# 3. Test extraction manually
docker-compose exec crawl4ai-rag-mcp python -c "
from crawl4ai import AsyncWebCrawler
import asyncio

async def test_extraction():
    async with AsyncWebCrawler() as crawler:
        result = await crawler.arun('https://example.com')
        print(f'Content length: {len(result.cleaned_html)}')
        print(f'Text length: {len(result.extracted_content)}')

asyncio.run(test_extraction())
"

# 4. Check for content behind login
# May need authentication setup

# 5. Try different CSS selectors
# For specific content extraction
```

## Search and RAG Problems

### Issue: Search Returns No Results

**Symptoms:**
- RAG queries return empty results
- "No sources found" errors
- Search always returns 0 results

**Solutions:**

```bash
# 1. Check if data exists
curl -X POST http://localhost:8051/tools/get_available_sources \
     -H "Content-Type: application/json" -d '{}'

# 2. Test database directly
psql "$SUPABASE_URL" -c "SELECT COUNT(*) FROM crawled_pages;"

# 3. Check embeddings
psql "$SUPABASE_URL" -c "SELECT COUNT(*) FROM crawled_pages WHERE embedding IS NOT NULL;"

# 4. Test search with simple query
curl -X POST http://localhost:8051/tools/perform_rag_query \
     -H "Content-Type: application/json" \
     -d '{"query": "test", "count": 10}'

# 5. Check search similarity threshold
# Lower threshold for more results
SEARCH_SIMILARITY_THRESHOLD=0.3
```

### Issue: Search Results Are Irrelevant

**Symptoms:**
- Search returns unrelated content
- Low similarity scores
- Poor search quality

**Solutions:**

```bash
# 1. Enable hybrid search
USE_HYBRID_SEARCH=true

# 2. Enable reranking
USE_RERANKING=true

# 3. Test with different embedding models
EMBEDDING_STRATEGY=openai
OPENAI_EMBEDDING_MODEL=text-embedding-3-small

# 4. Check query preprocessing
# Enable contextual embeddings
USE_CONTEXTUAL_EMBEDDINGS=true

# 5. Adjust similarity threshold
SEARCH_SIMILARITY_THRESHOLD=0.5
```

### Issue: Embedding Generation Fails

**Symptoms:**
- "Embedding generation failed" errors
- Content stored without embeddings
- Search not working due to missing embeddings

**Solutions:**

```bash
# 1. Test embedding API directly
curl -X POST "https://api.openai.com/v1/embeddings" \
     -H "Authorization: Bearer $OPENAI_API_KEY" \
     -H "Content-Type: application/json" \
     -d '{"input": "test text", "model": "text-embedding-3-small"}'

# 2. Check embedding model availability
# Try different models
OPENAI_EMBEDDING_MODEL=text-embedding-ada-002

# 3. Test with Ollama (alternative)
EMBEDDING_STRATEGY=ollama
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_EMBEDDING_MODEL=nomic-embed-text

# 4. Check content length
# Ensure content isn't too long for embedding
MAX_CONTENT_LENGTH=8000

# 5. Retry embedding generation
# For existing content without embeddings
```

## Diagnostic Tools

### Health Check Script

```bash
#!/bin/bash
# health-check.sh
# Comprehensive health check for Crawl4AI RAG MCP server

echo "=== Crawl4AI RAG MCP Server Health Check ==="
echo "Timestamp: $(date)"
echo

# 1. Server Health
echo "1. Server Health:"
HEALTH_RESPONSE=$(curl -s -f http://localhost:8051/health 2>/dev/null)
if [ $? -eq 0 ]; then
    echo "   ✅ Server is responding"
    echo "   Response: $HEALTH_RESPONSE"
else
    echo "   ❌ Server is not responding"
fi
echo

# 2. Docker Services
echo "2. Docker Services:"
docker-compose ps --format "table {{.Name}}\t{{.State}}\t{{.Status}}"
echo

# 3. Resource Usage
echo "3. Resource Usage:"
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"
echo

# 4. Database Connection
echo "4. Database Connection:"
if docker-compose exec -T crawl4ai-rag-mcp python -c "
import os
from supabase import create_client, Client
try:
    client = create_client(os.getenv('SUPABASE_URL'), os.getenv('SUPABASE_SERVICE_KEY'))
    result = client.table('sources').select('*').limit(1).execute()
    print('✅ Database connection successful')
except Exception as e:
    print(f'❌ Database connection failed: {e}')
" 2>/dev/null; then
    echo "   Database test completed"
else
    echo "   ❌ Database test failed"
fi
echo

# 5. API Keys
echo "5. API Keys:"
if docker-compose exec -T crawl4ai-rag-mcp python -c "
import os
import openai
try:
    client = openai.OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
    models = client.models.list()
    print('✅ OpenAI API key valid')
except Exception as e:
    print(f'❌ OpenAI API key invalid: {e}')
" 2>/dev/null; then
    echo "   API key test completed"
else
    echo "   ❌ API key test failed"
fi
echo

# 6. MCP Tools
echo "6. MCP Tools:"
TOOLS_RESPONSE=$(curl -s -X POST http://localhost:8051/tools/list -H "Content-Type: application/json" 2>/dev/null)
if [ $? -eq 0 ]; then
    echo "   ✅ MCP tools are available"
    echo "   Tools count: $(echo $TOOLS_RESPONSE | jq '. | length' 2>/dev/null || echo 'N/A')"
else
    echo "   ❌ MCP tools are not available"
fi
echo

# 7. Recent Logs
echo "7. Recent Logs (last 10 lines):"
docker-compose logs --tail=10 crawl4ai-rag-mcp
echo

echo "=== Health Check Complete ==="
```

### Log Analysis Script

```bash
#!/bin/bash
# log-analysis.sh
# Analyze logs for common issues

echo "=== Log Analysis ==="
echo "Timestamp: $(date)"
echo

# 1. Error Analysis
echo "1. Error Analysis:"
ERROR_COUNT=$(docker-compose logs --since=24h | grep -i error | wc -l)
echo "   Total errors (24h): $ERROR_COUNT"

if [ $ERROR_COUNT -gt 0 ]; then
    echo "   Recent errors:"
    docker-compose logs --since=24h | grep -i error | tail -5
fi
echo

# 2. Warning Analysis
echo "2. Warning Analysis:"
WARNING_COUNT=$(docker-compose logs --since=24h | grep -i warning | wc -l)
echo "   Total warnings (24h): $WARNING_COUNT"

if [ $WARNING_COUNT -gt 0 ]; then
    echo "   Recent warnings:"
    docker-compose logs --since=24h | grep -i warning | tail -5
fi
echo

# 3. Performance Analysis
echo "3. Performance Analysis:"
SLOW_QUERIES=$(docker-compose logs --since=24h | grep -i "slow" | wc -l)
echo "   Slow queries (24h): $SLOW_QUERIES"

TIMEOUTS=$(docker-compose logs --since=24h | grep -i "timeout" | wc -l)
echo "   Timeouts (24h): $TIMEOUTS"
echo

# 4. Database Analysis
echo "4. Database Analysis:"
DB_ERRORS=$(docker-compose logs --since=24h | grep -i "database\|supabase" | grep -i error | wc -l)
echo "   Database errors (24h): $DB_ERRORS"
echo

# 5. Memory Analysis
echo "5. Memory Analysis:"
OOM_ERRORS=$(docker-compose logs --since=24h | grep -i "out of memory\|oom" | wc -l)
echo "   Out of memory errors (24h): $OOM_ERRORS"
echo

echo "=== Log Analysis Complete ==="
```

### Performance Monitoring Script

```bash
#!/bin/bash
# performance-monitor.sh
# Monitor server performance

echo "=== Performance Monitoring ==="
echo "Timestamp: $(date)"
echo

# 1. Response Time Test
echo "1. Response Time Test:"
for i in {1..5}; do
    RESPONSE_TIME=$(curl -o /dev/null -s -w "%{time_total}" http://localhost:8051/health)
    echo "   Request $i: ${RESPONSE_TIME}s"
done
echo

# 2. Memory Usage Over Time
echo "2. Memory Usage Monitor (30 seconds):"
for i in {1..6}; do
    MEMORY_USAGE=$(docker stats --no-stream --format "{{.MemUsage}}" crawl4ai-rag-mcp 2>/dev/null)
    echo "   $(date '+%H:%M:%S'): $MEMORY_USAGE"
    sleep 5
done
echo

# 3. Database Performance
echo "3. Database Performance:"
docker-compose exec -T crawl4ai-rag-mcp python -c "
import time
import os
from supabase import create_client

client = create_client(os.getenv('SUPABASE_URL'), os.getenv('SUPABASE_SERVICE_KEY'))

# Test query performance
start_time = time.time()
result = client.table('crawled_pages').select('*').limit(10).execute()
end_time = time.time()

print(f'   Query time: {end_time - start_time:.3f}s')
print(f'   Results: {len(result.data)} rows')
" 2>/dev/null
echo

echo "=== Performance Monitoring Complete ==="
```

## Monitoring and Maintenance

### Daily Maintenance Script

```bash
#!/bin/bash
# daily-maintenance.sh
# Daily maintenance tasks

echo "=== Daily Maintenance ==="
echo "Date: $(date)"
echo

# 1. Health Check
echo "1. Running health check..."
./health-check.sh > /tmp/health-check-$(date +%Y%m%d).log
echo "   Health check completed"

# 2. Log Rotation
echo "2. Log rotation..."
docker-compose exec crawl4ai-rag-mcp find /app/logs -name "*.log" -size +100M -exec gzip {} \;
echo "   Log rotation completed"

# 3. Database Cleanup
echo "3. Database cleanup..."
docker-compose exec -T crawl4ai-rag-mcp python -c "
import os
from supabase import create_client
from datetime import datetime, timedelta

client = create_client(os.getenv('SUPABASE_URL'), os.getenv('SUPABASE_SERVICE_KEY'))

# Delete old crawled pages (older than 30 days)
cutoff_date = (datetime.now() - timedelta(days=30)).isoformat()
result = client.table('crawled_pages').delete().lt('created_at', cutoff_date).execute()
print(f'   Deleted {len(result.data)} old records')
" 2>/dev/null

# 4. Docker Cleanup
echo "4. Docker cleanup..."
docker system prune -f
docker volume prune -f
echo "   Docker cleanup completed"

# 5. Backup
echo "5. Creating backup..."
./backup-script.sh
echo "   Backup completed"

echo "=== Daily Maintenance Complete ==="
```

### Backup Script

```bash
#!/bin/bash
# backup-script.sh
# Create backup of configuration and data

BACKUP_DIR="/backups/$(date +%Y%m%d)"
mkdir -p $BACKUP_DIR

echo "=== Backup Script ==="
echo "Backup directory: $BACKUP_DIR"

# 1. Configuration backup
echo "1. Backing up configuration..."
cp .env $BACKUP_DIR/
cp docker-compose.yml $BACKUP_DIR/
cp docker-compose.prod.yml $BACKUP_DIR/
echo "   Configuration backup completed"

# 2. Database backup
echo "2. Backing up database..."
docker-compose exec -T crawl4ai-rag-mcp python -c "
import os
import json
from supabase import create_client

client = create_client(os.getenv('SUPABASE_URL'), os.getenv('SUPABASE_SERVICE_KEY'))

# Backup sources
sources = client.table('sources').select('*').execute()
with open('/tmp/sources_backup.json', 'w') as f:
    json.dump(sources.data, f, indent=2)

# Backup sample of crawled pages
pages = client.table('crawled_pages').select('*').limit(1000).execute()
with open('/tmp/pages_backup.json', 'w') as f:
    json.dump(pages.data, f, indent=2)

print('Database backup completed')
" 2>/dev/null

docker cp crawl4ai-rag-mcp:/tmp/sources_backup.json $BACKUP_DIR/
docker cp crawl4ai-rag-mcp:/tmp/pages_backup.json $BACKUP_DIR/

# 3. Logs backup
echo "3. Backing up logs..."
docker-compose logs --since=24h > $BACKUP_DIR/docker-logs.txt
echo "   Logs backup completed"

# 4. Compress backup
echo "4. Compressing backup..."
tar -czf $BACKUP_DIR.tar.gz $BACKUP_DIR
rm -rf $BACKUP_DIR
echo "   Backup compressed: $BACKUP_DIR.tar.gz"

echo "=== Backup Complete ==="
```

## Recovery Procedures

### Disaster Recovery Plan

#### 1. Complete System Recovery

```bash
#!/bin/bash
# disaster-recovery.sh
# Complete system recovery procedure

echo "=== Disaster Recovery ==="
echo "Timestamp: $(date)"
echo

# Stop all services
echo "1. Stopping all services..."
docker-compose down
docker system prune -f

# Restore configuration
echo "2. Restoring configuration..."
BACKUP_FILE="/backups/$(date +%Y%m%d).tar.gz"
if [ -f "$BACKUP_FILE" ]; then
    tar -xzf $BACKUP_FILE
    cp backups/$(date +%Y%m%d)/.env .
    cp backups/$(date +%Y%m%d)/docker-compose.yml .
    echo "   Configuration restored"
else
    echo "   ❌ Backup file not found: $BACKUP_FILE"
    exit 1
fi

# Rebuild containers
echo "3. Rebuilding containers..."
docker-compose build --no-cache
docker-compose up -d

# Wait for services to start
echo "4. Waiting for services to start..."
sleep 30

# Test system health
echo "5. Testing system health..."
./health-check.sh

echo "=== Disaster Recovery Complete ==="
```

#### 2. Database Recovery

```bash
#!/bin/bash
# database-recovery.sh
# Database recovery procedure

echo "=== Database Recovery ==="

# 1. Recreate database schema
echo "1. Recreating database schema..."
psql "$SUPABASE_URL" -f crawled_pages.sql

# 2. Restore sources
echo "2. Restoring sources..."
docker-compose exec -T crawl4ai-rag-mcp python -c "
import os
import json
from supabase import create_client

client = create_client(os.getenv('SUPABASE_URL'), os.getenv('SUPABASE_SERVICE_KEY'))

# Load backup
with open('/tmp/sources_backup.json', 'r') as f:
    sources = json.load(f)

# Restore sources
for source in sources:
    client.table('sources').insert(source).execute()

print('Sources restored')
" 2>/dev/null

# 3. Verify restoration
echo "3. Verifying restoration..."
docker-compose exec -T crawl4ai-rag-mcp python -c "
import os
from supabase import create_client

client = create_client(os.getenv('SUPABASE_URL'), os.getenv('SUPABASE_SERVICE_KEY'))

sources = client.table('sources').select('*').execute()
pages = client.table('crawled_pages').select('*').execute()

print(f'Sources: {len(sources.data)}')
print(f'Pages: {len(pages.data)}')
" 2>/dev/null

echo "=== Database Recovery Complete ==="
```

#### 3. Configuration Recovery

```bash
#!/bin/bash
# config-recovery.sh
# Configuration recovery procedure

echo "=== Configuration Recovery ==="

# 1. Restore environment variables
echo "1. Restoring environment variables..."
if [ -f "backup/.env" ]; then
    cp backup/.env .
    echo "   Environment variables restored"
else
    echo "   ❌ Environment backup not found"
    echo "   Please manually configure .env file"
fi

# 2. Restore Docker Compose configuration
echo "2. Restoring Docker Compose configuration..."
if [ -f "backup/docker-compose.yml" ]; then
    cp backup/docker-compose.yml .
    echo "   Docker Compose configuration restored"
fi

# 3. Restore SSL certificates
echo "3. Restoring SSL certificates..."
if [ -d "backup/ssl" ]; then
    sudo cp -r backup/ssl/* /etc/ssl/certs/
    echo "   SSL certificates restored"
fi

# 4. Validate configuration
echo "4. Validating configuration..."
docker-compose config
if [ $? -eq 0 ]; then
    echo "   ✅ Configuration is valid"
else
    echo "   ❌ Configuration is invalid"
fi

echo "=== Configuration Recovery Complete ==="
```

---

**Troubleshooting Guide Version**: 2.0  
**Last Updated**: 2025-01-17  
**Compatible Server Version**: 1.0+  
**Next Review**: 2025-02-17  

For setup instructions, see the [Complete Setup Guide](COMPLETE_SETUP_GUIDE.md).  
For integration guidance, refer to the [Integration Guide](INTEGRATION_GUIDE.md).  
For security considerations, consult the [Security Guide](SECURITY_GUIDE.md).  

**Emergency Contact**: For critical issues, check the server logs first, then consult this guide for step-by-step resolution procedures.