-- Evaluation Tables for Embedding Model Comparison
-- Schema for testing and benchmarking different embedding strategies

-- Enable pgvector extension if not already enabled
CREATE EXTENSION IF NOT EXISTS vector;

-- Evaluation test runs metadata
CREATE TABLE IF NOT EXISTS eval_test_runs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    test_name VARCHAR(255) NOT NULL,
    source_url TEXT NOT NULL,
    embedding_model VARCHAR(50) NOT NULL, -- 'ollama' or 'openai'
    embedding_strategy VARCHAR(100) NOT NULL, -- specific model name
    vector_dimensions INTEGER NOT NULL,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    status VARCHAR(20) DEFAULT 'running', -- 'running', 'completed', 'failed'
    
    -- Performance metrics
    crawl_duration_seconds DECIMAL(10,3),
    embedding_duration_seconds DECIMAL(10,3),
    total_duration_seconds DECIMAL(10,3),
    pages_crawled INTEGER DEFAULT 0,
    chunks_created INTEGER DEFAULT 0,
    total_tokens INTEGER DEFAULT 0,
    
    -- Metadata
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Evaluation sources (similar to main sources table)
CREATE TABLE IF NOT EXISTS eval_sources (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    test_run_id UUID REFERENCES eval_test_runs(id) ON DELETE CASCADE,
    domain VARCHAR(255) NOT NULL,
    base_url TEXT NOT NULL,
    title TEXT,
    description TEXT,
    total_pages INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Evaluation crawled pages with vectors (similar to main crawled_pages table)
CREATE TABLE IF NOT EXISTS eval_crawled_pages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    test_run_id UUID REFERENCES eval_test_runs(id) ON DELETE CASCADE,
    source_id UUID REFERENCES eval_sources(id) ON DELETE CASCADE,
    url TEXT NOT NULL,
    title TEXT,
    content TEXT NOT NULL,
    content_hash VARCHAR(64),
    chunk_index INTEGER NOT NULL,
    chunk_text TEXT NOT NULL,
    embedding vector, -- Dynamic dimensions based on model
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Auto-generated test queries for search performance testing
CREATE TABLE IF NOT EXISTS eval_test_queries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    test_run_id UUID REFERENCES eval_test_runs(id) ON DELETE CASCADE,
    query_text TEXT NOT NULL,
    query_type VARCHAR(50) DEFAULT 'auto_generated', -- 'auto_generated', 'manual'
    query_context TEXT, -- Why this query was generated
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Search performance results for each query
CREATE TABLE IF NOT EXISTS eval_search_results (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    test_run_id UUID REFERENCES eval_test_runs(id) ON DELETE CASCADE,
    query_id UUID REFERENCES eval_test_queries(id) ON DELETE CASCADE,
    search_duration_ms DECIMAL(8,3) NOT NULL,
    results_count INTEGER NOT NULL,
    top_similarity_score DECIMAL(5,4), -- Best match score
    avg_similarity_score DECIMAL(5,4), -- Average of top 5 matches
    search_method VARCHAR(50) DEFAULT 'vector', -- 'vector', 'hybrid'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_eval_test_runs_status ON eval_test_runs(status);
CREATE INDEX IF NOT EXISTS idx_eval_test_runs_embedding_model ON eval_test_runs(embedding_model);
CREATE INDEX IF NOT EXISTS idx_eval_test_runs_created_at ON eval_test_runs(created_at);

CREATE INDEX IF NOT EXISTS idx_eval_crawled_pages_test_run ON eval_crawled_pages(test_run_id);
CREATE INDEX IF NOT EXISTS idx_eval_crawled_pages_source ON eval_crawled_pages(source_id);
CREATE INDEX IF NOT EXISTS idx_eval_crawled_pages_url ON eval_crawled_pages(url);

CREATE INDEX IF NOT EXISTS idx_eval_test_queries_test_run ON eval_test_queries(test_run_id);
CREATE INDEX IF NOT EXISTS idx_eval_search_results_test_run ON eval_search_results(test_run_id);
CREATE INDEX IF NOT EXISTS idx_eval_search_results_query ON eval_search_results(query_id);

-- Function to calculate vector index for eval tables
CREATE OR REPLACE FUNCTION create_eval_vector_index(vector_dims INTEGER)
RETURNS TEXT
LANGUAGE plpgsql
AS $$
DECLARE
    index_name TEXT;
BEGIN
    index_name := 'idx_eval_crawled_pages_embedding_' || vector_dims;
    
    -- Drop existing index if dimensions changed
    EXECUTE format('DROP INDEX IF EXISTS %I', index_name);
    
    -- Create new index with specified dimensions
    EXECUTE format(
        'CREATE INDEX %I ON eval_crawled_pages USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100)',
        index_name
    );
    
    RETURN index_name;
END;
$$;

-- Cleanup function to remove all evaluation data
CREATE OR REPLACE FUNCTION cleanup_eval_data()
RETURNS INTEGER
LANGUAGE plpgsql
AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM eval_test_runs;
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Reset sequences if needed
    -- Note: UUIDs don't need sequence reset
    
    RETURN deleted_count;
END;
$$;