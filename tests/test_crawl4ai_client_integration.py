"""
Integration tests for Crawl4AI client HTTP communication.

These tests focus on actual HTTP communication patterns and error handling
between the MCP server and Crawl4AI worker service.
"""

import pytest
import asyncio
import aiohttp
from unittest.mock import Mock, AsyncMock, patch
from typing import Dict, Any, List

# Import test target
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent / "src"))

from crawl4ai_client import Crawl4AIClient, AsyncWebCrawler
from exceptions import (
    ValidationError,
    NetworkError,
    JobError,
    TimeoutError,
    ServiceUnavailableError
)


class TestCrawl4AIClientIntegration:
    """Integration tests for Crawl4AI client HTTP operations."""
    
    @pytest.fixture
    def client(self):
        """Create client instance for testing."""
        return Crawl4AIClient(host="localhost", port=11235)
    
    @pytest.fixture
    def mock_session_response(self):
        """Create mock session response helper."""
        def create_response(status: int, json_data: Dict[str, Any] = None, text_data: str = ""):
            mock_response = AsyncMock()
            mock_response.status = status
            mock_response.json = AsyncMock(return_value=json_data or {})
            mock_response.text = AsyncMock(return_value=text_data)
            return mock_response
        return create_response
    
    @pytest.mark.asyncio
    async def test_submit_crawl_job_success(self, client, mock_session_response):
        """Test successful job submission."""
        # Mock successful response
        expected_task_id = "12345678-1234-5678-9012-123456789abc"
        mock_response = mock_session_response(200, {"task_id": expected_task_id})
        
        with patch('aiohttp.ClientSession') as mock_session_class:
            mock_session = AsyncMock()
            mock_session_class.return_value.__aenter__.return_value = mock_session
            mock_session.post.return_value.__aenter__.return_value = mock_response
            
            # Test job submission
            urls = ["https://example.com", "https://test.com"]
            task_id = await client.submit_crawl_job(urls)
            
            assert task_id == expected_task_id
            
            # Verify HTTP call was made correctly
            mock_session.post.assert_called_once()
            call_args = mock_session.post.call_args
            assert call_args[0][0] == "http://localhost:11235/crawl/job"
            assert call_args[1]["json"]["urls"] == urls
    
    @pytest.mark.asyncio
    async def test_submit_crawl_job_validation_error(self, client):
        """Test job submission with invalid URLs."""
        with pytest.raises(ValidationError) as exc_info:
            await client.submit_crawl_job(["not-a-url", "also-invalid"])
        
        assert "Invalid or unsafe URL format" in str(exc_info.value)
        assert exc_info.value.error_code == "VALIDATION_ERROR"
    
    @pytest.mark.asyncio
    async def test_submit_crawl_job_empty_urls(self, client):
        """Test job submission with empty URL list."""
        with pytest.raises(ValidationError) as exc_info:
            await client.submit_crawl_job([])
        
        assert "URL list cannot be empty" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_submit_crawl_job_too_many_urls(self, client):
        """Test job submission with too many URLs."""
        urls = [f"https://example{i}.com" for i in range(101)]  # 101 URLs (max is 100)
        
        with pytest.raises(ValidationError) as exc_info:
            await client.submit_crawl_job(urls)
        
        assert "Too many URLs" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_submit_crawl_job_http_error(self, client, mock_session_response):
        """Test job submission with HTTP error response."""
        mock_response = mock_session_response(500, text_data="Internal Server Error")
        
        with patch('aiohttp.ClientSession') as mock_session_class:
            mock_session = AsyncMock()
            mock_session_class.return_value.__aenter__.return_value = mock_session
            mock_session.post.return_value.__aenter__.return_value = mock_response
            
            with pytest.raises(NetworkError) as exc_info:
                await client.submit_crawl_job(["https://example.com"])
            
            assert exc_info.value.context["status_code"] == 500
            assert exc_info.value.error_code == "NETWORK_ERROR"
    
    @pytest.mark.asyncio
    async def test_submit_crawl_job_missing_task_id(self, client, mock_session_response):
        """Test job submission with missing task_id in response."""
        mock_response = mock_session_response(200, {"status": "submitted"})  # No task_id
        
        with patch('aiohttp.ClientSession') as mock_session_class:
            mock_session = AsyncMock()
            mock_session_class.return_value.__aenter__.return_value = mock_session
            mock_session.post.return_value.__aenter__.return_value = mock_response
            
            with pytest.raises(JobError) as exc_info:
                await client.submit_crawl_job(["https://example.com"])
            
            assert "No task_id in response" in str(exc_info.value)
            assert exc_info.value.error_code == "JOB_ERROR"
    
    @pytest.mark.asyncio
    async def test_get_job_status_success(self, client, mock_session_response):
        """Test successful job status retrieval."""
        job_id = "12345678-1234-5678-9012-123456789abc"
        expected_status = {
            "status": "COMPLETED",
            "progress": 100.0,
            "result": {"crawl_results": [{"url": "https://example.com", "content": "test"}]}
        }
        
        mock_response = mock_session_response(200, expected_status)
        
        with patch('aiohttp.ClientSession') as mock_session_class:
            mock_session = AsyncMock()
            mock_session_class.return_value.__aenter__.return_value = mock_session
            mock_session.get.return_value.__aenter__.return_value = mock_response
            
            status = await client.get_job_status(job_id)
            
            assert status == expected_status
            mock_session.get.assert_called_once_with(f"http://localhost:11235/crawl/job/{job_id}")
    
    @pytest.mark.asyncio
    async def test_get_job_status_invalid_job_id(self, client):
        """Test job status with invalid job ID format."""
        with pytest.raises(ValidationError) as exc_info:
            await client.get_job_status("invalid-job-id")
        
        assert "Job ID must be a valid UUID format" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_get_job_status_not_found(self, client, mock_session_response):
        """Test job status for non-existent job."""
        job_id = "12345678-1234-5678-9012-123456789abc"
        mock_response = mock_session_response(404)
        
        with patch('aiohttp.ClientSession') as mock_session_class:
            mock_session = AsyncMock()
            mock_session_class.return_value.__aenter__.return_value = mock_session
            mock_session.get.return_value.__aenter__.return_value = mock_response
            
            with pytest.raises(JobError) as exc_info:
                await client.get_job_status(job_id)
            
            assert "Job not found" in str(exc_info.value)
            assert exc_info.value.context["job_id"] == job_id
    
    @pytest.mark.asyncio
    async def test_wait_for_job_completion_success(self, client, mock_session_response):
        """Test successful job completion waiting."""
        job_id = "12345678-1234-5678-9012-123456789abc"
        
        # Mock responses: first RUNNING, then COMPLETED
        running_response = mock_session_response(200, {"status": "RUNNING", "progress": 50.0})
        completed_response = mock_session_response(200, {
            "status": "COMPLETED",
            "result": {"crawl_results": [{"url": "https://example.com", "content": "test"}]}
        })
        
        with patch('aiohttp.ClientSession') as mock_session_class:
            mock_session = AsyncMock()
            mock_session_class.return_value.__aenter__.return_value = mock_session
            # First call returns RUNNING, second returns COMPLETED
            mock_session.get.return_value.__aenter__.side_effect = [running_response, completed_response]
            
            result = await client.wait_for_job_completion(job_id, poll_interval=0.1, max_wait=30)\n            \n            assert result[\"crawl_results\"][0][\"content\"] == \"test\"\n            assert mock_session.get.call_count == 2\n    \n    @pytest.mark.asyncio\n    async def test_wait_for_job_completion_timeout(self, client, mock_session_response):\n        \"\"\"Test job completion waiting with timeout.\"\"\"\n        job_id = \"12345678-1234-5678-9012-123456789abc\"\n        \n        # Mock response that always returns RUNNING\n        running_response = mock_session_response(200, {\"status\": \"RUNNING\", \"progress\": 50.0})\n        \n        with patch('aiohttp.ClientSession') as mock_session_class:\n            mock_session = AsyncMock()\n            mock_session_class.return_value.__aenter__.return_value = mock_session\n            mock_session.get.return_value.__aenter__.return_value = running_response\n            \n            with pytest.raises(TimeoutError) as exc_info:\n                await client.wait_for_job_completion(job_id, poll_interval=0.1, max_wait=0.2)\n            \n            assert f\"job {job_id} completion wait\" in str(exc_info.value)\n            assert exc_info.value.context[\"timeout_duration\"] == 0.2\n    \n    @pytest.mark.asyncio\n    async def test_wait_for_job_completion_failed_job(self, client, mock_session_response):\n        \"\"\"Test job completion waiting for failed job.\"\"\"\n        job_id = \"12345678-1234-5678-9012-123456789abc\"\n        \n        failed_response = mock_session_response(200, {\n            \"status\": \"FAILED\",\n            \"error\": \"Network connection failed\"\n        })\n        \n        with patch('aiohttp.ClientSession') as mock_session_class:\n            mock_session = AsyncMock()\n            mock_session_class.return_value.__aenter__.return_value = mock_session\n            mock_session.get.return_value.__aenter__.return_value = failed_response\n            \n            with pytest.raises(JobError) as exc_info:\n                await client.wait_for_job_completion(job_id, poll_interval=0.1, max_wait=30)\n            \n            assert \"Job failed: Network connection failed\" in str(exc_info.value)\n            assert exc_info.value.context[\"job_id\"] == job_id\n    \n    @pytest.mark.asyncio\n    async def test_health_check_success(self, client, mock_session_response):\n        \"\"\"Test successful health check.\"\"\"\n        mock_response = mock_session_response(200)\n        \n        with patch('aiohttp.ClientSession') as mock_session_class:\n            mock_session = AsyncMock()\n            mock_session_class.return_value.__aenter__.return_value = mock_session\n            mock_session.get.return_value.__aenter__.return_value = mock_response\n            \n            is_healthy = await client.health_check()\n            \n            assert is_healthy is True\n            mock_session.get.assert_called_once_with(\"http://localhost:11235/health\")\n    \n    @pytest.mark.asyncio\n    async def test_health_check_failure(self, client):\n        \"\"\"Test health check with connection failure.\"\"\"\n        with patch('aiohttp.ClientSession') as mock_session_class:\n            mock_session = AsyncMock()\n            mock_session_class.return_value.__aenter__.return_value = mock_session\n            mock_session.get.side_effect = aiohttp.ClientConnectorError(\"Connection failed\", None)\n            \n            is_healthy = await client.health_check()\n            \n            assert is_healthy is False\n    \n    @pytest.mark.asyncio\n    async def test_crawl_urls_end_to_end(self, client, mock_session_response):\n        \"\"\"Test complete crawl URLs workflow.\"\"\"\n        urls = [\"https://example.com\"]\n        job_id = \"12345678-1234-5678-9012-123456789abc\"\n        \n        # Mock job submission\n        submit_response = mock_session_response(200, {\"task_id\": job_id})\n        \n        # Mock job completion\n        completed_response = mock_session_response(200, {\n            \"status\": \"COMPLETED\",\n            \"result\": {\n                \"crawl_results\": [\n                    {\"url\": \"https://example.com\", \"content\": \"Example content\", \"success\": True}\n                ]\n            }\n        })\n        \n        with patch('aiohttp.ClientSession') as mock_session_class:\n            mock_session = AsyncMock()\n            mock_session_class.return_value.__aenter__.return_value = mock_session\n            # First POST for submission, then GET for status\n            mock_session.post.return_value.__aenter__.return_value = submit_response\n            mock_session.get.return_value.__aenter__.return_value = completed_response\n            \n            results = await client.crawl_urls(urls)\n            \n            assert len(results) == 1\n            assert results[0][\"url\"] == \"https://example.com\"\n            assert results[0][\"content\"] == \"Example content\"\n            \n            # Verify both submission and status check were called\n            mock_session.post.assert_called_once()\n            mock_session.get.assert_called_once()\n\n\nclass TestAsyncWebCrawlerIntegration:\n    \"\"\"Integration tests for AsyncWebCrawler compatibility layer.\"\"\"\n    \n    @pytest.mark.asyncio\n    async def test_context_manager_success(self, mock_session_response):\n        \"\"\"Test AsyncWebCrawler context manager with healthy service.\"\"\"\n        # Mock health check success\n        health_response = mock_session_response(200)\n        \n        with patch('aiohttp.ClientSession') as mock_session_class:\n            mock_session = AsyncMock()\n            mock_session_class.return_value.__aenter__.return_value = mock_session\n            mock_session.get.return_value.__aenter__.return_value = health_response\n            \n            async with AsyncWebCrawler() as crawler:\n                assert crawler is not None\n                # Health check should have been called\n                mock_session.get.assert_called_once_with(\"http://localhost:11235/health\")\n    \n    @pytest.mark.asyncio\n    async def test_context_manager_service_unavailable(self):\n        \"\"\"Test AsyncWebCrawler context manager with unhealthy service.\"\"\"\n        with patch('aiohttp.ClientSession') as mock_session_class:\n            mock_session = AsyncMock()\n            mock_session_class.return_value.__aenter__.return_value = mock_session\n            mock_session.get.side_effect = aiohttp.ClientConnectorError(\"Connection failed\", None)\n            \n            with pytest.raises(ServiceUnavailableError) as exc_info:\n                async with AsyncWebCrawler() as crawler:\n                    pass\n            \n            assert \"Crawl4AI worker\" in str(exc_info.value)\n            assert exc_info.value.context[\"service_url\"] == \"http://localhost:11235\"\n    \n    @pytest.mark.asyncio\n    async def test_arun_single_url(self, mock_session_response):\n        \"\"\"Test crawling single URL through compatibility layer.\"\"\"\n        url = \"https://example.com\"\n        job_id = \"12345678-1234-5678-9012-123456789abc\"\n        \n        # Mock health check\n        health_response = mock_session_response(200)\n        \n        # Mock job submission\n        submit_response = mock_session_response(200, {\"task_id\": job_id})\n        \n        # Mock job completion\n        completed_response = mock_session_response(200, {\n            \"status\": \"COMPLETED\",\n            \"result\": {\n                \"crawl_results\": [\n                    {\n                        \"url\": url,\n                        \"success\": True,\n                        \"html\": \"<html><body>Test</body></html>\",\n                        \"markdown\": \"# Test\",\n                        \"metadata\": {\"title\": \"Test Page\"}\n                    }\n                ]\n            }\n        })\n        \n        with patch('aiohttp.ClientSession') as mock_session_class:\n            mock_session = AsyncMock()\n            mock_session_class.return_value.__aenter__.return_value = mock_session\n            \n            # Set up response sequence: health, submit, status\n            mock_session.get.return_value.__aenter__.side_effect = [health_response, completed_response]\n            mock_session.post.return_value.__aenter__.return_value = submit_response\n            \n            async with AsyncWebCrawler() as crawler:\n                result = await crawler.arun(url)\n                \n                assert result.success is True\n                assert result.html == \"<html><body>Test</body></html>\"\n                assert result.markdown == \"# Test\"\n                assert result.metadata[\"title\"] == \"Test Page\"\n    \n    @pytest.mark.asyncio\n    async def test_arun_no_results(self, mock_session_response):\n        \"\"\"Test crawling with no results returned.\"\"\"\n        url = \"https://example.com\"\n        job_id = \"12345678-1234-5678-9012-123456789abc\"\n        \n        # Mock health check\n        health_response = mock_session_response(200)\n        \n        # Mock job submission\n        submit_response = mock_session_response(200, {\"task_id\": job_id})\n        \n        # Mock job completion with empty results\n        completed_response = mock_session_response(200, {\n            \"status\": \"COMPLETED\",\n            \"result\": {\"crawl_results\": []}\n        })\n        \n        with patch('aiohttp.ClientSession') as mock_session_class:\n            mock_session = AsyncMock()\n            mock_session_class.return_value.__aenter__.return_value = mock_session\n            \n            mock_session.get.return_value.__aenter__.side_effect = [health_response, completed_response]\n            mock_session.post.return_value.__aenter__.return_value = submit_response\n            \n            async with AsyncWebCrawler() as crawler:\n                result = await crawler.arun(url)\n                \n                assert result.success is False\n                assert \"No results returned\" in result.error_message"