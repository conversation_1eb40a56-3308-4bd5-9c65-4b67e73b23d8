"""
Comprehensive test suite for reliability features.

Tests cover:
- Retry mechanisms with exponential backoff
- Timeout management
- Circuit breaker pattern
- Caching mechanisms
- Health checks
- Error handling and classification
- Metrics collection
- Integration scenarios
"""

import asyncio
import pytest
import time
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timedelta
from typing import List, Dict, Any

# Import the reliability module
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent / "src"))

from reliability import (
    ReliableCrawler,
    CrawlResult,
    CrawlErrorType,
    RetryConfig,
    TimeoutConfig,
    CircuitBreakerConfig,
    CacheConfig,
    CircuitBreaker,
    CircuitBreakerState,
    CrawlCache,
    HealthChecker,
    MetricsCollector,
    create_default_reliable_crawler,
    create_production_reliable_crawler
)


class TestCrawlResult:
    """Test CrawlResult dataclass."""
    
    def test_crawl_result_creation(self):
        """Test creating CrawlResult instances."""
        result = CrawlResult(
            url="https://example.com",
            success=True,
            content="test content"
        )
        
        assert result.url == "https://example.com"
        assert result.success is True
        assert result.content == "test content"
        assert result.retry_count == 0
        assert isinstance(result.timestamp, datetime)
    
    def test_crawl_result_with_error(self):
        """Test CrawlResult with error information."""
        result = CrawlResult(
            url="https://example.com",
            success=False,
            error="Connection failed",
            error_type=CrawlErrorType.NETWORK_ERROR,
            retry_count=3
        )
        
        assert result.success is False
        assert result.error == "Connection failed"
        assert result.error_type == CrawlErrorType.NETWORK_ERROR
        assert result.retry_count == 3


class TestRetryConfig:
    """Test RetryConfig configuration."""
    
    def test_default_retry_config(self):
        """Test default retry configuration."""
        config = RetryConfig()
        
        assert config.max_retries == 3
        assert config.base_delay == 1.0
        assert config.max_delay == 60.0
        assert config.exponential_base == 2.0
        assert config.jitter is True
        assert CrawlErrorType.NETWORK_ERROR in config.retry_on_errors
    
    def test_custom_retry_config(self):
        """Test custom retry configuration."""
        config = RetryConfig(
            max_retries=5,
            base_delay=2.0,
            max_delay=120.0,
            retry_on_errors=[CrawlErrorType.TIMEOUT_ERROR]
        )
        
        assert config.max_retries == 5
        assert config.base_delay == 2.0
        assert config.max_delay == 120.0
        assert config.retry_on_errors == [CrawlErrorType.TIMEOUT_ERROR]


class TestCircuitBreaker:
    """Test Circuit Breaker implementation."""
    
    @pytest.fixture
    def circuit_breaker(self):
        """Create a circuit breaker for testing."""
        config = CircuitBreakerConfig(
            failure_threshold=3,
            recovery_timeout=1.0,
            success_threshold=2,
            enabled=True
        )
        return CircuitBreaker(config)
    
    @pytest.mark.asyncio
    async def test_circuit_breaker_success(self, circuit_breaker):
        """Test circuit breaker with successful operations."""
        async def successful_func():
            return "success"
        
        result = await circuit_breaker.call(successful_func)
        assert result == "success"
        assert circuit_breaker.state == CircuitBreakerState.CLOSED
        assert circuit_breaker.failure_count == 0
    
    @pytest.mark.asyncio
    async def test_circuit_breaker_failure_threshold(self, circuit_breaker):
        """Test circuit breaker opening after failure threshold."""
        async def failing_func():
            raise Exception("Test failure")
        
        # Fail enough times to open circuit breaker
        for i in range(3):
            with pytest.raises(Exception):
                await circuit_breaker.call(failing_func)
        
        assert circuit_breaker.state == CircuitBreakerState.OPEN
        assert circuit_breaker.failure_count == 3
        
        # Next call should raise circuit breaker exception
        with pytest.raises(Exception, match="Circuit breaker is OPEN"):
            await circuit_breaker.call(failing_func)
    
    @pytest.mark.asyncio
    async def test_circuit_breaker_recovery(self, circuit_breaker):
        """Test circuit breaker recovery after timeout."""
        async def failing_func():
            raise Exception("Test failure")
        
        async def successful_func():
            return "success"
        
        # Open the circuit breaker
        for i in range(3):
            with pytest.raises(Exception):
                await circuit_breaker.call(failing_func)
        
        assert circuit_breaker.state == CircuitBreakerState.OPEN
        
        # Wait for recovery timeout
        await asyncio.sleep(1.1)
        
        # Should transition to HALF_OPEN on next call
        result = await circuit_breaker.call(successful_func)
        assert result == "success"
        assert circuit_breaker.state == CircuitBreakerState.HALF_OPEN
        
        # Another successful call should close the circuit
        result = await circuit_breaker.call(successful_func)
        assert result == "success"
        assert circuit_breaker.state == CircuitBreakerState.CLOSED


class TestCrawlCache:
    """Test caching mechanism."""
    
    @pytest.fixture
    def cache(self):
        """Create a cache for testing."""
        config = CacheConfig(
            enabled=True,
            max_size=3,
            ttl_seconds=1
        )
        return CrawlCache(config)
    
    @pytest.mark.asyncio
    async def test_cache_set_and_get(self, cache):
        """Test basic cache operations."""
        url = "https://example.com"
        content = "test content"
        
        # Set content
        await cache.set(url, content)
        
        # Get content
        retrieved = await cache.get(url)
        assert retrieved == content
    
    @pytest.mark.asyncio
    async def test_cache_miss(self, cache):
        """Test cache miss scenario."""
        result = await cache.get("https://nonexistent.com")
        assert result is None
    
    @pytest.mark.asyncio
    async def test_cache_expiration(self, cache):
        """Test cache expiration."""
        url = "https://example.com"
        content = "test content"
        
        # Set content
        await cache.set(url, content)
        
        # Should retrieve immediately
        retrieved = await cache.get(url)
        assert retrieved == content
        
        # Wait for expiration
        await asyncio.sleep(1.1)
        
        # Should be expired
        retrieved = await cache.get(url)
        assert retrieved is None
    
    @pytest.mark.asyncio
    async def test_cache_lru_eviction(self, cache):
        """Test LRU eviction when cache is full."""
        # Fill cache to capacity
        for i in range(3):
            await cache.set(f"https://example{i}.com", f"content{i}")
        
        # All should be cached
        for i in range(3):
            retrieved = await cache.get(f"https://example{i}.com")
            assert retrieved == f"content{i}"
        
        # Add one more (should evict oldest)
        await cache.set("https://example3.com", "content3")
        
        # First entry should be evicted
        retrieved = await cache.get("https://example0.com")
        assert retrieved is None
        
        # Others should still be there
        for i in range(1, 4):
            retrieved = await cache.get(f"https://example{i}.com")
            assert retrieved == f"content{i}"


class TestHealthChecker:
    """Test health check functionality."""
    
    @pytest.fixture
    def health_checker(self):
        """Create a health checker for testing."""
        timeout_config = TimeoutConfig(connection_timeout=5.0)
        return HealthChecker(timeout_config)
    
    @pytest.mark.asyncio
    async def test_check_url_health_success(self, health_checker):
        """Test successful health check."""
        with patch('aiohttp.ClientSession') as mock_session:
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_context = AsyncMock()
            mock_context.__aenter__.return_value = mock_response
            mock_session.return_value.__aenter__.return_value.head.return_value = mock_context
            
            is_healthy, error = await health_checker.check_url_health("https://example.com")
            
            assert is_healthy is True
            assert error is None
    
    @pytest.mark.asyncio
    async def test_check_url_health_failure(self, health_checker):
        """Test failed health check."""
        with patch('aiohttp.ClientSession') as mock_session:
            mock_response = AsyncMock()
            mock_response.status = 500
            mock_context = AsyncMock()
            mock_context.__aenter__.return_value = mock_response
            mock_session.return_value.__aenter__.return_value.head.return_value = mock_context
            
            is_healthy, error = await health_checker.check_url_health("https://example.com")
            
            assert is_healthy is False
            assert error == "HTTP 500"
    
    @pytest.mark.asyncio
    async def test_check_url_health_timeout(self, health_checker):
        """Test health check timeout."""
        with patch('aiohttp.ClientSession') as mock_session:
            mock_session.return_value.__aenter__.return_value.head.side_effect = asyncio.TimeoutError()
            
            is_healthy, error = await health_checker.check_url_health("https://example.com")
            
            assert is_healthy is False
            assert error == "Connection timeout"


class TestMetricsCollector:
    """Test metrics collection."""
    
    @pytest.fixture
    def metrics_collector(self):
        """Create a metrics collector for testing."""
        return MetricsCollector()
    
    @pytest.mark.asyncio
    async def test_record_successful_request(self, metrics_collector):
        """Test recording successful request."""
        result = CrawlResult(
            url="https://example.com",
            success=True,
            content="test content",
            response_time_ms=100
        )
        
        await metrics_collector.record_request(result)
        
        metrics = await metrics_collector.get_metrics()
        assert metrics["total_requests"] == 1
        assert metrics["successful_requests"] == 1
        assert metrics["failed_requests"] == 0
        assert metrics["success_rate"] == 1.0
        assert 100 in metrics["response_times"]
    
    @pytest.mark.asyncio
    async def test_record_failed_request(self, metrics_collector):
        """Test recording failed request."""
        result = CrawlResult(
            url="https://example.com",
            success=False,
            error="Connection failed",
            error_type=CrawlErrorType.NETWORK_ERROR,
            retry_count=2,
            response_time_ms=5000
        )
        
        await metrics_collector.record_request(result)
        
        metrics = await metrics_collector.get_metrics()
        assert metrics["total_requests"] == 1
        assert metrics["successful_requests"] == 0
        assert metrics["failed_requests"] == 1
        assert metrics["failure_rate"] == 1.0
        assert metrics["error_types"]["network_error"] == 1
        assert metrics["retry_counts"]["2"] == 1
    
    @pytest.mark.asyncio
    async def test_metrics_aggregation(self, metrics_collector):
        """Test metrics aggregation over multiple requests."""
        # Record multiple requests
        for i in range(5):
            result = CrawlResult(
                url=f"https://example{i}.com",
                success=i % 2 == 0,  # Every other request fails
                response_time_ms=100 + i * 50
            )
            await metrics_collector.record_request(result)
        
        metrics = await metrics_collector.get_metrics()
        assert metrics["total_requests"] == 5
        assert metrics["successful_requests"] == 3
        assert metrics["failed_requests"] == 2
        assert metrics["success_rate"] == 0.6
        assert metrics["avg_response_time"] == 200.0  # (100+150+200+250+300)/5


class TestReliableCrawler:
    """Test main ReliableCrawler functionality."""
    
    @pytest.fixture
    def reliable_crawler(self):
        """Create a reliable crawler for testing."""
        return ReliableCrawler(
            retry_config=RetryConfig(max_retries=2, base_delay=0.1),
            timeout_config=TimeoutConfig(connection_timeout=1.0),
            circuit_breaker_config=CircuitBreakerConfig(failure_threshold=2, recovery_timeout=0.5),
            cache_config=CacheConfig(enabled=True, max_size=10, ttl_seconds=60)
        )
    
    def test_error_classification(self, reliable_crawler):
        """Test error classification."""
        # Test timeout error
        timeout_error = asyncio.TimeoutError()
        assert reliable_crawler._classify_error(timeout_error) == CrawlErrorType.TIMEOUT_ERROR
        
        # Test network error
        network_error = Exception("connection failed")
        assert reliable_crawler._classify_error(network_error) == CrawlErrorType.NETWORK_ERROR
        
        # Test unknown error
        unknown_error = Exception("unknown error")
        assert reliable_crawler._classify_error(unknown_error) == CrawlErrorType.UNKNOWN_ERROR
    
    def test_should_retry_logic(self, reliable_crawler):
        """Test retry decision logic."""
        # Should retry network errors
        assert reliable_crawler._should_retry(CrawlErrorType.NETWORK_ERROR, 0) is True
        assert reliable_crawler._should_retry(CrawlErrorType.NETWORK_ERROR, 1) is True
        assert reliable_crawler._should_retry(CrawlErrorType.NETWORK_ERROR, 2) is False  # max_retries=2
        
        # Should not retry authentication errors
        assert reliable_crawler._should_retry(CrawlErrorType.AUTHENTICATION_ERROR, 0) is False
    
    @pytest.mark.asyncio
    async def test_retry_delay_calculation(self, reliable_crawler):
        """Test retry delay calculation."""
        # Test exponential backoff
        delay0 = await reliable_crawler._calculate_retry_delay(0)
        delay1 = await reliable_crawler._calculate_retry_delay(1)
        delay2 = await reliable_crawler._calculate_retry_delay(2)
        
        # Should increase exponentially (allowing for jitter)
        assert delay0 >= 0.075  # 0.1 * 0.75 (with jitter)
        assert delay0 <= 0.125  # 0.1 * 1.25 (with jitter)
        
        assert delay1 >= 0.15   # 0.2 * 0.75
        assert delay1 <= 0.25   # 0.2 * 1.25
    
    @pytest.mark.asyncio
    async def test_successful_crawl(self, reliable_crawler):
        """Test successful crawl operation."""
        async def mock_crawl_func(url):
            return "crawled content"
        
        result = await reliable_crawler.crawl_url("https://example.com", mock_crawl_func)
        
        assert result.success is True
        assert result.content == "crawled content"
        assert result.retry_count == 0
        assert result.error is None
    
    @pytest.mark.asyncio
    async def test_crawl_with_retries(self, reliable_crawler):
        """Test crawl with retry logic."""
        call_count = 0
        
        async def mock_crawl_func(url):
            nonlocal call_count
            call_count += 1
            if call_count < 3:
                raise Exception("Network error")
            return "success after retries"
        
        # Mock health checker to pass
        with patch.object(reliable_crawler, '_perform_health_check', return_value=(True, None)):
            # Need to classify the error as a retryable type
            original_classify = reliable_crawler._classify_error
            def mock_classify(error):
                return CrawlErrorType.NETWORK_ERROR
            
            with patch.object(reliable_crawler, '_classify_error', side_effect=mock_classify):
                result = await reliable_crawler.crawl_url("https://example.com", mock_crawl_func)
        
        assert result.success is True
        assert result.content == "success after retries"
        assert result.retry_count == 2  # Failed twice, succeeded on third attempt
        assert call_count == 3
    
    @pytest.mark.asyncio
    async def test_crawl_failure_after_max_retries(self, reliable_crawler):
        """Test crawl failure after exhausting retries."""
        async def mock_crawl_func(url):
            raise Exception("Persistent network error")
        
        # Mock health checker to pass
        with patch.object(reliable_crawler, '_perform_health_check', return_value=(True, None)):
            result = await reliable_crawler.crawl_url("https://example.com", mock_crawl_func)
        
        assert result.success is False
        assert result.error == "Persistent network error"
        assert result.retry_count == 2  # max_retries = 2
    
    @pytest.mark.asyncio
    async def test_health_check_failure(self, reliable_crawler):
        """Test crawl blocked by health check failure."""
        async def mock_crawl_func(url):
            return "should not be called"
        
        # Mock health checker to fail
        with patch.object(reliable_crawler, '_perform_health_check', return_value=(False, "Domain unreachable")):
            result = await reliable_crawler.crawl_url("https://example.com", mock_crawl_func)
        
        assert result.success is False
        assert "Health check failed" in result.error
        assert result.error_type == CrawlErrorType.NETWORK_ERROR
    
    @pytest.mark.asyncio
    async def test_caching_behavior(self, reliable_crawler):
        """Test caching behavior."""
        call_count = 0
        
        async def mock_crawl_func(url):
            nonlocal call_count
            call_count += 1
            return f"content_{call_count}"
        
        # Mock health checker to pass
        with patch.object(reliable_crawler, '_perform_health_check', return_value=(True, None)):
            # First call should hit the crawler
            result1 = await reliable_crawler.crawl_url("https://example.com", mock_crawl_func)
            assert result1.success is True
            assert result1.content == "content_1"
            assert call_count == 1
            
            # Second call should hit cache
            result2 = await reliable_crawler.crawl_url("https://example.com", mock_crawl_func)
            assert result2.success is True
            assert result2.content == "content_1"  # Same content from cache
            assert result2.metadata.get("from_cache") is True
            assert call_count == 1  # Crawler not called again
    
    @pytest.mark.asyncio
    async def test_batch_crawl(self, reliable_crawler):
        """Test batch crawling functionality."""
        async def mock_crawl_func(url):
            if "fail" in url:
                raise Exception("Simulated failure")
            return f"content for {url}"
        
        urls = [
            "https://example1.com",
            "https://example2.com",
            "https://fail.com",
            "https://example3.com"
        ]
        
        # Mock health checker to pass
        with patch.object(reliable_crawler, '_perform_health_check', return_value=(True, None)):
            results = await reliable_crawler.crawl_batch(urls, mock_crawl_func, max_concurrent=2)
        
        assert len(results) == 4
        
        # Check successful results
        successful_results = [r for r in results if r.success]
        assert len(successful_results) == 3
        
        # Check failed result
        failed_results = [r for r in results if not r.success]
        assert len(failed_results) == 1
        assert failed_results[0].url == "https://fail.com"
    
    @pytest.mark.asyncio
    async def test_metrics_integration(self, reliable_crawler):
        """Test metrics collection during crawl operations."""
        async def mock_crawl_func(url):
            return "test content"
        
        # Mock health checker to pass
        with patch.object(reliable_crawler, '_perform_health_check', return_value=(True, None)):
            result = await reliable_crawler.crawl_url("https://example.com", mock_crawl_func)
        
        metrics = await reliable_crawler.get_metrics()
        assert metrics["total_requests"] == 1
        assert metrics["successful_requests"] == 1
        assert metrics["success_rate"] == 1.0
    
    @pytest.mark.asyncio
    async def test_circuit_breaker_integration(self, reliable_crawler):
        """Test circuit breaker integration."""
        async def mock_crawl_func(url):
            raise Exception("Persistent failure")
        
        # Mock health checker to pass
        with patch.object(reliable_crawler, '_perform_health_check', return_value=(True, None)):
            # Make enough failed requests to open circuit breaker
            for i in range(3):
                result = await reliable_crawler.crawl_url(f"https://example{i}.com", mock_crawl_func)
                assert result.success is False
        
        # Circuit breaker should now be open
        assert reliable_crawler.circuit_breaker.state == CircuitBreakerState.OPEN
        
        # Next request should fail immediately due to circuit breaker
        result = await reliable_crawler.crawl_url("https://example.com", mock_crawl_func)
        assert result.success is False


class TestFactoryFunctions:
    """Test factory functions for creating configured crawlers."""
    
    def test_create_default_reliable_crawler(self):
        """Test creating default reliable crawler."""
        crawler = create_default_reliable_crawler()
        
        assert isinstance(crawler, ReliableCrawler)
        assert crawler.retry_config.max_retries == 3
        assert crawler.timeout_config.connection_timeout == 30.0
        assert crawler.circuit_breaker_config.failure_threshold == 5
        assert crawler.cache_config.enabled is True
    
    def test_create_production_reliable_crawler(self):
        """Test creating production-optimized reliable crawler."""
        crawler = create_production_reliable_crawler()
        
        assert isinstance(crawler, ReliableCrawler)
        assert crawler.retry_config.max_retries == 5
        assert crawler.timeout_config.connection_timeout == 45.0
        assert crawler.circuit_breaker_config.failure_threshold == 10
        assert crawler.cache_config.max_size == 5000


class TestIntegrationScenarios:
    """Test real-world integration scenarios."""
    
    @pytest.mark.asyncio
    async def test_cascading_failures(self):
        """Test handling of cascading failures."""
        crawler = create_default_reliable_crawler()
        
        failure_count = 0
        
        async def mock_crawl_func(url):
            nonlocal failure_count
            failure_count += 1
            if failure_count <= 10:
                raise Exception("Network failure")
            return "finally succeeded"
        
        # Mock health checker to pass
        with patch.object(crawler, '_perform_health_check', return_value=(True, None)):
            # Should eventually succeed after circuit breaker recovery
            results = []
            for i in range(5):
                result = await crawler.crawl_url(f"https://example{i}.com", mock_crawl_func)
                results.append(result)
                
                # Add delay to allow circuit breaker recovery
                await asyncio.sleep(0.1)
        
        # Some requests should fail, but eventually succeed
        failed_results = [r for r in results if not r.success]
        successful_results = [r for r in results if r.success]
        
        # Should have both failures and successes
        assert len(failed_results) > 0
        assert len(successful_results) >= 0
    
    @pytest.mark.asyncio
    async def test_mixed_success_failure_batch(self):
        """Test batch processing with mixed success and failure."""
        crawler = create_default_reliable_crawler()
        
        async def mock_crawl_func(url):
            if "timeout" in url:
                raise asyncio.TimeoutError("Request timeout")
            elif "auth" in url:
                raise Exception("Authentication required")
            elif "rate" in url:
                raise Exception("Rate limit exceeded")
            return f"Success for {url}"
        
        urls = [
            "https://success1.com",
            "https://timeout.com",
            "https://success2.com",
            "https://auth.com",
            "https://rate.com",
            "https://success3.com"
        ]
        
        # Mock health checker to pass
        with patch.object(crawler, '_perform_health_check', return_value=(True, None)):
            results = await crawler.crawl_batch(urls, mock_crawl_func)
        
        assert len(results) == 6
        
        # Check results by URL pattern
        success_results = [r for r in results if r.success]
        timeout_results = [r for r in results if not r.success and "timeout" in r.url]
        auth_results = [r for r in results if not r.success and "auth" in r.url]
        rate_results = [r for r in results if not r.success and "rate" in r.url]
        
        assert len(success_results) == 3
        assert len(timeout_results) == 1
        assert len(auth_results) == 1
        assert len(rate_results) == 1
        
        # Verify error types
        timeout_result = timeout_results[0]
        assert timeout_result.error_type == CrawlErrorType.TIMEOUT_ERROR
        
        # Check metrics
        metrics = await crawler.get_metrics()
        assert metrics["total_requests"] == 6
        assert metrics["successful_requests"] == 3
        assert metrics["failed_requests"] == 3
        assert metrics["success_rate"] == 0.5


if __name__ == "__main__":
    pytest.main([__file__, "-v"])