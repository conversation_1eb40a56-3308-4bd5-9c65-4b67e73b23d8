#!/usr/bin/env python3
"""
Test script to verify the BrowserConfig chrome_flags fix works.
"""

import asyncio
from crawl4ai import AsyncWeb<PERSON>raw<PERSON>, BrowserConfig

async def test_browser_config():
    """Test the fixed BrowserConfig with extra_args."""
    print("Testing BrowserConfig fix...")
    
    try:
        # Create browser config using the fixed approach
        browser_config = BrowserConfig(
            headless=True,
            verbose=False,
            text_mode=True,
            extra_args=[
                "--no-sandbox",
                "--disable-dev-shm-usage",
                "--disable-gpu",
                "--disable-extensions",
                "--disable-plugins",
                "--disable-images",
                "--disable-javascript",
                "--memory-pressure-off",
                "--max_old_space_size=512"
            ]
        )
        
        print("✓ BrowserConfig created successfully with extra_args")
        
        # Test creating AsyncWebCrawler with the config
        crawler = AsyncWebCrawler(config=browser_config)
        print("✓ AsyncWebCrawler created successfully")
        
        # Test entering the async context
        async with crawler:
            print("✓ Async context manager works")
            
            # Test a simple crawl
            result = await crawler.arun("https://httpbin.org/html")
            
            if result.success:
                print("✓ Simple crawl test successful")
                print(f"  - Content length: {len(result.markdown)} characters")
            else:
                print(f"✗ Simple crawl failed: {result.error_message}")
                
    except Exception as e:
        print(f"✗ Error: {str(e)}")
        return False
    
    return True

if __name__ == "__main__":
    print("=== Crawl4AI BrowserConfig Fix Test ===")
    success = asyncio.run(test_browser_config())
    
    if success:
        print("\n✓ All tests passed! The chrome_flags fix is working.")
    else:
        print("\n✗ Tests failed. Check the error messages above.")