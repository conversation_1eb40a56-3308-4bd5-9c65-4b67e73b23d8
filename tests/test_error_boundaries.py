"""
Error boundary tests for the communication layer.

These tests focus on edge cases, malformed responses, network interruptions,
and other error scenarios to ensure robust error handling.
"""

import pytest
import asyncio
import aiohttp
import json
from unittest.mock import Mock, AsyncMock, patch
from typing import Dict, Any

# Import test targets
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent / "src"))

from crawl4ai_client import Crawl4AIClient
from job_manager import Crawl4AIJobManager, JobStatus
from exceptions import (
    ValidationError,
    NetworkError,
    JobError,
    TimeoutError,
    ServiceUnavailableError,
    ErrorSeverity
)
from error_handler import ErrorHandler, handle_async_errors
from validators import URLValidator, JobValidator


class TestErrorBoundaries:
    """Test error boundary scenarios across the communication layer."""
    
    @pytest.fixture
    def client(self):
        """Create client instance for testing."""
        return Crawl4AIClient(host="localhost", port=11235)
    
    @pytest.fixture  
    def job_manager(self):
        """Create job manager instance for testing."""
        return Crawl4AIJobManager(api_host="localhost", api_port=11235)
    
    @pytest.fixture
    def error_handler(self):
        """Create error handler instance for testing."""
        return ErrorHandler()
    
    def create_mock_response(self, status: int, content: str = "", json_data: Dict = None, raise_on_json: bool = False):
        """Helper to create mock HTTP response."""
        mock_response = AsyncMock()
        mock_response.status = status
        mock_response.text = AsyncMock(return_value=content)
        
        if raise_on_json:
            mock_response.json = AsyncMock(side_effect=ValueError("Invalid JSON"))
        else:
            mock_response.json = AsyncMock(return_value=json_data or {})
        
        return mock_response


class TestMalformedResponses(TestErrorBoundaries):
    """Test handling of malformed server responses."""
    
    @pytest.mark.asyncio
    async def test_invalid_json_response(self, client):
        """Test handling of invalid JSON in response."""
        mock_response = self.create_mock_response(200, raise_on_json=True)
        
        with patch('aiohttp.ClientSession') as mock_session_class:
            mock_session = AsyncMock()
            mock_session_class.return_value.__aenter__.return_value = mock_session
            mock_session.post.return_value.__aenter__.return_value = mock_response
            
            with pytest.raises(JobError) as exc_info:
                await client.submit_crawl_job(["https://example.com"])
            
            assert "Invalid response format" in str(exc_info.value)
            assert exc_info.value.error_code == "JOB_ERROR"
    
    @pytest.mark.asyncio
    async def test_missing_required_fields(self, client):
        """Test handling of response missing required fields."""
        # Response without task_id
        mock_response = self.create_mock_response(200, json_data={"status": "submitted"})
        
        with patch('aiohttp.ClientSession') as mock_session_class:
            mock_session = AsyncMock()
            mock_session_class.return_value.__aenter__.return_value = mock_session
            mock_session.post.return_value.__aenter__.return_value = mock_response
            
            with pytest.raises(JobError) as exc_info:
                await client.submit_crawl_job(["https://example.com"])
            
            assert "No task_id in response" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_malformed_job_status_response(self, job_manager):
        """Test handling of malformed job status response."""
        job_id = "12345678-1234-5678-9012-123456789abc"
        
        # Mock session setup
        mock_session = AsyncMock()
        job_manager.session = mock_session
        
        # Response with invalid status value
        mock_response = self.create_mock_response(200, json_data={
            "status": "INVALID_STATUS",  # Not a valid JobStatus
            "progress": "not_a_number"   # Invalid progress type
        })
        mock_session.get.return_value.__aenter__.return_value = mock_response
        
        result = await job_manager.get_job_status(job_id)
        
        # Should handle invalid status gracefully
        assert result.status == JobStatus.PENDING  # Default fallback
        assert result.progress == "not_a_number"   # Should preserve original value
    
    @pytest.mark.asyncio
    async def test_empty_response_body(self, client):
        """Test handling of empty response body."""
        mock_response = self.create_mock_response(200, json_data={})
        
        with patch('aiohttp.ClientSession') as mock_session_class:
            mock_session = AsyncMock()
            mock_session_class.return_value.__aenter__.return_value = mock_session
            mock_session.post.return_value.__aenter__.return_value = mock_response
            
            with pytest.raises(JobError) as exc_info:
                await client.submit_crawl_job(["https://example.com"])
            
            assert "No task_id in response" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_invalid_result_format(self, client):
        """Test handling of invalid crawl results format."""
        job_id = "12345678-1234-5678-9012-123456789abc"
        
        # Mock successful job submission
        submit_response = self.create_mock_response(200, json_data={"task_id": job_id})
        
        # Mock completion with invalid results format
        completion_response = self.create_mock_response(200, json_data={
            "status": "COMPLETED",
            "result": {
                "crawl_results": "not_a_list"  # Should be a list
            }
        })
        
        with patch('aiohttp.ClientSession') as mock_session_class:
            mock_session = AsyncMock()
            mock_session_class.return_value.__aenter__.return_value = mock_session
            mock_session.post.return_value.__aenter__.return_value = submit_response
            mock_session.get.return_value.__aenter__.return_value = completion_response
            
            with pytest.raises(JobError) as exc_info:
                await client.crawl_urls(["https://example.com"])
            
            assert "Invalid result format" in str(exc_info.value)


class TestNetworkInterruptions(TestErrorBoundaries):
    """Test handling of network interruptions and connection issues."""
    
    @pytest.mark.asyncio
    async def test_connection_timeout(self, client):
        """Test handling of connection timeout."""
        with patch('aiohttp.ClientSession') as mock_session_class:
            mock_session = AsyncMock()
            mock_session_class.return_value.__aenter__.return_value = mock_session
            mock_session.post.side_effect = asyncio.TimeoutError("Connection timed out")
            
            with pytest.raises(TimeoutError) as exc_info:
                await client.submit_crawl_job(["https://example.com"])
            
            assert exc_info.value.error_code == "TIMEOUT_ERROR"
    
    @pytest.mark.asyncio
    async def test_connection_refused(self, client):
        """Test handling of connection refused."""
        with patch('aiohttp.ClientSession') as mock_session_class:
            mock_session = AsyncMock()
            mock_session_class.return_value.__aenter__.return_value = mock_session
            mock_session.post.side_effect = aiohttp.ClientConnectorError("Connection refused", None)
            
            with pytest.raises(ServiceUnavailableError) as exc_info:
                await client.submit_crawl_job(["https://example.com"])
            
            assert exc_info.value.error_code == "SERVICE_UNAVAILABLE"
    
    @pytest.mark.asyncio
    async def test_partial_response_read(self, client):
        """Test handling of partial response read."""
        with patch('aiohttp.ClientSession') as mock_session_class:
            mock_session = AsyncMock()
            mock_session_class.return_value.__aenter__.return_value = mock_session
            
            # Mock response that fails during JSON parsing
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.json.side_effect = aiohttp.ClientPayloadError("Connection lost during read")
            mock_session.post.return_value.__aenter__.return_value = mock_response
            
            with pytest.raises(NetworkError) as exc_info:
                await client.submit_crawl_job(["https://example.com"])
            
            assert exc_info.value.error_code == "NETWORK_ERROR"
    
    @pytest.mark.asyncio
    async def test_dns_resolution_failure(self, client):
        """Test handling of DNS resolution failure."""
        # Create client with invalid host
        client = Crawl4AIClient(host="nonexistent.invalid.domain", port=11235)
        
        with patch('aiohttp.ClientSession') as mock_session_class:
            mock_session = AsyncMock()
            mock_session_class.return_value.__aenter__.return_value = mock_session
            mock_session.post.side_effect = aiohttp.ClientConnectorError("Cannot resolve host", None)
            
            with pytest.raises(ServiceUnavailableError):
                await client.submit_crawl_job(["https://example.com"])


class TestResourceExhaustion(TestErrorBoundaries):
    """Test handling of resource exhaustion scenarios."""
    
    @pytest.mark.asyncio
    async def test_server_overload_503(self, client):
        """Test handling of server overload (503)."""
        mock_response = self.create_mock_response(503, content="Service Temporarily Unavailable")
        
        with patch('aiohttp.ClientSession') as mock_session_class:
            mock_session = AsyncMock()
            mock_session_class.return_value.__aenter__.return_value = mock_session
            mock_session.post.return_value.__aenter__.return_value = mock_response
            
            with pytest.raises(ServiceUnavailableError) as exc_info:
                await client.submit_crawl_job(["https://example.com"])
            
            assert exc_info.value.error_code == "SERVICE_UNAVAILABLE"
    
    @pytest.mark.asyncio
    async def test_rate_limit_429(self, client):
        """Test handling of rate limiting (429)."""
        mock_response = self.create_mock_response(429, content="Too Many Requests")
        
        with patch('aiohttp.ClientSession') as mock_session_class:
            mock_session = AsyncMock()
            mock_session_class.return_value.__aenter__.return_value = mock_session
            mock_session.post.return_value.__aenter__.return_value = mock_response
            
            with pytest.raises(NetworkError) as exc_info:  # Rate limit handled as network error in current implementation
                await client.submit_crawl_job(["https://example.com"])
            
            assert exc_info.value.context["status_code"] == 429
    
    @pytest.mark.asyncio
    async def test_memory_exhaustion_simulation(self, client):
        """Test handling of memory exhaustion simulation."""
        # Simulate memory error during large response processing
        with patch('aiohttp.ClientSession') as mock_session_class:
            mock_session = AsyncMock()
            mock_session_class.return_value.__aenter__.return_value = mock_session
            
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.json.side_effect = MemoryError("Out of memory")
            mock_session.post.return_value.__aenter__.return_value = mock_response
            
            with pytest.raises(Exception):  # Should propagate memory error
                await client.submit_crawl_job(["https://example.com"])


class TestInputValidationBoundaries(TestErrorBoundaries):
    """Test input validation boundary conditions."""
    
    def test_url_validation_edge_cases(self):
        """Test URL validation with edge cases."""
        validator = URLValidator()
        
        # Test extremely long URL
        long_url = "https://example.com/" + "a" * 3000  # Exceeds 2048 limit
        with pytest.raises(ValidationError) as exc_info:
            validator.validate_url(long_url)
        assert "exceeds maximum length" in str(exc_info.value)
        
        # Test URL with special characters
        special_url = "https://example.com/path with spaces"
        # Should work after normalization
        normalized = validator.validate_url(special_url)
        assert "path%20with%20spaces" in normalized or "path with spaces" in normalized
        
        # Test empty string
        with pytest.raises(ValidationError):
            validator.validate_url("")
        
        # Test None
        with pytest.raises(ValidationError):
            validator.validate_url(None)
        
        # Test non-string input
        with pytest.raises(ValidationError):
            validator.validate_url(123)
    
    def test_job_id_validation_edge_cases(self):
        """Test job ID validation with edge cases."""
        validator = JobValidator()
        
        # Test valid UUID
        valid_uuid = "12345678-1234-5678-9012-123456789abc"
        result = validator.validate_job_id(valid_uuid)
        assert result == valid_uuid
        
        # Test UUID with wrong case
        mixed_case_uuid = "12345678-1234-5678-9012-123456789ABC"
        result = validator.validate_job_id(mixed_case_uuid)
        assert result == mixed_case_uuid  # Should preserve case
        
        # Test malformed UUID
        with pytest.raises(ValidationError):
            validator.validate_job_id("not-a-uuid")
        
        # Test UUID with extra characters
        with pytest.raises(ValidationError):
            validator.validate_job_id("12345678-1234-5678-9012-123456789abc-extra")
        
        # Test empty string
        with pytest.raises(ValidationError):
            validator.validate_job_id("")
    
    def test_timeout_validation_edge_cases(self):
        """Test timeout validation with edge cases."""
        validator = JobValidator()
        
        # Test minimum boundary
        result = validator.validate_timeout(1.0)
        assert result == 1.0
        
        # Test maximum boundary
        result = validator.validate_timeout(3600.0)
        assert result == 3600.0
        
        # Test below minimum
        with pytest.raises(ValidationError):
            validator.validate_timeout(0.5)
        
        # Test above maximum
        with pytest.raises(ValidationError):
            validator.validate_timeout(3601.0)
        
        # Test string number
        result = validator.validate_timeout("30.5")
        assert result == 30.5
        
        # Test invalid string
        with pytest.raises(ValidationError):
            validator.validate_timeout("not-a-number")
        
        # Test None
        with pytest.raises(ValidationError):
            validator.validate_timeout(None)


class TestErrorHandlerBoundaries(TestErrorBoundaries):
    """Test error handler edge cases."""
    
    def test_error_classification_edge_cases(self, error_handler):
        """Test error classification with edge cases."""
        # Test unknown HTTP status
        error = error_handler.classify_http_error(999, "Unknown Status", "https://example.com", "test")
        assert isinstance(error, NetworkError)
        assert error.context["status_code"] == 999
        
        # Test empty response text
        error = error_handler.classify_http_error(500, "", "https://example.com", "test")
        assert isinstance(error, NetworkError)
        
        # Test very long response text
        long_text = "Error: " + "x" * 10000
        error = error_handler.classify_http_error(500, long_text, "https://example.com", "test")
        assert isinstance(error, NetworkError)
    
    def test_error_response_creation_edge_cases(self, error_handler):
        """Test error response creation with edge cases."""
        # Test with custom exception
        custom_error = JobError("Test error", job_id="test-123")
        response = error_handler.create_error_response(custom_error)
        
        assert response["error"] == "JobError"
        assert response["message"] == "Test error"
        assert response["context"]["job_id"] == "test-123"
        
        # Test with standard exception
        std_error = ValueError("Standard error")
        response = error_handler.create_error_response(std_error)
        
        assert response["error"] == "ValueError"
        assert response["message"] == "Standard error"
        assert response["error_code"] == "UNKNOWN_ERROR"
        
        # Test with include_details=False
        response = error_handler.create_error_response(custom_error, include_details=False)
        assert "context" not in response
        assert "original_error" not in response
    
    def test_logging_with_different_severities(self, error_handler):
        """Test logging with different error severities."""
        import logging
        
        with patch.object(error_handler.logger, 'log') as mock_log:
            # Test critical error
            critical_error = ServiceUnavailableError("Critical service down")
            error_handler.log_error(critical_error)
            
            # Should log at CRITICAL level
            mock_log.assert_called()
            call_args = mock_log.call_args_list[0][0]
            assert call_args[0] == logging.CRITICAL
            
            mock_log.reset_mock()
            
            # Test medium severity error
            medium_error = ValidationError("Invalid input")
            error_handler.log_error(medium_error)
            
            # Should log at WARNING level
            call_args = mock_log.call_args_list[0][0]
            assert call_args[0] == logging.WARNING


class TestConcurrentOperations(TestErrorBoundaries):
    """Test error handling under concurrent operations."""
    
    @pytest.mark.asyncio
    async def test_concurrent_job_submissions(self, client):
        """Test error handling with concurrent job submissions."""
        # Create multiple clients to simulate concurrent access
        clients = [Crawl4AIClient(host="localhost", port=11235) for _ in range(5)]
        
        # Mock responses - some succeed, some fail
        responses = [
            self.create_mock_response(200, json_data={"task_id": f"job-{i}"}) if i % 2 == 0
            else self.create_mock_response(503, content="Service Unavailable")
            for i in range(5)
        ]
        
        with patch('aiohttp.ClientSession') as mock_session_class:
            mock_sessions = [AsyncMock() for _ in range(5)]
            mock_session_class.return_value.__aenter__.side_effect = mock_sessions
            
            for i, (mock_session, response) in enumerate(zip(mock_sessions, responses)):
                mock_session.post.return_value.__aenter__.return_value = response
            
            # Run concurrent operations
            tasks = [
                client.submit_crawl_job([f"https://example{i}.com"])
                for i, client in enumerate(clients)
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Check that we got mix of successes and failures
            successes = [r for r in results if isinstance(r, str)]
            failures = [r for r in results if isinstance(r, Exception)]
            
            assert len(successes) == 3  # Jobs 0, 2, 4 should succeed
            assert len(failures) == 2   # Jobs 1, 3 should fail
            assert all(isinstance(f, ServiceUnavailableError) for f in failures)
    
    @pytest.mark.asyncio
    async def test_error_during_cleanup(self, job_manager):
        """Test error handling during cleanup operations."""
        # Mock session that fails during cleanup
        mock_session = AsyncMock()
        mock_session.close.side_effect = Exception("Cleanup failed")
        job_manager.session = mock_session
        
        # Cleanup should not raise exception even if session cleanup fails
        try:
            await job_manager.cleanup()
        except Exception:
            pytest.fail("Cleanup should not raise exceptions")


class TestErrorRecovery(TestErrorBoundaries):
    """Test error recovery and resilience patterns."""
    
    @pytest.mark.asyncio
    async def test_automatic_retry_on_transient_errors(self, client):
        """Test that transient errors are retried appropriately."""
        # This would need to be implemented at a higher level
        # For now, just test that the errors are classified correctly
        
        # Simulate transient network error
        with patch('aiohttp.ClientSession') as mock_session_class:
            mock_session = AsyncMock()
            mock_session_class.return_value.__aenter__.return_value = mock_session
            
            # First call fails with timeout, second succeeds
            mock_session.post.side_effect = [
                asyncio.TimeoutError("Timeout"),
                AsyncMock()  # This would need proper mock setup for success case
            ]
            
            with pytest.raises(TimeoutError):
                await client.submit_crawl_job(["https://example.com"])
    
    def test_error_severity_classification(self):
        """Test that errors are classified with appropriate severity."""
        # Critical errors
        critical_error = ServiceUnavailableError("Service down")
        assert critical_error.severity == ErrorSeverity.CRITICAL
        
        # High severity errors
        high_error = NetworkError("Network failed")
        assert high_error.severity == ErrorSeverity.HIGH
        
        # Medium severity errors
        medium_error = ValidationError("Invalid input")
        assert medium_error.severity == ErrorSeverity.MEDIUM