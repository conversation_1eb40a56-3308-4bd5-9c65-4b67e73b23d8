# Test environment variables for Docker container
TRANSPORT=sse
HOST=0.0.0.0
PORT=8052

# Mock API keys for testing (replace with real ones for production)
OPENAI_API_KEY=test-key
MODEL_CHOICE=gpt-4o-mini

# Embedding configuration
EMBEDDING_MODEL=text-embedding-3-small
EMBEDDING_DIMENSIONS=1536

# RAG strategies
USE_CONTEXTUAL_EMBEDDINGS=false
USE_HYBRID_SEARCH=true
USE_AGENTIC_RAG=true
USE_RERANKING=true
USE_KNOWLEDGE_GRAPH=false

# Mock Supabase for testing (replace with real ones for production)
SUPABASE_URL=https://test.supabase.co
SUPABASE_SERVICE_KEY=test-service-key