FROM python:3.12-slim

ARG PORT=8051

# Set up working directory
WORKDIR /app

# Install minimal system dependencies for MCP server only
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Install uv package manager
RUN pip install uv

# Copy only the files needed for MCP server
COPY src/ ./src/
COPY pyproject.toml ./

# Create requirements file for MCP server only (no crawl4ai!)
COPY <<EOF ./requirements-mcp.txt
fastmcp>=0.2.0
fastapi>=0.100.0
jinja2>=3.1.0
supabase>=2.0.0
openai>=1.0.0
sentence-transformers>=2.0.0
psycopg2-binary>=2.9.0
asyncpg>=0.29.0
requests>=2.31.0
aiohttp>=3.9.0
python-multipart>=0.0.6
uvicorn[standard]>=0.24.0
pydantic>=2.0.0
python-dotenv>=1.0.0
EOF

# Install ONLY MCP server dependencies (no crawl4ai-setup!)
RUN uv pip install --system -r requirements-mcp.txt

# Create required cache directories for embeddings only
RUN mkdir -p .cache/embeddings .cache/queries

# Health check for MCP server - check if SSE endpoint responds
HEALTHCHECK --interval=30s --timeout=5s --start-period=5s --retries=3 \
    CMD timeout 1s curl -s http://localhost:${PORT}/sse >/dev/null 2>&1 || test $? -eq 124

# Expose port
EXPOSE ${PORT}

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV HF_HOME=/home/<USER>/.cache/huggingface
ENV TRANSFORMERS_CACHE=/home/<USER>/.cache/huggingface/transformers

# Security: Create non-root user with proper home directory and cache
RUN useradd -r -s /bin/false mcp && \
    mkdir -p /home/<USER>/.cache/huggingface/transformers && \
    chown -R mcp:mcp /app /home/<USER>

USER mcp

# Command to run the MCP server
CMD ["python", "src/main.py"]