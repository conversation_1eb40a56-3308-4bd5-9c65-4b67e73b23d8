"""
Benchmark system for MCP Crawl4AI RAG server.

This module handles benchmark execution, timing, and performance measurement
for comparing different embedding models and configurations.
"""

import time
import uuid
import json
import os
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict

from mcp.server.fastmcp import Context
from utils import get_supabase_client

@dataclass
class BenchmarkTimer:
    """Timer for tracking benchmark performance metrics"""
    test_start: Optional[float] = None
    crawl_start: Optional[float] = None
    crawl_end: Optional[float] = None
    embedding_start: Optional[float] = None
    embedding_end: Optional[float] = None
    indexing_start: Optional[float] = None
    indexing_end: Optional[float] = None
    test_end: Optional[float] = None
    
    def start_test(self):
        self.test_start = time.time()
    
    def start_crawl(self):
        self.crawl_start = time.time()
    
    def end_crawl(self):
        self.crawl_end = time.time()
    
    def start_embedding(self):
        self.embedding_start = time.time()
    
    def end_embedding(self):
        self.embedding_end = time.time()
    
    def start_indexing(self):
        self.indexing_start = time.time()
    
    def end_indexing(self):
        self.indexing_end = time.time()
    
    def end_test(self):
        self.test_end = time.time()
    
    def calculate_durations(self) -> Dict[str, float]:
        """Calculate duration metrics from timestamps"""
        return {
            'crawl_duration_seconds': (self.crawl_end - self.crawl_start) if self.crawl_end and self.crawl_start else 0,
            'embedding_duration_seconds': (self.embedding_end - self.embedding_start) if self.embedding_end and self.embedding_start else 0,
            'total_duration_seconds': (self.test_end - self.test_start) if self.test_end and self.test_start else 0,
            'indexing_duration_seconds': (self.indexing_end - self.indexing_start) if self.indexing_end and self.indexing_start else 0
        }

class BenchmarkManager:
    """Manages benchmark execution and state tracking"""
    
    def __init__(self):
        self.active_benchmarks: Dict[str, Dict] = {}
    
    def start_benchmark(self, url: str, embedding_model: str, test_name: str) -> str:
        """Start a new benchmark test run"""
        test_run_id = str(uuid.uuid4())
        
        # Initialize benchmark state
        benchmark_state = {
            "test_run_id": test_run_id,
            "test_name": test_name,
            "source_url": url,
            "embedding_model": embedding_model,
            "status": "running",
            "progress": 0,
            "current_step": "Initializing",
            "timer": BenchmarkTimer(),
            "started_at": time.time()
        }
        
        self.active_benchmarks[test_run_id] = benchmark_state
        return test_run_id
    
    def get_benchmark_status(self, test_run_id: str) -> Optional[Dict]:
        """Get current status of a benchmark"""
        return self.active_benchmarks.get(test_run_id)
    
    def update_benchmark_progress(self, test_run_id: str, progress: int, step: str):
        """Update benchmark progress"""
        if test_run_id in self.active_benchmarks:
            self.active_benchmarks[test_run_id]["progress"] = progress
            self.active_benchmarks[test_run_id]["current_step"] = step
    
    def complete_benchmark(self, test_run_id: str, success: bool = True, error: str = None):
        """Mark benchmark as completed"""
        if test_run_id in self.active_benchmarks:
            state = self.active_benchmarks[test_run_id]
            state["status"] = "completed" if success else "failed"
            state["progress"] = 100 if success else 0
            if error:
                state["error"] = error
            
            # End timing
            state["timer"].end_test()
    
    async def run_benchmark_async(self, test_run_id: str, url: str, embedding_model: str, test_name: str):
        """Run the benchmark asynchronously"""
        try:
            state = self.active_benchmarks[test_run_id]
            timer = state["timer"]
            timer.start_test()
            
            # Set embedding strategy based on model choice
            original_strategy = os.getenv("EMBEDDING_STRATEGY")
            os.environ["EMBEDDING_STRATEGY"] = embedding_model
            
            # Update progress
            state["current_step"] = "Starting crawl"
            state["progress"] = 10
            
            # Start crawling
            timer.start_crawl()
            state["current_step"] = "Crawling pages"
            state["progress"] = 20
            
            # Import the smart_crawl_url function
            from tools.crawling import smart_crawl_url
            
            # Create proper Context instance
            mock_ctx = Context(request_context=None, fastmcp=None)
            crawl_result = await smart_crawl_url(
                mock_ctx,
                url=url,
                max_depth=2,
                max_concurrent=5,
                chunk_size=5000,
                use_async_job=False
            )
            
            timer.end_crawl()
            state["current_step"] = "Processing embeddings"
            state["progress"] = 60
            
            # Insert test run into database
            timer.start_embedding()
            await self._insert_benchmark_data(test_run_id, test_name, url, embedding_model, timer)
            timer.end_embedding()
            
            state["current_step"] = "Generating test queries"
            state["progress"] = 80
            
            # Generate and run test queries
            await self._generate_and_run_test_queries(test_run_id)
            
            state["current_step"] = "Completed"
            state["progress"] = 100
            state["status"] = "completed"
            timer.end_test()
            
            # Update final timing in database
            await self._update_benchmark_timing(test_run_id, timer)
            
            # Restore original embedding strategy
            if original_strategy:
                os.environ["EMBEDDING_STRATEGY"] = original_strategy
            
        except Exception as e:
            state["status"] = "failed"
            state["error"] = str(e)
            print(f"Benchmark failed: {e}")
    
    async def _insert_benchmark_data(self, test_run_id: str, test_name: str, url: str, embedding_model: str, timer: BenchmarkTimer):
        """Insert benchmark test run data into eval tables"""
        supabase = get_supabase_client()
        
        # Determine vector dimensions based on model
        vector_dims = 768 if embedding_model == "ollama" else 1536
        embedding_strategy = f"nomic-embed-text" if embedding_model == "ollama" else "text-embedding-3-small"
        
        # Insert test run record
        test_run_data = {
            "id": test_run_id,
            "test_name": test_name,
            "source_url": url,
            "embedding_model": embedding_model,
            "embedding_strategy": embedding_strategy,
            "vector_dimensions": vector_dims,
            "status": "running"
        }
        
        supabase.table("eval_test_runs").insert(test_run_data).execute()
    
    async def _update_benchmark_timing(self, test_run_id: str, timer: BenchmarkTimer):
        """Update benchmark timing in database"""
        supabase = get_supabase_client()
        durations = timer.calculate_durations()
        
        update_data = {
            "crawl_duration_seconds": durations["crawl_duration_seconds"],
            "embedding_duration_seconds": durations["embedding_duration_seconds"],
            "total_duration_seconds": durations["total_duration_seconds"],
            "completed_at": "now()",
            "status": "completed"
        }
        
        supabase.table("eval_test_runs").update(update_data).eq("id", test_run_id).execute()
    
    async def _generate_and_run_test_queries(self, test_run_id: str):
        """Generate test queries using LLM and execute them for performance testing"""
        supabase = get_supabase_client()
        
        sample_queries = [
            {"query_text": "How to get started?", "query_type": "getting_started"},
            {"query_text": "What are the main features?", "query_type": "feature_overview"},
            {"query_text": "Installation and setup guide", "query_type": "installation"},
            {"query_text": "Common troubleshooting issues", "query_type": "troubleshooting"},
            {"query_text": "API reference and examples", "query_type": "api_reference"}
        ]
        
        for query_data in sample_queries:
            # Insert query
            query_response = supabase.table("eval_test_queries").insert({
                "test_run_id": test_run_id,
                "query_text": query_data["query_text"],
                "query_type": query_data["query_type"]
            }).execute()
            
            if query_response.data:
                query_id = query_response.data[0]["id"]
                
                # Execute search and measure performance
                start_time = time.time()
                # Mock search execution - in real implementation, this would call the search function
                search_duration_ms = (time.time() - start_time) * 1000
                
                # Insert search result metrics
                supabase.table("eval_search_results").insert({
                    "test_run_id": test_run_id,
                    "query_id": query_id,
                    "search_duration_ms": search_duration_ms,
                    "results_count": 5,  # Mock data
                    "top_similarity_score": 0.85,  # Mock data
                    "avg_similarity_score": 0.72   # Mock data
                }).execute()

def calculate_performance_metrics(test_run: Dict, search_results: List[Dict]) -> Dict:
    """Calculate performance metrics from test run and search results"""
    if not search_results:
        return {
            "avg_search_duration_ms": 0,
            "avg_similarity_score": 0,
            "total_queries": 0
        }
    
    avg_search_duration = sum(r.get("search_duration_ms", 0) for r in search_results) / len(search_results)
    avg_similarity = sum(r.get("avg_similarity_score", 0) for r in search_results) / len(search_results)
    
    return {
        "avg_search_duration_ms": round(avg_search_duration, 2),
        "avg_similarity_score": round(avg_similarity, 4),
        "total_queries": len(search_results),
        "crawl_duration_seconds": test_run.get("crawl_duration_seconds", 0),
        "embedding_duration_seconds": test_run.get("embedding_duration_seconds", 0),
        "total_duration_seconds": test_run.get("total_duration_seconds", 0)
    }

def calculate_comparison_metrics(metrics_1: Dict, metrics_2: Dict) -> Dict:
    """Calculate comparison between two sets of metrics"""
    def safe_divide(a, b):
        return (a / b - 1) * 100 if b != 0 else 0
    
    return {
        "search_speed_diff_percent": safe_divide(metrics_2["avg_search_duration_ms"], metrics_1["avg_search_duration_ms"]),
        "similarity_diff_percent": safe_divide(metrics_2["avg_similarity_score"], metrics_1["avg_similarity_score"]),
        "crawl_speed_diff_percent": safe_divide(metrics_2["crawl_duration_seconds"], metrics_1["crawl_duration_seconds"]),
        "embedding_speed_diff_percent": safe_divide(metrics_2["embedding_duration_seconds"], metrics_1["embedding_duration_seconds"]),
        "total_speed_diff_percent": safe_divide(metrics_2["total_duration_seconds"], metrics_1["total_duration_seconds"])
    }