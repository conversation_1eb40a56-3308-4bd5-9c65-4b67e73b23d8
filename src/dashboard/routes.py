"""
Dashboard routes for MCP Crawl4AI RAG server.

This module contains all the web dashboard routes for monitoring,
benchmarking, and managing the MCP server.
"""

import asyncio
import json
import uuid
from pathlib import Path
from typing import Dict, Any

from starlette.requests import Request
from starlette.responses import HTMLResponse, JSONResponse, StreamingResponse
from starlette.templating import Jinja2Templates

from utils import get_supabase_client
from .benchmarks import BenchmarkManager, calculate_performance_metrics, calculate_comparison_metrics

# Setup Jinja2 templates for dashboard
templates = Jinja2Templates(directory=str(Path(__file__).parent.parent / "templates"))

# Global benchmark manager instance
benchmark_manager = BenchmarkManager()

def setup_dashboard_routes(mcp_server):
    """Setup all dashboard routes on the MCP server"""
    
    @mcp_server.custom_route("/dashboard", methods=["GET"])
    async def dashboard(request: Request) -> HTMLResponse:
        """Main dashboard page with HTMX integration"""
        return templates.TemplateResponse("dashboard.html", {"request": request})
    
    @mcp_server.custom_route("/api/benchmark/start", methods=["POST"])
    async def start_benchmark(request: Request) -> JSONResponse:
        """Start new benchmark test run"""
        try:
            form = await request.form()
            url = form.get("url")
            embedding_model = form.get("embedding_model")
            test_name = form.get("test_name")
            
            if not all([url, embedding_model, test_name]):
                return JSONResponse({
                    "success": False,
                    "error": "Missing required fields: url, embedding_model, test_name"
                }, status_code=400)
            
            # Start benchmark
            test_run_id = benchmark_manager.start_benchmark(url, embedding_model, test_name)
            
            # Start the benchmark in background
            asyncio.create_task(benchmark_manager.run_benchmark_async(
                test_run_id, url, embedding_model, test_name
            ))
            
            return JSONResponse({
                "success": True,
                "test_run_id": test_run_id,
                "message": "Benchmark started successfully"
            })
            
        except Exception as e:
            return JSONResponse({
                "success": False,
                "error": str(e)
            }, status_code=500)
    
    @mcp_server.custom_route("/api/benchmark/status/{test_run_id}", methods=["GET"])
    async def benchmark_status(request: Request) -> StreamingResponse:
        """SSE endpoint for real-time progress updates"""
        test_run_id = request.path_params.get("test_run_id")
        
        async def event_generator():
            while True:
                try:
                    status = benchmark_manager.get_benchmark_status(test_run_id)
                    
                    if not status:
                        yield f"data: {json.dumps({'error': 'Test run not found'})}\\n\\n"
                        break
                    
                    if status['status'] == 'completed':
                        yield f"data: {json.dumps({'progress': 100, 'message': 'Test completed', 'status': 'completed'})}\\n\\n"
                        break
                    elif status['status'] == 'failed':
                        yield f"data: {json.dumps({'progress': 0, 'message': 'Test failed', 'status': 'failed', 'error': status.get('error', 'Unknown error')})}\\n\\n"
                        break
                    
                    progress_data = {
                        'progress': status['progress'],
                        'message': status['current_step'],
                        'status': status['status']
                    }
                    yield f"data: {json.dumps(progress_data)}\\n\\n"
                    
                    await asyncio.sleep(1)  # Poll every second
                    
                except Exception as e:
                    yield f"data: {json.dumps({'error': str(e)})}\\n\\n"
                    break
        
        return StreamingResponse(
            event_generator(), 
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "Cache-Control"
            }
        )
    
    @mcp_server.custom_route("/api/benchmark/results/{test_run_id}", methods=["GET"])
    async def benchmark_results(request: Request) -> JSONResponse:
        """Get benchmark results and comparison data"""
        test_run_id = request.path_params.get("test_run_id")
        
        try:
            # Query evaluation results from database
            supabase = get_supabase_client()
            
            # Get test run data
            test_run_response = supabase.table("eval_test_runs").select("*").eq("id", test_run_id).execute()
            if not test_run_response.data:
                return JSONResponse({"success": False, "error": "Test run not found"}, status_code=404)
            
            test_run = test_run_response.data[0]
            
            # Get search performance data
            search_results_response = supabase.table("eval_search_results").select("*").eq("test_run_id", test_run_id).execute()
            search_results = search_results_response.data if search_results_response.data else []
            
            # Calculate performance metrics
            metrics = calculate_performance_metrics(test_run, search_results)
            
            return JSONResponse({
                "success": True,
                "test_run": test_run,
                "search_results": search_results,
                "metrics": metrics
            })
            
        except Exception as e:
            return JSONResponse({
                "success": False,
                "error": str(e)
            }, status_code=500)
    
    @mcp_server.custom_route("/api/benchmark/compare", methods=["GET"])
    async def compare_benchmarks(request: Request) -> JSONResponse:
        """Compare two benchmark results side-by-side"""
        query_params = request.query_params
        test_run_1 = query_params.get("test_run_1")
        test_run_2 = query_params.get("test_run_2")
        
        if not test_run_1 or not test_run_2:
            return JSONResponse({
                "success": False,
                "error": "Missing required query parameters: test_run_1, test_run_2"
            }, status_code=400)
        
        try:
            supabase = get_supabase_client()
            
            # Get both test runs
            test_runs_response = supabase.table("eval_test_runs").select("*").in_("id", [test_run_1, test_run_2]).execute()
            if not test_runs_response.data or len(test_runs_response.data) != 2:
                return JSONResponse({"success": False, "error": "One or both test runs not found"}, status_code=404)
            
            test_runs = {run['id']: run for run in test_runs_response.data}
            
            # Get search results for both
            search_results_response = supabase.table("eval_search_results").select("*").in_("test_run_id", [test_run_1, test_run_2]).execute()
            search_results = {}
            for result in search_results_response.data or []:
                run_id = result['test_run_id']
                if run_id not in search_results:
                    search_results[run_id] = []
                search_results[run_id].append(result)
            
            # Calculate metrics for both
            metrics_1 = calculate_performance_metrics(test_runs[test_run_1], search_results.get(test_run_1, []))
            metrics_2 = calculate_performance_metrics(test_runs[test_run_2], search_results.get(test_run_2, []))
            
            # Calculate differences
            comparison = calculate_comparison_metrics(metrics_1, metrics_2)
            
            return JSONResponse({
                "success": True,
                "test_run_1": {
                    "data": test_runs[test_run_1],
                    "metrics": metrics_1
                },
                "test_run_2": {
                    "data": test_runs[test_run_2],
                    "metrics": metrics_2
                },
                "comparison": comparison
            })
            
        except Exception as e:
            return JSONResponse({
                "success": False,
                "error": str(e)
            }, status_code=500)