"""
Core business logic for MCP Crawl4AI RAG server.

This module contains the core business logic separated from MCP concerns:
- crawling: Web crawling and content processing
- utils: Utility functions for response formatting
- chunking: Content chunking and processing
"""

from .crawling import (
    smart_chunk_markdown,
    extract_section_info,
    is_txt,
    is_sitemap,
    parse_sitemap,
    crawl_markdown_file,
    crawl_batch,
    crawl_recursive_internal_links,
    process_code_example,
    rerank_results
)
from .utils import (
    create_standard_error_response,
    create_standard_success_response,
    serialize_datetime_objects
)

__all__ = [
    # Crawling functions
    "smart_chunk_markdown",
    "extract_section_info", 
    "is_txt",
    "is_sitemap",
    "parse_sitemap",
    "crawl_markdown_file",
    "crawl_batch",
    "crawl_recursive_internal_links",
    "process_code_example",
    "rerank_results",
    
    # Utility functions
    "create_standard_error_response",
    "create_standard_success_response",
    "serialize_datetime_objects"
]