"""
Utility functions for MCP Crawl4AI RAG server.

This module contains utility functions for response formatting,
data serialization, and common operations.
"""

import json
from datetime import datetime
from typing import Dict, Any, Optional

def create_standard_error_response(error: str, context: str = "", error_type: str = "error", details: Optional[Dict[str, Any]] = None) -> str:
    """
    Create a standardized error response for MCP tools.
    
    Args:
        error: The error message
        context: Additional context about where the error occurred
        error_type: Type of error (error, validation_error, network_error, etc.)
        details: Additional error details
        
    Returns:
        JSON string with standardized error format
    """
    error_response = {
        "success": False,
        "error": {
            "message": error,
            "type": error_type,
            "context": context,
            "timestamp": datetime.utcnow().isoformat() + "Z"
        }
    }
    
    if details:
        error_response["error"]["details"] = details
    
    return json.dumps(error_response, indent=2)

def create_standard_success_response(data: Dict[str, Any], message: str = "Operation completed successfully") -> str:
    """
    Create a standardized success response for MCP tools.
    
    Args:
        data: The response data
        message: Success message
        
    Returns:
        JSON string with standardized success format
    """
    success_response = {
        "success": True,
        "message": message,
        "timestamp": datetime.utcnow().isoformat() + "Z",
        "data": data
    }
    
    return json.dumps(success_response, indent=2)

def serialize_datetime_objects(obj: Any) -> Any:
    """
    Recursively convert datetime objects to ISO format strings for JSON serialization.
    
    Args:
        obj: Object that may contain datetime objects
        
    Returns:
        Object with datetime objects converted to ISO strings
    """
    if isinstance(obj, datetime):
        return obj.isoformat()
    elif isinstance(obj, dict):
        return {key: serialize_datetime_objects(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [serialize_datetime_objects(item) for item in obj]
    else:
        return obj