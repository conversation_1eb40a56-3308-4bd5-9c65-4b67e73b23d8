"""
Error handling utilities for the MCP Crawl4AI RAG server.

This module provides standardized error handling, logging, and response
formatting utilities to ensure consistent error management across the
communication layer.
"""

import logging
import traceback
import aiohttp
import asyncio
from typing import Dict, Any, Optional, Type, Union
from functools import wraps

from exceptions import (
    Crawl4AIError,
    NetworkError,
    TimeoutError,
    ServiceUnavailableError,
    ValidationError,
    JobError,
    RateLimitError,
    ErrorSeverity,
    create_network_error,
    create_timeout_error,
    create_service_unavailable_error
)

# Configure logger for error handling
logger = logging.getLogger(__name__)


class ErrorHandler:
    """
    Centralized error handler for the communication layer.
    
    Provides consistent error classification, logging, and response formatting.
    """
    
    def __init__(self, logger_name: str = __name__):
        self.logger = logging.getLogger(logger_name)
    
    def classify_http_error(
        self,
        response_status: int,
        response_text: str = "",
        url: str = "",
        operation: str = ""
    ) -> NetworkError:
        """
        Classify HTTP errors into appropriate exception types.
        
        Args:
            response_status: HTTP status code
            response_text: Response body text
            url: URL that caused the error
            operation: Operation being performed
            
        Returns:
            Appropriate NetworkError subclass
        """
        if response_status == 404:
            return create_network_error(
                url,
                response_status,
                f"Resource not found during {operation}"
            )
        elif response_status == 429:
            return RateLimitError(
                f"Rate limit exceeded for {operation}",
                context={
                    "url": url,
                    "status_code": response_status,
                    "response_text": response_text
                }
            )
        elif response_status == 503:
            return create_service_unavailable_error(
                operation,
                url
            )
        elif 400 <= response_status < 500:
            return create_network_error(
                url,
                response_status,
                f"Client error during {operation}: {response_text}"
            )
        elif 500 <= response_status < 600:
            return create_network_error(
                url,
                response_status,
                f"Server error during {operation}: {response_text}"
            )
        else:
            return create_network_error(
                url,
                response_status,
                f"HTTP error during {operation}: {response_text}"
            )
    
    def classify_aiohttp_error(
        self,
        error: Exception,
        url: str = "",
        operation: str = ""
    ) -> Crawl4AIError:
        """
        Classify aiohttp errors into appropriate exception types.
        
        Args:
            error: Original aiohttp exception
            url: URL that caused the error
            operation: Operation being performed
            
        Returns:
            Appropriate Crawl4AIError subclass
        """
        if isinstance(error, asyncio.TimeoutError):
            return create_timeout_error(operation, 0)
        elif isinstance(error, aiohttp.ClientConnectorError):
            return create_service_unavailable_error(operation, url)
        elif isinstance(error, aiohttp.ClientResponseError):
            return self.classify_http_error(
                error.status,
                str(error),
                url,
                operation
            )
        elif isinstance(error, aiohttp.ClientError):
            return NetworkError(
                f"Client error during {operation}: {str(error)}",
                url=url,
                original_error=error
            )
        else:
            return Crawl4AIError(
                f"Unexpected error during {operation}: {str(error)}",
                original_error=error,
                context={"url": url, "operation": operation}
            )
    
    def log_error(
        self,
        error: Exception,
        context: Optional[Dict[str, Any]] = None,
        include_traceback: bool = True
    ) -> None:
        """
        Log error with appropriate level and context.
        
        Args:
            error: Exception to log
            context: Additional context information
            include_traceback: Whether to include full traceback
        """
        context = context or {}
        
        if isinstance(error, Crawl4AIError):
            # Use severity from custom exception
            severity = error.severity
            log_data = {
                "error_type": error.__class__.__name__,
                "error_code": error.error_code,
                "message": error.message,
                "context": {**error.context, **context}
            }
        else:
            # Default severity for unknown errors
            severity = ErrorSeverity.HIGH
            log_data = {
                "error_type": error.__class__.__name__,
                "message": str(error),
                "context": context
            }
        
        # Choose log level based on severity
        if severity == ErrorSeverity.CRITICAL:
            log_level = logging.CRITICAL
        elif severity == ErrorSeverity.HIGH:
            log_level = logging.ERROR
        elif severity == ErrorSeverity.MEDIUM:
            log_level = logging.WARNING
        else:
            log_level = logging.INFO
        
        # Log the error
        self.logger.log(log_level, "Error occurred", extra=log_data)
        
        # Include traceback for high severity errors
        if include_traceback and severity in (ErrorSeverity.CRITICAL, ErrorSeverity.HIGH):
            self.logger.log(log_level, traceback.format_exc())
    
    def create_error_response(
        self,
        error: Exception,
        include_details: bool = True
    ) -> Dict[str, Any]:
        """
        Create standardized error response for API endpoints.
        
        Args:
            error: Exception to format
            include_details: Whether to include detailed error information
            
        Returns:
            Dictionary suitable for JSON API response
        """
        if isinstance(error, Crawl4AIError):
            response = error.to_dict()
        else:
            response = {
                "error": error.__class__.__name__,
                "message": str(error),
                "error_code": "UNKNOWN_ERROR",
                "severity": ErrorSeverity.HIGH.value,
                "context": {}
            }
        
        if not include_details:
            # Remove sensitive information for production
            response.pop("context", None)
            response.pop("original_error", None)
        
        return response


# Global error handler instance
error_handler = ErrorHandler()


def handle_async_errors(
    operation_name: str = "",
    reraise: bool = True,
    log_errors: bool = True
):
    """
    Decorator for handling async function errors consistently.
    
    Args:
        operation_name: Name of the operation for error context
        reraise: Whether to reraise the error after handling
        log_errors: Whether to log errors
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except Crawl4AIError:
                # Already a custom error, just reraise
                if log_errors:
                    error_handler.log_error(error)
                if reraise:
                    raise
                return None
            except Exception as error:
                # Convert to custom error
                operation = operation_name or func.__name__
                custom_error = error_handler.classify_aiohttp_error(
                    error,
                    operation=operation
                )
                
                if log_errors:
                    error_handler.log_error(custom_error)
                
                if reraise:
                    raise custom_error from error
                return None
        
        return wrapper
    return decorator


def handle_http_response(
    response_status: int,
    response_text: str,
    url: str = "",
    operation: str = ""
) -> None:
    """
    Check HTTP response and raise appropriate error if needed.
    
    Args:
        response_status: HTTP status code
        response_text: Response body text
        url: URL that caused the response
        operation: Operation being performed
        
    Raises:
        NetworkError: For non-2xx status codes
    """
    if not (200 <= response_status < 300):
        error = error_handler.classify_http_error(
            response_status,
            response_text,
            url,
            operation
        )
        error_handler.log_error(error)
        raise error


async def safe_http_request(
    session: aiohttp.ClientSession,
    method: str,
    url: str,
    operation: str = "",
    **kwargs
) -> aiohttp.ClientResponse:
    """
    Make HTTP request with standardized error handling.
    
    Args:
        session: aiohttp session
        method: HTTP method (GET, POST, etc.)
        url: URL to request
        operation: Operation name for error context
        **kwargs: Additional arguments for the request
        
    Returns:
        aiohttp.ClientResponse object
        
    Raises:
        Appropriate Crawl4AIError subclass
    """
    try:
        async with session.request(method, url, **kwargs) as response:
            if not (200 <= response.status < 300):
                response_text = await response.text()
                handle_http_response(
                    response.status,
                    response_text,
                    url,
                    operation
                )
            return response
    except Exception as error:
        custom_error = error_handler.classify_aiohttp_error(
            error,
            url,
            operation
        )
        error_handler.log_error(custom_error)
        raise custom_error from error


# Backward compatibility aliases
classify_error = error_handler.classify_aiohttp_error
log_error = error_handler.log_error
create_error_response = error_handler.create_error_response