"""
FastMCP server configuration and setup.

This module contains the main FastMCP server instance and configuration.
Tools are registered from their respective modules.
"""

import os
from pathlib import Path
from dotenv import load_dotenv
import logging

from mcp.server.fastmcp import FastMCP
from .lifespan import crawl4ai_lifespan

# Load environment variables from the project root .env file
project_root = Path(__file__).resolve().parent.parent.parent
dotenv_path = project_root / '.env'

# Force override of existing environment variables
load_dotenv(dotenv_path, override=True)

# Configure logging to reduce connection error noise
logging.getLogger("anyio").setLevel(logging.CRITICAL)
logging.getLogger("uvicorn.error").setLevel(logging.WARNING)

# Log level can be controlled via environment variable for debugging
log_level = os.getenv("LOG_LEVEL", "INFO").upper()
if log_level in ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]:
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level))
    print(f"🔧 Log level set to: {log_level}")
else:
    print(f"⚠️ Invalid LOG_LEVEL '{log_level}', using default INFO")

# Initialize FastMCP server with proper configuration
mcp = FastMCP(
    "mcp-crawl4ai-rag",
    description="MCP server for RAG and web crawling with Crawl4AI",
    lifespan=crawl4ai_lifespan,
    host=os.getenv("HOST", "0.0.0.0"),
    port=os.getenv("PORT", "8051")
)

# Tools will be registered in separate modules and imported when needed