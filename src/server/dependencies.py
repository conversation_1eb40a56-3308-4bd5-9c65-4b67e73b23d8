"""
Dependency injection for MCP Crawl4AI RAG server.

This module provides dependency injection for the MCP server, managing
global resources and their lifecycle.
"""

from dataclasses import dataclass
from typing import Any, Optional

@dataclass
class Crawl4AIContext:
    """Context object containing all initialized resources for the MCP server."""
    crawler: Any
    supabase_client: Any
    reranking_model: Optional[Any] = None
    knowledge_validator: Optional[Any] = None
    repo_extractor: Optional[Any] = None
    reliable_crawler: Optional[Any] = None
    job_manager: Optional[Any] = None

# Global singleton instances for persistent resource management
_global_crawler = None
_global_supabase_client = None
_global_reliable_crawler = None
_global_knowledge_validator = None
_global_repo_extractor = None
_global_job_manager = None
_cached_reranking_model = None

def get_resources():
    """Get global resources for MCP tools. This is a dependency function for FastMCP."""
    return Crawl4AIContext(
        crawler=_global_crawler,
        supabase_client=_global_supabase_client,
        reranking_model=_cached_reranking_model,
        knowledge_validator=_global_knowledge_validator,
        repo_extractor=_global_repo_extractor,
        reliable_crawler=_global_reliable_crawler,
        job_manager=_global_job_manager
    )

def set_global_resources(
    crawler=None,
    supabase_client=None,
    reranking_model=None,
    knowledge_validator=None,
    repo_extractor=None,
    reliable_crawler=None,
    job_manager=None
):
    """Set global resources (used by lifespan management)."""
    global _global_crawler, _global_supabase_client, _cached_reranking_model
    global _global_knowledge_validator, _global_repo_extractor
    global _global_reliable_crawler, _global_job_manager
    
    if crawler is not None:
        _global_crawler = crawler
    if supabase_client is not None:
        _global_supabase_client = supabase_client
    if reranking_model is not None:
        _cached_reranking_model = reranking_model
    if knowledge_validator is not None:
        _global_knowledge_validator = knowledge_validator
    if repo_extractor is not None:
        _global_repo_extractor = repo_extractor
    if reliable_crawler is not None:
        _global_reliable_crawler = reliable_crawler
    if job_manager is not None:
        _global_job_manager = job_manager

def get_global_resources():
    """Get global resources for internal use."""
    return {
        'crawler': _global_crawler,
        'supabase_client': _global_supabase_client,
        'reranking_model': _cached_reranking_model,
        'knowledge_validator': _global_knowledge_validator,
        'repo_extractor': _global_repo_extractor,
        'reliable_crawler': _global_reliable_crawler,
        'job_manager': _global_job_manager
    }