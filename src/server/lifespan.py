"""
Lifecycle management for MCP Crawl4AI RAG server.

This module handles initialization and cleanup of global resources
including crawler, database clients, and AI models.
"""

import asyncio
import os
import logging
from contextlib import asynccontextmanager
from collections.abc import AsyncIterator
from sentence_transformers import CrossEncoder
from pathlib import Path
import sys

# Set HF_HOME environment variable to avoid deprecation warnings
if not os.getenv("HF_HOME"):
    os.environ["HF_HOME"] = os.path.expanduser("~/.cache/huggingface")

from mcp.server.fastmcp import FastMCP
from .dependencies import Crawl4AIContext, set_global_resources, get_global_resources

# Add knowledge_graphs folder to path for importing knowledge graph modules
knowledge_graphs_path = Path(__file__).resolve().parent.parent.parent / 'knowledge_graphs'
sys.path.append(str(knowledge_graphs_path))

# Use HTTP client instead of direct crawl4ai imports for lightweight container
from crawl4ai_client import AsyncWebCrawler, BrowserConfig

from utils import get_supabase_client

# Import knowledge graph modules conditionally
KnowledgeGraphValidator = None
DirectNeo4jExtractor = None

try:
    if os.getenv("USE_KNOWLEDGE_GRAPH", "false") == "true":
        from knowledge_graph_validator import KnowledgeGraphValidator
        from parse_repo_into_neo4j import DirectNeo4jExtractor
except ImportError as e:
    print(f"ℹ️  Knowledge graph modules not available: {e}")
    print("ℹ️  Knowledge graph functionality will be disabled")

# Import reliability service
from reliability import (
    create_default_reliable_crawler,
    create_production_reliable_crawler
)

# Import job management
from job_manager import Crawl4AIJobManager, get_job_manager_config

# Global state management
_initialization_complete = False
_reranking_model_lock = None
_initialization_lock = None
_server_initialized = False

def validate_neo4j_connection() -> bool:
    """Check if Neo4j environment variables are configured."""
    return all([
        os.getenv("NEO4J_URI"),
        os.getenv("NEO4J_USER"),
        os.getenv("NEO4J_PASSWORD")
    ])

def format_neo4j_error(error: Exception) -> str:
    """Format Neo4j connection errors for user-friendly messages."""
    error_str = str(error).lower()
    if "authentication" in error_str or "unauthorized" in error_str:
        return "Neo4j authentication failed. Check NEO4J_USER and NEO4J_PASSWORD."
    elif "connection" in error_str or "refused" in error_str or "timeout" in error_str:
        return "Cannot connect to Neo4j. Check NEO4J_URI and ensure Neo4j is running."
    elif "database" in error_str:
        return "Neo4j database error. Check if the database exists and is accessible."
    else:
        return f"Neo4j error: {str(error)}"

async def _initialize_global_resources():
    """Initialize all global resources once."""
    global _server_initialized, _reranking_model_lock
    
    # Skip if already initialized
    if _server_initialized:
        return
    
    # Create browser configuration
    browser_config = BrowserConfig(
        headless=True,
        verbose=False
    )
    
    # Initialize the crawler
    print("🔄 Initializing web crawler...")
    crawler = AsyncWebCrawler(config=browser_config)
    await crawler.__aenter__()
    print("✅ Web crawler initialized")
    
    # Initialize Supabase client
    print("🔄 Initializing Supabase client...")
    supabase_client = get_supabase_client()
    print("✅ Supabase client initialized")
    
    # Initialize cross-encoder model for reranking if enabled
    reranking_model = None
    if _reranking_model_lock is None:
        _reranking_model_lock = asyncio.Lock()
    
    if os.getenv("USE_RERANKING", "false") == "true":
        print("🔄 Initializing reranking model...")
        async with _reranking_model_lock:
            try:
                print("🔄 Loading CrossEncoder model for reranking...")
                # Load model in executor to prevent blocking
                loop = asyncio.get_event_loop()
                reranking_model = await loop.run_in_executor(
                    None, 
                    lambda: CrossEncoder("cross-encoder/ms-marco-MiniLM-L-6-v2", device="cpu")
                )
                print("✅ CrossEncoder model loaded successfully")
            except Exception as e:
                print(f"❌ Failed to load reranking model: {e}")
                reranking_model = None
    else:
        print("ℹ️  Reranking disabled")
    
    # Initialize Neo4j components if configured and enabled
    knowledge_validator = None
    repo_extractor = None
    knowledge_graph_enabled = os.getenv("USE_KNOWLEDGE_GRAPH", "false") == "true"
    
    if knowledge_graph_enabled:
        print("🔄 Initializing knowledge graph components...")
        neo4j_uri = os.getenv("NEO4J_URI")
        neo4j_user = os.getenv("NEO4J_USER")
        neo4j_password = os.getenv("NEO4J_PASSWORD")
        
        if neo4j_uri and neo4j_user and neo4j_password:
            try:
                # Initialize knowledge graph validator
                knowledge_validator = KnowledgeGraphValidator(neo4j_uri, neo4j_user, neo4j_password)
                await knowledge_validator.initialize()
                print("✅ Knowledge graph validator initialized")
                
                # Initialize repository extractor
                repo_extractor = DirectNeo4jExtractor(neo4j_uri, neo4j_user, neo4j_password)
                await repo_extractor.initialize()
                print("✅ Repository extractor initialized")
                
            except Exception as e:
                print(f"❌ Failed to initialize Neo4j components: {format_neo4j_error(e)}")
                knowledge_validator = None
                repo_extractor = None
        else:
            print("ℹ️  Neo4j credentials not configured - knowledge graph tools will be unavailable")
    else:
        print("ℹ️  Knowledge graph functionality disabled - set USE_KNOWLEDGE_GRAPH=true to enable")
    
    # Initialize reliable crawler based on environment
    print("🔄 Initializing reliable crawler...")
    environment = os.getenv("ENVIRONMENT", "development").lower()
    
    if environment == "production":
        reliable_crawler = create_production_reliable_crawler()
        print("✅ Production-optimized reliable crawler initialized")
    else:
        reliable_crawler = create_default_reliable_crawler()
        print("✅ Development reliable crawler initialized")
    
    # Initialize job manager for async operations
    job_manager = None
    job_management_enabled = os.getenv("USE_JOB_MANAGEMENT", "false") == "true"
    if job_management_enabled:
        print("🔄 Initializing job manager...")
        try:
            job_config = get_job_manager_config()
            job_manager = Crawl4AIJobManager(**job_config)
            await job_manager.start()
            print("✅ Job manager initialized")
        except Exception as e:
            print(f"❌ Failed to initialize job manager: {e}")
            print("ℹ️  Job management disabled - long operations may timeout")
            job_manager = None
    else:
        print("ℹ️  Job management disabled - set USE_JOB_MANAGEMENT=true to enable")
    
    # Set global resources
    set_global_resources(
        crawler=crawler,
        supabase_client=supabase_client,
        reranking_model=reranking_model,
        knowledge_validator=knowledge_validator,
        repo_extractor=repo_extractor,
        reliable_crawler=reliable_crawler,
        job_manager=job_manager
    )
    
    # Mark server as initialized
    _server_initialized = True

async def _cleanup_global_resources():
    """Cleanup all global resources on server shutdown."""
    global _server_initialized
    
    resources = get_global_resources()
    
    if resources['crawler']:
        try:
            await resources['crawler'].__aexit__(None, None, None)
            print("✓ Web crawler closed")
        except Exception as e:
            print(f"Error closing web crawler: {e}")
    
    if resources['knowledge_validator']:
        try:
            await resources['knowledge_validator'].close()
            print("✓ Knowledge graph validator closed")
        except Exception as e:
            print(f"Error closing knowledge validator: {e}")
    
    if resources['repo_extractor']:
        try:
            await resources['repo_extractor'].close()
            print("✓ Repository extractor closed")
        except Exception as e:
            print(f"Error closing repository extractor: {e}")
    
    if resources['job_manager']:
        try:
            await resources['job_manager'].cleanup()
            print("✓ Job manager closed")
        except Exception as e:
            print(f"Error closing job manager: {e}")
    
    _server_initialized = False

@asynccontextmanager
async def crawl4ai_lifespan(server: FastMCP) -> AsyncIterator[Crawl4AIContext]:
    """
    Manages the Crawl4AI client lifecycle with singleton pattern to prevent reinitialization.
    Enhanced with robust connection handling and error recovery.
    
    Args:
        server: The FastMCP server instance
        
    Yields:
        Crawl4AIContext: The context containing the Crawl4AI crawler and Supabase client
    """
    global _initialization_lock, _server_initialized
    
    # Initialize lock if needed
    if _initialization_lock is None:
        _initialization_lock = asyncio.Lock()
    
    # Wait for server to be fully initialized with timeout protection
    max_wait_time = 30  # seconds
    start_time = asyncio.get_event_loop().time()
    
    while not _server_initialized:
        # Check if we've been waiting too long
        if asyncio.get_event_loop().time() - start_time > max_wait_time:
            raise RuntimeError(f"Server initialization timeout after {max_wait_time} seconds")
        
        try:
            # Try to acquire the lock with timeout
            await asyncio.wait_for(_initialization_lock.acquire(), timeout=5.0)
            try:
                if not _server_initialized:
                    print("🔄 Starting MCP server initialization...")
                    await _initialize_global_resources()
                    print("✅ MCP server initialization complete - ready to accept requests")
                break
            finally:
                _initialization_lock.release()
        except asyncio.TimeoutError:
            print("⚠️ Waiting for server initialization lock...")
            await asyncio.sleep(0.5)
            continue
        except Exception as e:
            print(f"❌ Error during server initialization: {e}")
            await asyncio.sleep(1.0)
            continue
    
    # Get resources
    resources = get_global_resources()
    
    # Add connection health check
    if not all([resources['crawler'], resources['supabase_client']]):
        raise RuntimeError("Critical components not properly initialized")
    
    try:
        yield Crawl4AIContext(
            crawler=resources['crawler'],
            supabase_client=resources['supabase_client'],
            reranking_model=resources['reranking_model'],
            knowledge_validator=resources['knowledge_validator'],
            repo_extractor=resources['repo_extractor'],
            reliable_crawler=resources['reliable_crawler'],
            job_manager=resources['job_manager']
        )
    finally:
        # Keep resources alive for next request - don't cleanup here
        # Resources will be cleaned up when server shuts down
        pass