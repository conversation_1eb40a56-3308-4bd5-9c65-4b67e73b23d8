"""
Main entry point for the refactored MCP Crawl4AI RAG server.

This file serves as the main entry point that properly imports all refactored modules
and starts the server.
"""

import os
import sys
import signal
import logging
import warnings
from pathlib import Path

# Suppress aiohttp unclosed session warnings during development
warnings.filterwarnings("ignore", message=".*Unclosed client session.*")
warnings.filterwarnings("ignore", message=".*Unclosed connector.*")

# Add the src directory to Python path for imports
src_path = Path(__file__).parent
sys.path.insert(0, str(src_path))

# Import the refactored modules
from server.mcp_server import mcp
from server.lifespan import _cleanup_global_resources
from dashboard.routes import setup_dashboard_routes
from tools.crawling import crawl_single_page, smart_crawl_url
from tools.search import perform_rag_query, search_code_examples, get_available_sources
from tools.knowledge_graph import check_ai_script_hallucinations, query_knowledge_graph, parse_github_repository
from tools.management import (
    start_crawl_job, check_job_status, get_job_result, 
    cancel_crawl_job, list_crawl_jobs
)
from tools.reliability import (
    get_reliability_metrics, reset_reliability_state, test_url_health
)

# Setup dashboard routes
setup_dashboard_routes(mcp)

# Tools are automatically registered via @mcp.tool() decorators in their respective modules
# All tools will be discovered and registered automatically by FastMCP

# Connection error handler for graceful handling of broken connections
class ConnectionErrorHandler:
    """Middleware to handle broken connections and initialization race conditions gracefully."""
    
    @staticmethod
    async def handle_connection_errors(func):
        """Wrapper to handle connection-related errors gracefully."""
        try:
            return await func()
        except Exception as e:
            error_str = str(e).lower()
            error_type = type(e).__name__
            
            # Handle broken connection errors gracefully
            if any(keyword in error_str for keyword in [
                'brokenresourceerror', 'connection', 'broken', 'closed', 'disconnected'
            ]) or error_type in ['BrokenResourceError', 'ConnectionResetError']:
                print(f"⚠️ Client connection issue (handled gracefully): {error_type}")
                return None  # Graceful handling
            
            # Handle initialization race conditions
            elif "received request before initialization" in error_str:
                print(f"⚠️ Initialization race condition detected, retrying...")
                import asyncio
                await asyncio.sleep(0.5)  # Brief delay before retry
                raise e  # Let MCP framework handle retry
            
            else:
                # Re-raise other errors
                raise e

# Add graceful shutdown handler
def signal_handler(signum, frame):
    """Handle shutdown signals gracefully."""
    print(f"\\n🔄 Received signal {signum}, shutting down gracefully...")
    import asyncio
    try:
        # Run cleanup in event loop
        loop = asyncio.get_event_loop()
        loop.run_until_complete(_cleanup_global_resources())
    except Exception as e:
        print(f"Error during cleanup: {e}")
    print("✅ Shutdown complete")
    exit(0)

# Register signal handlers
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

# Main execution
if __name__ == "__main__":
    print("🚀 Starting MCP Crawl4AI RAG Server (Refactored)")
    print("📊 Dashboard available at: http://localhost:8051/dashboard")
    print("🔧 Configuration:")
    print(f"   - Host: {os.getenv('HOST', '0.0.0.0')}")
    print(f"   - Port: {os.getenv('PORT', '8051')}")
    print(f"   - Transport: {os.getenv('TRANSPORT', 'sse')}")
    print(f"   - Environment: {os.getenv('ENVIRONMENT', 'development')}")
    print("✅ Server ready to accept connections")
    
    # Start the server
    mcp.run()
