<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Embedding Model Evaluation Dashboard</title>
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    <script src="https://unpkg.com/htmx.org/dist/ext/sse.js"></script>
    <style>
        /* Minimal CSS for clean dashboard */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .dashboard {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 20px;
        }
        
        .header h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 2.5em;
        }
        
        .header p {
            color: #7f8c8d;
            margin: 10px 0;
            font-size: 1.1em;
        }
        
        .form-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 8px;
            margin-bottom: 30px;
            border: 1px solid #e9ecef;
        }
        
        .form-section h2 {
            margin-top: 0;
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }
        
        .form-group input, .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus, .form-group select:focus {
            border-color: #3498db;
            outline: none;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #2980b9;
        }
        
        .btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
        }
        
        .progress {
            height: 25px;
            background: #e9ecef;
            border-radius: 12px;
            overflow: hidden;
            margin: 15px 0;
            box-shadow: inset 0 1px 3px rgba(0,0,0,0.2);
        }
        
        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #45a049);
            border-radius: 12px;
            transition: width 0.5s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 14px;
        }
        
        .status-section {
            background: #fff;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
            display: none;
        }
        
        .status-section.active {
            display: block;
        }
        
        .status-message {
            font-size: 16px;
            margin-bottom: 10px;
            color: #2c3e50;
        }
        
        .results-section {
            background: #fff;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            margin-top: 30px;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .comparison-table th,
        .comparison-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .comparison-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .faster {
            color: #27ae60;
            font-weight: 600;
        }
        
        .slower {
            color: #e74c3c;
            font-weight: 600;
        }
        
        .better {
            color: #27ae60;
            font-weight: 600;
        }
        
        .worse {
            color: #e74c3c;
            font-weight: 600;
        }
        
        .model-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .model-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #e9ecef;
        }
        
        .model-card h3 {
            margin: 0 0 10px 0;
            color: #2c3e50;
        }
        
        .model-card .dimension-info {
            font-size: 14px;
            color: #6c757d;
        }
        
        .spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .alert {
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .alert-error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .metric-value {
            font-size: 1.2em;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .metric-label {
            font-size: 0.9em;
            color: #7f8c8d;
        }
        
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <div class="header">
            <h1>🚀 Embedding Model Evaluation Dashboard</h1>
            <p>Compare OpenAI vs Ollama embedding performance for your RAG system</p>
        </div>

        <div class="form-section">
            <h2>🔬 Start New Benchmark Test</h2>
            
            <div class="model-info">
                <div class="model-card">
                    <h3>OpenAI Embeddings</h3>
                    <div class="dimension-info">Model: text-embedding-3-small<br>Dimensions: 1536<br>Cost: ~$0.02/1M tokens</div>
                </div>
                <div class="model-card">
                    <h3>Ollama Embeddings</h3>
                    <div class="dimension-info">Model: nomic-embed-text<br>Dimensions: 768<br>Cost: Free (local)</div>
                </div>
            </div>

            <form hx-post="/api/benchmark/start" 
                  hx-target="#results-container"
                  hx-indicator="#spinner">
                <div class="form-row">
                    <div class="form-group">
                        <label for="test_name">Test Name</label>
                        <input type="text" 
                               id="test_name" 
                               name="test_name" 
                               placeholder="e.g., FastAPI Documentation Test"
                               required>
                    </div>
                    <div class="form-group">
                        <label for="embedding_model">Embedding Model</label>
                        <select id="embedding_model" 
                                name="embedding_model" 
                                required>
                            <option value="">Select Model</option>
                            <option value="openai">OpenAI (text-embedding-3-small)</option>
                            <option value="ollama">Ollama (nomic-embed-text)</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="url">Documentation URL</label>
                    <input type="url" 
                           id="url" 
                           name="url" 
                           placeholder="https://docs.example.com or https://docs.example.com/sitemap.xml"
                           required>
                </div>
                
                <button type="submit" class="btn">
                    Start Benchmark Test
                    <span id="spinner" class="spinner"></span>
                </button>
            </form>
        </div>

        <div id="status-section" class="status-section">
            <h2>📊 Test Progress</h2>
            <div id="status-message" class="status-message">Initializing test...</div>
            <div class="progress">
                <div id="progress-bar" class="progress-bar" style="width: 0%">0%</div>
            </div>
        </div>

        <div id="results-container" class="results-section hidden">
            <!-- Results will be loaded here via HTMX -->
        </div>
    </div>

    <script>
        // Handle benchmark start response
        document.body.addEventListener('htmx:afterRequest', function(event) {
            if (event.detail.xhr.status === 200 && event.detail.target.id === 'results-container') {
                const response = JSON.parse(event.detail.xhr.responseText);
                if (response.success && response.test_run_id) {
                    // Show status section and start SSE connection
                    document.getElementById('status-section').classList.add('active');
                    startProgressTracking(response.test_run_id);
                }
            }
        });

        function startProgressTracking(testRunId) {
            const eventSource = new EventSource(`/api/benchmark/status/${testRunId}`);
            const progressBar = document.getElementById('progress-bar');
            const statusMessage = document.getElementById('status-message');

            eventSource.onmessage = function(event) {
                const data = JSON.parse(event.data);
                
                if (data.error) {
                    statusMessage.textContent = `Error: ${data.error}`;
                    progressBar.style.width = '100%';
                    progressBar.style.background = '#e74c3c';
                    progressBar.textContent = 'Error';
                    eventSource.close();
                    return;
                }

                // Update progress
                progressBar.style.width = data.progress + '%';
                progressBar.textContent = data.progress + '%';
                statusMessage.textContent = data.message;

                // Check if completed
                if (data.status === 'completed') {
                    loadResults(testRunId);
                    eventSource.close();
                } else if (data.status === 'failed') {
                    progressBar.style.background = '#e74c3c';
                    progressBar.textContent = 'Failed';
                    eventSource.close();
                }
            };

            eventSource.onerror = function(event) {
                console.error('SSE error:', event);
                statusMessage.textContent = 'Connection error - please refresh to check status';
                eventSource.close();
            };
        }

        function loadResults(testRunId) {
            fetch(`/api/benchmark/results/${testRunId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayResults(data);
                    } else {
                        document.getElementById('results-container').innerHTML = 
                            `<div class="alert alert-error">Error loading results: ${data.error}</div>`;
                    }
                })
                .catch(error => {
                    document.getElementById('results-container').innerHTML = 
                        `<div class="alert alert-error">Error: ${error.message}</div>`;
                });
        }

        function displayResults(data) {
            const resultsContainer = document.getElementById('results-container');
            const metrics = data.metrics;
            const testRun = data.test_run;

            resultsContainer.innerHTML = `
                <h2>📈 Benchmark Results</h2>
                
                <div class="alert alert-success">
                    <strong>Test Completed!</strong> ${testRun.test_name} finished successfully.
                </div>

                <div class="model-info">
                    <div class="model-card">
                        <h3>Test Configuration</h3>
                        <div>
                            <strong>Model:</strong> ${testRun.embedding_model}<br>
                            <strong>Strategy:</strong> ${testRun.embedding_strategy}<br>
                            <strong>Dimensions:</strong> ${testRun.vector_dimensions}<br>
                            <strong>URL:</strong> <a href="${testRun.source_url}" target="_blank">${testRun.source_url}</a>
                        </div>
                    </div>
                    <div class="model-card">
                        <h3>Performance Summary</h3>
                        <div>
                            <div class="metric-label">Total Duration</div>
                            <div class="metric-value">${metrics.total_duration_seconds}s</div>
                            <div class="metric-label">Avg Search Time</div>
                            <div class="metric-value">${metrics.avg_search_duration_ms}ms</div>
                        </div>
                    </div>
                </div>

                <table class="comparison-table">
                    <thead>
                        <tr>
                            <th>Metric</th>
                            <th>Value</th>
                            <th>Description</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Crawl Duration</td>
                            <td class="metric-value">${metrics.crawl_duration_seconds}s</td>
                            <td class="metric-label">Time to crawl and extract content</td>
                        </tr>
                        <tr>
                            <td>Embedding Duration</td>
                            <td class="metric-value">${metrics.embedding_duration_seconds}s</td>
                            <td class="metric-label">Time to generate embeddings</td>
                        </tr>
                        <tr>
                            <td>Average Search Time</td>
                            <td class="metric-value">${metrics.avg_search_duration_ms}ms</td>
                            <td class="metric-label">Average vector search response time</td>
                        </tr>
                        <tr>
                            <td>Average Similarity Score</td>
                            <td class="metric-value">${metrics.avg_similarity_score}</td>
                            <td class="metric-label">Average relevance score (0-1)</td>
                        </tr>
                        <tr>
                            <td>Test Queries</td>
                            <td class="metric-value">${metrics.total_queries}</td>
                            <td class="metric-label">Number of queries tested</td>
                        </tr>
                    </tbody>
                </table>
            `;
            
            resultsContainer.classList.remove('hidden');
        }
    </script>
</body>
</html>