"""
Input validation utilities for the MCP Crawl4AI RAG server.

This module provides comprehensive validation functions and decorators
to ensure data integrity and security across the communication layer.
"""

import re
import uuid
from typing import List, Dict, Any, Optional, Union, Callable
from urllib.parse import urlparse, urlunparse
from functools import wraps

from exceptions import ValidationError, create_validation_error


class URLValidator:
    """Comprehensive URL validation utilities."""
    
    # Common URL schemes that are allowed
    ALLOWED_SCHEMES = {'http', 'https'}
    
    # Domain patterns to block (security)
    BLOCKED_DOMAINS = {
        'localhost', '127.0.0.1', '0.0.0.0',
        '::1', 'local', 'internal'
    }
    
    # Maximum URL length for security
    MAX_URL_LENGTH = 2048
    
    @classmethod
    def is_valid_url(cls, url: str, allow_local: bool = False) -> bool:
        """
        Check if URL is valid and safe.
        
        Args:
            url: URL to validate
            allow_local: Whether to allow local/internal URLs
            
        Returns:
            True if URL is valid and safe
        """
        if not url or not isinstance(url, str):
            return False
        
        if len(url) > cls.MAX_URL_LENGTH:
            return False
        
        try:
            parsed = urlparse(url)
            
            # Check scheme
            if parsed.scheme not in cls.ALLOWED_SCHEMES:
                return False
            
            # Check if hostname exists
            if not parsed.netloc:
                return False
            
            # Check for blocked domains (unless explicitly allowed)
            if not allow_local:
                hostname = parsed.hostname
                if hostname and hostname.lower() in cls.BLOCKED_DOMAINS:
                    return False
            
            return True
            
        except Exception:
            return False
    
    @classmethod
    def validate_url(cls, url: str, allow_local: bool = False) -> str:
        """
        Validate and normalize URL.
        
        Args:
            url: URL to validate
            allow_local: Whether to allow local/internal URLs
            
        Returns:
            Normalized URL
            
        Raises:
            ValidationError: If URL is invalid
        """
        if not url:
            raise create_validation_error("url", "URL cannot be empty", url)
        
        if not isinstance(url, str):
            raise create_validation_error("url", "URL must be a string", url)
        
        url = url.strip()
        
        if len(url) > cls.MAX_URL_LENGTH:
            raise create_validation_error(
                "url",
                f"URL exceeds maximum length of {cls.MAX_URL_LENGTH} characters",
                url
            )
        
        if not cls.is_valid_url(url, allow_local):
            raise create_validation_error("url", "Invalid or unsafe URL format", url)
        
        # Normalize URL
        try:
            parsed = urlparse(url)
            normalized = urlunparse(parsed)
            return normalized
        except Exception as e:
            raise create_validation_error("url", f"Failed to normalize URL: {e}", url)
    
    @classmethod
    def validate_url_list(
        cls,
        urls: List[str],
        max_count: int = 100,
        allow_local: bool = False
    ) -> List[str]:
        """
        Validate and normalize a list of URLs.
        
        Args:
            urls: List of URLs to validate
            max_count: Maximum number of URLs allowed
            allow_local: Whether to allow local/internal URLs
            
        Returns:
            List of normalized URLs
            
        Raises:
            ValidationError: If any URL is invalid or list is too long
        """
        if not isinstance(urls, list):
            raise create_validation_error("urls", "URLs must be provided as a list", urls)
        
        if not urls:
            raise create_validation_error("urls", "URL list cannot be empty", urls)
        
        if len(urls) > max_count:
            raise create_validation_error(
                "urls",
                f"Too many URLs: {len(urls)} (maximum: {max_count})",
                len(urls)
            )
        
        validated_urls = []
        for i, url in enumerate(urls):
            try:
                validated_url = cls.validate_url(url, allow_local)
                validated_urls.append(validated_url)
            except ValidationError as e:
                raise create_validation_error(
                    f"urls[{i}]",
                    f"Invalid URL at index {i}: {e.message}",
                    url
                )
        
        return validated_urls


class JobValidator:
    """Job-related validation utilities."""
    
    # UUID pattern for job IDs
    UUID_PATTERN = re.compile(
        r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$',
        re.IGNORECASE
    )
    
    @classmethod
    def validate_job_id(cls, job_id: str) -> str:
        """
        Validate job ID format.
        
        Args:
            job_id: Job ID to validate
            
        Returns:
            Validated job ID
            
        Raises:
            ValidationError: If job ID is invalid
        """
        if not job_id:
            raise create_validation_error("job_id", "Job ID cannot be empty", job_id)
        
        if not isinstance(job_id, str):
            raise create_validation_error("job_id", "Job ID must be a string", job_id)
        
        job_id = job_id.strip()
        
        if not cls.UUID_PATTERN.match(job_id):
            raise create_validation_error(
                "job_id",
                "Job ID must be a valid UUID format",
                job_id
            )
        
        return job_id
    
    @classmethod
    def validate_timeout(cls, timeout: Union[int, float], min_timeout: float = 1.0, max_timeout: float = 3600.0) -> float:
        """
        Validate timeout value.
        
        Args:
            timeout: Timeout value in seconds
            min_timeout: Minimum allowed timeout
            max_timeout: Maximum allowed timeout
            
        Returns:
            Validated timeout as float
            
        Raises:
            ValidationError: If timeout is invalid
        """
        if timeout is None:
            raise create_validation_error("timeout", "Timeout cannot be None", timeout)
        
        try:
            timeout_float = float(timeout)
        except (TypeError, ValueError):
            raise create_validation_error("timeout", "Timeout must be a number", timeout)
        
        if timeout_float < min_timeout:
            raise create_validation_error(
                "timeout",
                f"Timeout too small: {timeout_float} (minimum: {min_timeout})",
                timeout
            )
        
        if timeout_float > max_timeout:
            raise create_validation_error(
                "timeout",
                f"Timeout too large: {timeout_float} (maximum: {max_timeout})",
                timeout
            )
        
        return timeout_float


class ConfigValidator:
    """Configuration validation utilities."""
    
    @classmethod
    def validate_config_dict(cls, config: Optional[Dict[str, Any]], max_keys: int = 50) -> Dict[str, Any]:
        """
        Validate configuration dictionary.
        
        Args:
            config: Configuration dictionary to validate
            max_keys: Maximum number of keys allowed
            
        Returns:
            Validated configuration dictionary
            
        Raises:
            ValidationError: If configuration is invalid
        """
        if config is None:
            return {}
        
        if not isinstance(config, dict):
            raise create_validation_error("config", "Configuration must be a dictionary", config)
        
        if len(config) > max_keys:
            raise create_validation_error(
                "config",
                f"Too many configuration keys: {len(config)} (maximum: {max_keys})",
                len(config)
            )
        
        # Validate that all keys are strings
        for key in config.keys():
            if not isinstance(key, str):
                raise create_validation_error(
                    "config",
                    f"Configuration key must be string, got {type(key).__name__}",
                    key
                )
        
        return config


def validate_required_params(**validations):
    """
    Decorator to validate required parameters for async functions.
    
    Args:
        **validations: Dictionary of parameter names and their validation functions
    
    Usage:
        @validate_required_params(
            urls=lambda x: URLValidator.validate_url_list(x),
            timeout=lambda x: JobValidator.validate_timeout(x)
        )
        async def some_function(urls, timeout):
            pass
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Get function parameter names
            import inspect
            sig = inspect.signature(func)
            bound_args = sig.bind(*args, **kwargs)
            bound_args.apply_defaults()
            
            # Validate each specified parameter
            for param_name, validator in validations.items():
                if param_name in bound_args.arguments:
                    value = bound_args.arguments[param_name]
                    try:
                        validated_value = validator(value)
                        bound_args.arguments[param_name] = validated_value
                    except ValidationError:
                        raise
                    except Exception as e:
                        raise create_validation_error(
                            param_name,
                            f"Validation failed: {e}",
                            value
                        )
            
            # Call function with validated parameters
            return await func(*bound_args.args, **bound_args.kwargs)
        
        return wrapper
    return decorator


def validate_sync_required_params(**validations):
    """
    Decorator to validate required parameters for sync functions.
    
    Args:
        **validations: Dictionary of parameter names and their validation functions
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Get function parameter names
            import inspect
            sig = inspect.signature(func)
            bound_args = sig.bind(*args, **kwargs)
            bound_args.apply_defaults()
            
            # Validate each specified parameter
            for param_name, validator in validations.items():
                if param_name in bound_args.arguments:
                    value = bound_args.arguments[param_name]
                    try:
                        validated_value = validator(value)
                        bound_args.arguments[param_name] = validated_value
                    except ValidationError:
                        raise
                    except Exception as e:
                        raise create_validation_error(
                            param_name,
                            f"Validation failed: {e}",
                            value
                        )
            
            # Call function with validated parameters
            return func(*bound_args.args, **bound_args.kwargs)
        
        return wrapper
    return decorator


# Convenience validation functions
def validate_url(url: str, allow_local: bool = False) -> str:
    """Convenience function for URL validation."""
    return URLValidator.validate_url(url, allow_local)


def validate_url_list(urls: List[str], max_count: int = 100, allow_local: bool = False) -> List[str]:
    """Convenience function for URL list validation."""
    return URLValidator.validate_url_list(urls, max_count, allow_local)


def validate_job_id(job_id: str) -> str:
    """Convenience function for job ID validation."""
    return JobValidator.validate_job_id(job_id)


def validate_timeout(timeout: Union[int, float]) -> float:
    """Convenience function for timeout validation."""
    return JobValidator.validate_timeout(timeout)


def validate_config(config: Optional[Dict[str, Any]]) -> Dict[str, Any]:
    """Convenience function for configuration validation."""
    return ConfigValidator.validate_config_dict(config)