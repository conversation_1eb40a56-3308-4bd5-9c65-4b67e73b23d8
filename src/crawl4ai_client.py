"""
HTTP client for Crawl4AI worker service
Replaces direct crawl4ai imports in MCP server
"""

import asyncio
import aiohttp
import json
import time
import logging
from typing import Dict, Any, List, Optional
import os

# Import our standardized error handling and validation
from exceptions import (
    Crawl4AIError,
    NetworkError,
    JobError,
    TimeoutError,
    ServiceUnavailableError,
    create_job_error,
    create_timeout_error,
    create_service_unavailable_error
)
from error_handler import (
    handle_async_errors,
    handle_http_response,
    safe_http_request
)
from validators import (
    validate_url_list,
    validate_job_id,
    validate_timeout,
    validate_config,
    validate_required_params
)

# Configure logging
logger = logging.getLogger(__name__)

class Crawl4AIClient:
    """HTTP client for communicating with Crawl4AI worker service"""
    
    def __init__(self, host: str = None, port: int = None):
        self.host = host or os.getenv('CRAWL4AI_HOST', 'localhost')
        self.port = port or int(os.getenv('CRAWL4AI_PORT', '11235'))
        self.base_url = f"http://{self.host}:{self.port}"
        self.timeout = aiohttp.ClientTimeout(total=300)  # 5 minutes
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
    @validate_required_params(
        urls=lambda x: validate_url_list(x, max_count=100),
        crawler_config=lambda x: validate_config(x),
        browser_config=lambda x: validate_config(x)
    )
    @handle_async_errors("job submission")
    async def submit_crawl_job(self, urls: List[str], crawler_config: Optional[Dict] = None, browser_config: Optional[Dict] = None) -> str:
        """Submit a crawl job to the worker and return job ID.
        
        Args:
            urls: List of URLs to crawl (max 100)
            crawler_config: Optional crawler configuration
            browser_config: Optional browser configuration
            
        Returns:
            Job ID string
            
        Raises:
            ValidationError: If inputs are invalid
            NetworkError: If HTTP request fails
            JobError: If job submission fails
        """
        payload = {
            "urls": urls,
            "crawler_config": crawler_config or {},
            "browser_config": browser_config or {}
        }
        
        async with aiohttp.ClientSession(timeout=self.timeout) as session:
            async with session.post(f"{self.base_url}/crawl/job", json=payload) as response:
                if response.status != 200:
                    response_text = await response.text()
                    handle_http_response(
                        response.status,
                        response_text,
                        f"{self.base_url}/crawl/job",
                        "job submission"
                    )
                
                try:
                    result = await response.json()
                    task_id = result.get("task_id")
                    if not task_id:
                        raise create_job_error(
                            "unknown",
                            "No task_id in response",
                            "submission_failed"
                        )
                    return task_id
                except (KeyError, ValueError) as e:
                    raise create_job_error(
                        "unknown",
                        f"Invalid response format: {e}",
                        "submission_failed"
                    )
    
    @validate_required_params(
        job_id=lambda x: validate_job_id(x)
    )
    @handle_async_errors("job status check")
    async def get_job_status(self, job_id: str) -> Dict[str, Any]:
        """Get job status and results.
        
        Args:
            job_id: Valid UUID format job ID
            
        Returns:
            Dictionary containing job status and results
            
        Raises:
            ValidationError: If job_id is invalid
            JobError: If job not found or status check fails
            NetworkError: If HTTP request fails
        """
        async with aiohttp.ClientSession(timeout=self.timeout) as session:
            async with session.get(f"{self.base_url}/crawl/job/{job_id}") as response:
                if response.status == 404:
                    raise create_job_error(
                        job_id,
                        "Job not found",
                        "not_found"
                    )
                elif response.status != 200:
                    response_text = await response.text()
                    handle_http_response(
                        response.status,
                        response_text,
                        f"{self.base_url}/crawl/job/{job_id}",
                        "job status check"
                    )
                
                try:
                    return await response.json()
                except ValueError as e:
                    raise create_job_error(
                        job_id,
                        f"Invalid JSON response: {e}",
                        "invalid_response"
                    )
    
    @validate_required_params(
        job_id=lambda x: validate_job_id(x),
        poll_interval=lambda x: validate_timeout(x, min_timeout=0.1, max_timeout=60.0),
        max_wait=lambda x: validate_timeout(x, min_timeout=10.0, max_timeout=3600.0)
    )
    @handle_async_errors("job completion wait")
    async def wait_for_job_completion(self, job_id: str, poll_interval: float = 1.0, max_wait: int = 600) -> Dict[str, Any]:
        """Wait for job to complete and return results.
        
        Args:
            job_id: Valid UUID format job ID
            poll_interval: Seconds between status checks (0.1-60.0)
            max_wait: Maximum seconds to wait (10-3600)
            
        Returns:
            Dictionary containing job results
            
        Raises:
            ValidationError: If parameters are invalid
            TimeoutError: If job doesn't complete within max_wait
            JobError: If job fails or is cancelled
        """
        start_time = time.time()
        
        while True:
            if time.time() - start_time > max_wait:
                raise create_timeout_error(
                    f"job {job_id} completion wait",
                    max_wait
                )
            
            status = await self.get_job_status(job_id)
            job_status = status.get("status", "UNKNOWN")
            
            if job_status == "COMPLETED":
                result = status.get("result")
                if result is None:
                    raise create_job_error(
                        job_id,
                        "Job completed but no result provided",
                        job_status
                    )
                return result
            elif job_status == "FAILED":
                error_msg = status.get('error', 'Unknown error')
                raise create_job_error(
                    job_id,
                    f"Job failed: {error_msg}",
                    job_status
                )
            elif job_status == "CANCELLED":
                raise create_job_error(
                    job_id,
                    "Job was cancelled",
                    job_status
                )
            
            await asyncio.sleep(poll_interval)
    
    @validate_required_params(
        urls=lambda x: validate_url_list(x, max_count=100),
        crawler_config=lambda x: validate_config(x),
        browser_config=lambda x: validate_config(x)
    )
    @handle_async_errors("URL crawling")
    async def crawl_urls(self, urls: List[str], crawler_config: Optional[Dict] = None, browser_config: Optional[Dict] = None) -> List[Dict[str, Any]]:
        """Crawl URLs and return results (blocking call).
        
        Args:
            urls: List of URLs to crawl (max 100)
            crawler_config: Optional crawler configuration
            browser_config: Optional browser configuration
            
        Returns:
            List of crawl results
            
        Raises:
            ValidationError: If inputs are invalid
            JobError: If job submission or processing fails
            TimeoutError: If job doesn't complete in time
        """
        job_id = await self.submit_crawl_job(urls, crawler_config, browser_config)
        result = await self.wait_for_job_completion(job_id)
        crawl_results = result.get("crawl_results", [])
        
        if not isinstance(crawl_results, list):
            raise create_job_error(
                job_id,
                "Invalid result format: expected list of crawl results",
                "completed"
            )
        
        return crawl_results
    
    async def health_check(self) -> bool:
        """Check if worker service is healthy.
        
        Returns:
            True if service is healthy, False otherwise
        """
        try:
            timeout = aiohttp.ClientTimeout(total=5)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(f"{self.base_url}/health") as response:
                    return response.status == 200
        except (aiohttp.ClientError, asyncio.TimeoutError) as e:
            self.logger.warning(f"Health check failed: {e}")
            return False
        except Exception as e:
            self.logger.error(f"Unexpected error during health check: {e}")
            return False

# Mock classes for compatibility with existing code
class MockBrowserConfig:
    """Mock BrowserConfig for compatibility"""
    def __init__(self, **kwargs):
        self.config = kwargs

class MockCrawlerRunConfig:
    """Mock CrawlerRunConfig for compatibility"""
    def __init__(self, **kwargs):
        self.config = kwargs

class MockCacheMode:
    """Mock CacheMode for compatibility"""
    ENABLED = "enabled"
    DISABLED = "disabled"
    BYPASS = "bypass"

class MockMemoryAdaptiveDispatcher:
    """Mock MemoryAdaptiveDispatcher for compatibility"""
    def __init__(self, **kwargs):
        pass

# Mock result class
class MockCrawlResult:
    """Mock crawl result for compatibility"""
    def __init__(self, data: Dict[str, Any]):
        self.success = data.get("success", False)
        self.html = data.get("html", "")
        self.cleaned_html = data.get("cleaned_html", "")
        self.markdown = data.get("markdown", "")
        self.extracted_content = data.get("extracted_content", "")
        self.metadata = data.get("metadata", {})
        self.error_message = data.get("error", "")
        self.links = data.get("links", {"internal": [], "external": []})

# Compatibility layer - replaces AsyncWebCrawler
class AsyncWebCrawler:
    """Compatibility layer that uses HTTP client instead of direct crawl4ai"""
    
    def __init__(self, config: Optional[MockBrowserConfig] = None):
        self.config = config
        self.client = Crawl4AIClient()
    
    async def __aenter__(self):
        # Check if worker is available
        is_healthy = await self.client.health_check()
        if not is_healthy:
            raise create_service_unavailable_error(
                "Crawl4AI worker",
                self.client.base_url
            )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        pass
    
    async def arun(self, url: str, config: Optional[MockCrawlerRunConfig] = None) -> MockCrawlResult:
        """Crawl a single URL"""
        crawler_config = config.config if config else {}
        browser_config = self.config.config if self.config else {}
        
        results = await self.client.crawl_urls(
            urls=[url],
            crawler_config=crawler_config,
            browser_config=browser_config
        )
        
        if not results:
            return MockCrawlResult({"success": False, "error": "No results returned"})
        
        return MockCrawlResult(results[0])

# Export compatibility names
BrowserConfig = MockBrowserConfig
CrawlerRunConfig = MockCrawlerRunConfig
CacheMode = MockCacheMode
MemoryAdaptiveDispatcher = MockMemoryAdaptiveDispatcher