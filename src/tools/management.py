"""
Management tools for MCP Crawl4AI RAG server.

This module contains MCP tools for job management and asynchronous operations.
"""

import os
import json
from typing import List, Dict, Any, Optional

from mcp.server.fastmcp import Context
from server.dependencies import get_resources, Crawl4AIContext
from server.mcp_server import mcp
from core.utils import create_standard_error_response, create_standard_success_response


@mcp.tool()
async def start_crawl_job(ctx: Context, urls: List[str], crawler_config: Optional[Dict[str, Any]] = None, browser_config: Optional[Dict[str, Any]] = None) -> str:
    """
    Start an asynchronous crawl job for long-running operations.
    
    This tool submits a crawl job to the Crawl4AI job management system, allowing
    long-running crawl operations to execute asynchronously without timing out
    Claude Desktop. The job will run in the background and can be monitored
    using check_job_status and retrieved using get_job_result.
    
    Args:
        ctx: MCP context
        urls: List of URLs to crawl
        crawler_config: Optional crawler configuration (e.g., cache_mode, extraction_strategy)
        browser_config: Optional browser configuration (e.g., headless, user_agent)
    
    Returns:
        JSON string with job submission result including job_id for tracking
    """
    try:
        # Get resources from context
        resources = get_resources()
        job_manager = resources.job_manager
        
        if not job_manager:
            return create_standard_error_response(
                error="Job manager not available",
                context="Job management system not initialized",
                error_type="system_error"
            )
        
        # Submit the job
        job_id = await job_manager.submit_job(urls, crawler_config, browser_config)
        
        return create_standard_success_response(
            data={
                "job_id": job_id,
                "urls": urls,
                "status": "submitted",
                "message": "Job submitted successfully"
            },
            context="Job submitted to processing queue"
        )
        
    except Exception as e:
        return create_standard_error_response(
            error=str(e),
            context="Failed to submit crawl job",
            error_type="submission_error",
            details={"urls": urls}
        )


@mcp.tool()
async def check_job_status(ctx: Context, job_id: str) -> str:
    """
    Check the status of an asynchronous crawl job.
    
    This tool monitors the progress of a previously submitted crawl job,
    providing real-time status updates including progress percentage,
    current status, and any error messages.
    
    Args:
        job_id: The job ID returned from start_crawl_job
        resources: Injected resources containing job manager
    
    Returns:
        JSON string with current job status, progress, and information
    """
    try:
        # Get the job manager from resources
        job_manager = resources.job_manager
        
        if not job_manager:
            return create_standard_error_response(
                error="Job manager not available",
                context="Job management system not initialized",
                error_type="system_error"
            )
        
        # Check job status
        status = await job_manager.get_job_status(job_id)
        
        return create_standard_success_response(
            data=status,
            context=f"Status retrieved for job {job_id}"
        )
        
    except Exception as e:
        return create_standard_error_response(
            error=str(e),
            context=f"Failed to check status for job {job_id}",
            error_type="status_error",
            details={"job_id": job_id}
        )


async def get_job_result(job_id: str, wait_for_completion: bool = False, timeout_seconds: Optional[int] = None, resources: Crawl4AIContext = get_resources) -> str:
    """
    Retrieve the result of a completed crawl job.
    
    This tool gets the final result of a crawl job. It can optionally wait
    for job completion if the job is still running. The result includes
    all crawled content, metadata, and processing statistics.
    
    Args:
        job_id: The job ID returned from start_crawl_job
        wait_for_completion: Whether to wait for the job to complete if still running
        timeout_seconds: Maximum time to wait for completion (default: no timeout)
        resources: Injected resources containing job manager
    
    Returns:
        JSON string with complete job result including all crawled data
    """
    try:
        # Get the job manager from resources
        job_manager = resources.job_manager
        
        if not job_manager:
            return create_standard_error_response(
                error="Job manager not available",
                context="Job management system not initialized",
                error_type="system_error"
            )
        
        # Get job result
        result = await job_manager.get_job_result(job_id, wait_for_completion, timeout_seconds)
        
        return create_standard_success_response(
            data=result,
            context=f"Result retrieved for job {job_id}"
        )
        
    except Exception as e:
        return create_standard_error_response(
            error=str(e),
            context=f"Failed to retrieve result for job {job_id}",
            error_type="result_error",
            details={"job_id": job_id}
        )


async def cancel_crawl_job(job_id: str, resources: Crawl4AIContext = get_resources) -> str:
    """
    Cancel a running crawl job.
    
    This tool cancels a previously submitted crawl job that is currently
    running or pending. Once cancelled, the job cannot be resumed.
    
    Args:
        job_id: The job ID to cancel
        resources: Injected resources containing job manager
    
    Returns:
        JSON string with cancellation result
    """
    try:
        # Get the job manager from resources
        job_manager = resources.job_manager
        
        if not job_manager:
            return create_standard_error_response(
                error="Job manager not available",
                context="Job management system not initialized",
                error_type="system_error"
            )
        
        # Cancel the job
        result = await job_manager.cancel_job(job_id)
        
        return create_standard_success_response(
            data=result,
            context=f"Job {job_id} cancellation processed"
        )
        
    except Exception as e:
        return create_standard_error_response(
            error=str(e),
            context=f"Failed to cancel job {job_id}",
            error_type="cancellation_error",
            details={"job_id": job_id}
        )


async def list_crawl_jobs(resources: Crawl4AIContext = get_resources) -> str:
    """
    List all active crawl jobs.
    
    This tool returns a list of all currently tracked crawl jobs,
    including their status, progress, and basic information.
    
    Args:
        resources: Injected resources containing job manager
    
    Returns:
        JSON string with list of all active jobs
    """
    try:
        # Get the job manager from resources
        job_manager = resources.job_manager
        
        if not job_manager:
            return create_standard_error_response(
                error="Job manager not available",
                context="Job management system not initialized",
                error_type="system_error"
            )
        
        # List all jobs
        jobs = await job_manager.list_jobs()
        
        return create_standard_success_response(
            data={
                "jobs": jobs,
                "count": len(jobs)
            },
            context="Job list retrieved successfully"
        )
        
    except Exception as e:
        return create_standard_error_response(
            error=str(e),
            context="Failed to list jobs",
            error_type="listing_error"
        )
