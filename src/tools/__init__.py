"""
MCP Tools module for Crawl4AI RAG server.

This module contains all the MCP tools organized by domain:
- crawling: Web crawling and content extraction
- search: RAG queries and search functionality
- knowledge_graph: Neo4j-based hallucination detection
- management: Job management and reliability monitoring
"""

from .crawling import crawl_single_page, smart_crawl_url
from .search import perform_rag_query, search_code_examples, get_available_sources
from .knowledge_graph import check_ai_script_hallucinations, query_knowledge_graph, parse_github_repository
from .management import start_crawl_job, check_job_status, get_job_result, cancel_crawl_job, list_crawl_jobs
from .reliability import get_reliability_metrics, reset_reliability_state, test_url_health

__all__ = [
    # Crawling tools
    "crawl_single_page",
    "smart_crawl_url",
    
    # Search tools  
    "perform_rag_query",
    "search_code_examples",
    "get_available_sources",
    
    # Knowledge graph tools
    "check_ai_script_hallucinations",
    "query_knowledge_graph", 
    "parse_github_repository",
    
    # Management tools
    "start_crawl_job",
    "check_job_status",
    "get_job_result",
    "cancel_crawl_job",
    "list_crawl_jobs",
    
    # Reliability tools
    "get_reliability_metrics",
    "reset_reliability_state",
    "test_url_health"
]