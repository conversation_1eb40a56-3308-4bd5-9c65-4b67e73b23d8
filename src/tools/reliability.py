"""
Reliability tools for MCP Crawl4AI RAG server.

This module contains MCP tools for reliability monitoring and system health.
"""

import os
import json
from typing import Dict, Any

# Tool decorator removed - using mcp.tool() registration instead
from server.dependencies import get_resources, Crawl4AIContext
from core.utils import create_standard_error_response, create_standard_success_response


async def get_reliability_metrics(resources: Crawl4AIContext = get_resources) -> str:
    """
    Get current reliability metrics for the crawling service.
    
    This tool returns comprehensive metrics including:
    - Total requests processed
    - Success and failure rates
    - Retry statistics
    - Error type distribution
    - Response time statistics
    - Circuit breaker status
    - Cache hit rates
    
    Args:
        resources: Injected resources containing reliability service
    
    Returns:
        JSON string with detailed reliability metrics
    """
    try:
        # Get the reliability service from resources
        reliability_service = resources.reliability_service
        
        if not reliability_service:
            return create_standard_error_response(
                error="Reliability service not available",
                context="Reliability monitoring system not initialized",
                error_type="system_error"
            )
        
        # Get metrics
        metrics = await reliability_service.get_metrics()
        
        return create_standard_success_response(
            data=metrics,
            context="Reliability metrics retrieved successfully"
        )
        
    except Exception as e:
        return create_standard_error_response(
            error=str(e),
            context="Failed to retrieve reliability metrics",
            error_type="metrics_error"
        )


async def reset_reliability_state(resources: Crawl4AIContext = get_resources) -> str:
    """
    Reset reliability service state (circuit breaker and cache).
    
    This tool allows manual intervention to reset the reliability service state:
    - Resets circuit breaker to CLOSED state
    - Clears all cached content
    - Resets failure counters
    
    Use this tool when you need to recover from persistent failures or
    clear stale cached content.
    
    Args:
        resources: Injected resources containing reliability service
    
    Returns:
        JSON string with reset operation results
    """
    try:
        # Get the reliability service from resources
        reliability_service = resources.reliability_service
        
        if not reliability_service:
            return create_standard_error_response(
                error="Reliability service not available",
                context="Reliability monitoring system not initialized",
                error_type="system_error"
            )
        
        # Reset state
        result = await reliability_service.reset_state()
        
        return create_standard_success_response(
            data=result,
            context="Reliability state reset successfully"
        )
        
    except Exception as e:
        return create_standard_error_response(
            error=str(e),
            context="Failed to reset reliability state",
            error_type="reset_error"
        )


async def test_url_health(url: str, resources: Crawl4AIContext = get_resources) -> str:
    """
    Test the health and accessibility of a URL before crawling.
    
    This tool performs comprehensive health checks including:
    - Connection validation
    - DNS resolution
    - HTTP response status
    - Response time measurement
    
    Use this tool to diagnose connectivity issues before attempting to crawl.
    
    Args:
        url: URL to test for health and accessibility
        resources: Injected resources containing reliability service
    
    Returns:
        JSON string with health check results
    """
    try:
        # Get the reliability service from resources
        reliability_service = resources.reliability_service
        
        if not reliability_service:
            return create_standard_error_response(
                error="Reliability service not available",
                context="Reliability monitoring system not initialized",
                error_type="system_error"
            )
        
        # Test URL health
        health_result = await reliability_service.test_url_health(url)
        
        return create_standard_success_response(
            data=health_result,
            context=f"Health check completed for {url}"
        )
        
    except Exception as e:
        return create_standard_error_response(
            error=str(e),
            context=f"Health check failed for {url}",
            error_type="health_check_error",
            details={"url": url}
        )
