"""
Custom exception hierarchy for MCP Crawl4AI RAG server.

This module provides specific exception types for better error handling
and debugging across the communication layer.
"""

from typing import Optional, Dict, Any
from enum import Enum


class ErrorSeverity(Enum):
    """Error severity levels for categorization."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class Crawl4AIError(Exception):
    """
    Base exception for all Crawl4AI related errors.
    
    Provides structured error handling with severity levels,
    error codes, and additional context.
    """
    
    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        context: Optional[Dict[str, Any]] = None,
        original_error: Optional[Exception] = None
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.severity = severity
        self.context = context or {}
        self.original_error = original_error
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary for API responses."""
        return {
            "error": self.__class__.__name__,
            "message": self.message,
            "error_code": self.error_code,
            "severity": self.severity.value,
            "context": self.context,
            "original_error": str(self.original_error) if self.original_error else None
        }


class ValidationError(Crawl4AIError):
    """
    Raised when input validation fails.
    
    Used for invalid URLs, parameters, or configuration values.
    """
    
    def __init__(
        self,
        message: str,
        field_name: Optional[str] = None,
        field_value: Optional[Any] = None,
        **kwargs
    ):
        context = kwargs.get('context', {})
        if field_name:
            context['field_name'] = field_name
        if field_value is not None:
            context['field_value'] = str(field_value)
        
        super().__init__(
            message,
            error_code="VALIDATION_ERROR",
            severity=ErrorSeverity.MEDIUM,
            context=context,
            **{k: v for k, v in kwargs.items() if k != 'context'}
        )


class NetworkError(Crawl4AIError):
    """
    Raised when network communication fails.
    
    Includes connection errors, DNS failures, and HTTP errors.
    """
    
    def __init__(
        self,
        message: str,
        url: Optional[str] = None,
        status_code: Optional[int] = None,
        **kwargs
    ):
        context = kwargs.get('context', {})
        if url:
            context['url'] = url
        if status_code:
            context['status_code'] = status_code
        
        super().__init__(
            message,
            error_code="NETWORK_ERROR",
            severity=ErrorSeverity.HIGH,
            context=context,
            **{k: v for k, v in kwargs.items() if k != 'context'}
        )


class JobError(Crawl4AIError):
    """
    Raised when job operations fail.
    
    Includes job submission, status checking, and result retrieval failures.
    """
    
    def __init__(
        self,
        message: str,
        job_id: Optional[str] = None,
        job_status: Optional[str] = None,
        **kwargs
    ):
        context = kwargs.get('context', {})
        if job_id:
            context['job_id'] = job_id
        if job_status:
            context['job_status'] = job_status
        
        super().__init__(
            message,
            error_code="JOB_ERROR",
            severity=ErrorSeverity.HIGH,
            context=context,
            **{k: v for k, v in kwargs.items() if k != 'context'}
        )


class TimeoutError(Crawl4AIError):
    """
    Raised when operations exceed timeout limits.
    
    Includes connection timeouts, read timeouts, and job timeouts.
    """
    
    def __init__(
        self,
        message: str,
        timeout_duration: Optional[float] = None,
        operation_type: Optional[str] = None,
        **kwargs
    ):
        context = kwargs.get('context', {})
        if timeout_duration:
            context['timeout_duration'] = timeout_duration
        if operation_type:
            context['operation_type'] = operation_type
        
        super().__init__(
            message,
            error_code="TIMEOUT_ERROR",
            severity=ErrorSeverity.HIGH,
            context=context,
            **{k: v for k, v in kwargs.items() if k != 'context'}
        )


class ServiceUnavailableError(Crawl4AIError):
    """
    Raised when external services are unavailable.
    
    Includes worker service down, health check failures.
    """
    
    def __init__(
        self,
        message: str,
        service_name: Optional[str] = None,
        service_url: Optional[str] = None,
        **kwargs
    ):
        context = kwargs.get('context', {})
        if service_name:
            context['service_name'] = service_name
        if service_url:
            context['service_url'] = service_url
        
        super().__init__(
            message,
            error_code="SERVICE_UNAVAILABLE",
            severity=ErrorSeverity.CRITICAL,
            context=context,
            **{k: v for k, v in kwargs.items() if k != 'context'}
        )


class ConfigurationError(Crawl4AIError):
    """
    Raised when configuration is invalid or missing.
    
    Includes missing environment variables, invalid configurations.
    """
    
    def __init__(
        self,
        message: str,
        config_key: Optional[str] = None,
        **kwargs
    ):
        context = kwargs.get('context', {})
        if config_key:
            context['config_key'] = config_key
        
        super().__init__(
            message,
            error_code="CONFIGURATION_ERROR",
            severity=ErrorSeverity.HIGH,
            context=context,
            **{k: v for k, v in kwargs.items() if k != 'context'}
        )


class RateLimitError(Crawl4AIError):
    """
    Raised when rate limits are exceeded.
    
    Includes API rate limits, concurrent request limits.
    """
    
    def __init__(
        self,
        message: str,
        retry_after: Optional[float] = None,
        **kwargs
    ):
        context = kwargs.get('context', {})
        if retry_after:
            context['retry_after'] = retry_after
        
        super().__init__(
            message,
            error_code="RATE_LIMIT_ERROR",
            severity=ErrorSeverity.MEDIUM,
            context=context,
            **{k: v for k, v in kwargs.items() if k != 'context'}
        )


# Convenience functions for creating specific errors
def create_validation_error(field_name: str, message: str, field_value: Any = None) -> ValidationError:
    """Create a validation error with field context."""
    return ValidationError(
        f"Validation failed for '{field_name}': {message}",
        field_name=field_name,
        field_value=field_value
    )


def create_network_error(url: str, status_code: int, message: str) -> NetworkError:
    """Create a network error with URL and status context."""
    return NetworkError(
        f"Network error for {url}: {message}",
        url=url,
        status_code=status_code
    )


def create_job_error(job_id: str, message: str, job_status: str = None) -> JobError:
    """Create a job error with job context."""
    return JobError(
        f"Job {job_id} failed: {message}",
        job_id=job_id,
        job_status=job_status
    )


def create_timeout_error(operation: str, timeout: float) -> TimeoutError:
    """Create a timeout error with operation context."""
    return TimeoutError(
        f"{operation} timed out after {timeout} seconds",
        timeout_duration=timeout,
        operation_type=operation
    )


def create_service_unavailable_error(service_name: str, service_url: str) -> ServiceUnavailableError:
    """Create a service unavailable error with service context."""
    return ServiceUnavailableError(
        f"Service '{service_name}' is unavailable at {service_url}",
        service_name=service_name,
        service_url=service_url
    )