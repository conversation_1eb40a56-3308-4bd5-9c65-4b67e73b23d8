"""
Job Management Module for Crawl4AI Integration

This module provides job management capabilities for long-running crawl operations
by integrating with Crawl4AI's built-in HTTP API server. It handles the lifecycle
of asynchronous crawl jobs to prevent Claude Desktop timeout issues.
"""

import asyncio
import json
import logging
import os
import subprocess
import time
from dataclasses import dataclass
from enum import Enum
from typing import Dict, List, Optional, Any, Tuple
import aiohttp
import signal
from pathlib import Path

# Import our standardized error handling and validation
from exceptions import (
    Crawl4AIError,
    NetworkError,
    JobError,
    TimeoutError,
    ServiceUnavailableError,
    create_job_error,
    create_timeout_error,
    create_service_unavailable_error
)
from error_handler import (
    handle_async_errors,
    handle_http_response,
    error_handler
)
from validators import (
    validate_url_list,
    validate_job_id,
    validate_timeout,
    validate_config,
    validate_required_params
)


class JobStatus(Enum):
    """Job status enumeration matching Crawl4AI's job states."""
    PENDING = "PENDING"
    RUNNING = "RUNNING" 
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"
    CANCELLED = "CANCELLED"


@dataclass
class JobResult:
    """Result container for async crawl jobs with URL-level tracking."""
    job_id: str
    status: JobStatus
    progress: float = 0.0
    message: str = ""
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    # Enhanced tracking for URL-level success/failure reporting
    total_urls: int = 0
    successful_urls: int = 0
    failed_urls: int = 0
    url_results: Optional[Dict[str, Dict[str, Any]]] = None  # URL -> {status, error, result}
    
    @property
    def success_rate(self) -> float:
        """Calculate the success rate of URL processing."""
        if self.total_urls == 0:
            return 0.0
        return self.successful_urls / self.total_urls
    
    @property 
    def is_partial_success(self) -> bool:
        """Check if job had partial success (some URLs failed)."""
        return self.successful_urls > 0 and self.failed_urls > 0
    
    @property
    def job_summary(self) -> str:
        """Get a human-readable summary of the job status."""
        if self.total_urls == 0:
            return "No URLs processed"
        elif self.failed_urls == 0:
            return f"All {self.successful_urls} URLs processed successfully"
        elif self.successful_urls == 0:
            return f"All {self.failed_urls} URLs failed to process"
        else:
            return f"{self.successful_urls}/{self.total_urls} URLs successful, {self.failed_urls} failed"


class Crawl4AIJobManager:
    """
    Manages asynchronous crawl jobs using Crawl4AI's HTTP API server.
    
    This class handles:
    - Starting and stopping the Crawl4AI HTTP server
    - Submitting async crawl jobs
    - Monitoring job status and progress
    - Retrieving job results
    - Graceful cleanup and error handling
    """

    def __init__(
        self,
        api_host: str = "localhost",
        api_port: int = 11235,
        server_startup_timeout: int = 30,
        max_poll_attempts: int = 600,
        poll_interval: float = 1.0,
        connection_retries: int = 3,
        connection_timeout: int = 30
    ):
        self.api_host = api_host
        self.api_port = api_port
        self.api_base_url = f"http://{api_host}:{api_port}"
        self.server_startup_timeout = server_startup_timeout
        self.max_poll_attempts = max_poll_attempts
        self.poll_interval = poll_interval
        self.connection_retries = connection_retries
        self.connection_timeout = connection_timeout
        
        # Remove subprocess management - external service
        self.session: Optional[aiohttp.ClientSession] = None
        self.active_jobs: Dict[str, JobResult] = {}
        
        self.logger = logging.getLogger(__name__)
        
        # Flag for external service mode
        self._external_service_mode = True

    async def __aenter__(self):
        """Async context manager entry."""
        await self.start()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.cleanup()

    async def start(self):
        """Initialize the job manager and connect to external Crawl4AI service."""
        # Ensure any existing session is closed first
        if self.session and not self.session.closed:
            try:
                await self.session.close()
            except Exception:
                pass  # Ignore cleanup errors
        
        # Create HTTP session with longer timeout for external service
        timeout = aiohttp.ClientTimeout(total=self.connection_timeout)
        self.session = aiohttp.ClientSession(timeout=timeout)
        
        try:
            # Wait for external Crawl4AI service to be ready with retries
            await self._wait_for_external_service()
        except Exception as e:
            # Ensure session is closed if initialization fails
            if self.session:
                await self.session.close()
                self.session = None
            raise e

    async def _check_server_health(self) -> bool:
        """Check if external Crawl4AI service is running and healthy."""
        try:
            self._ensure_session_available()
            async with self.session.get(f"{self.api_base_url}/health") as response:
                if response.status == 200:
                    self.logger.debug(f"Crawl4AI service health check passed at {self.api_base_url}")
                    return True
                else:
                    self.logger.debug(f"Crawl4AI service health check failed: HTTP {response.status}")
                    return False
        except Exception as e:
            self.logger.debug(f"Crawl4AI service health check failed: {e}")
            return False

    async def _wait_for_external_service(self):
        """Wait for external Crawl4AI service to be ready with retry logic."""
        for attempt in range(self.connection_retries):
            self.logger.info(f"Attempting to connect to Crawl4AI service at {self.api_base_url} (attempt {attempt + 1}/{self.connection_retries})")
            
            start_time = time.time()
            while time.time() - start_time < self.server_startup_timeout:
                if await self._check_server_health():
                    self.logger.info("Successfully connected to external Crawl4AI service")
                    return
                
                await asyncio.sleep(2)  # Check every 2 seconds
            
            if attempt < self.connection_retries - 1:
                self.logger.warning(f"Connection attempt {attempt + 1} failed, retrying in 5 seconds...")
                await asyncio.sleep(5)
        
        raise create_service_unavailable_error(
            "Crawl4AI service",
            self.api_base_url
        )

    @validate_required_params(
        urls=lambda x: validate_url_list(x, max_count=100),
        crawler_config=lambda x: validate_config(x),
        browser_config=lambda x: validate_config(x)
    )
    @handle_async_errors("job submission")
    async def submit_crawl_job(
        self,
        urls: List[str],
        crawler_config: Optional[Dict[str, Any]] = None,
        browser_config: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Submit an asynchronous crawl job to Crawl4AI.
        
        Args:
            urls: List of URLs to crawl (max 100)
            crawler_config: Optional crawler configuration
            browser_config: Optional browser configuration
            
        Returns:
            Job ID for tracking the submitted job
            
        Raises:
            ValidationError: If inputs are invalid
            JobError: If job submission fails
            NetworkError: If HTTP request fails
        """
        self._ensure_session_available()
        
        payload = {
            "urls": urls
        }
        
        if crawler_config:
            payload["crawler_config"] = crawler_config
            
        if browser_config:
            payload["browser_config"] = browser_config

        async with self.session.post(f"{self.api_base_url}/crawl/job", json=payload) as response:
            if response.status != 200:
                error_text = await response.text()
                handle_http_response(
                    response.status,
                    error_text,
                    f"{self.api_base_url}/crawl/job",
                    "job submission"
                )
            
            try:
                result = await response.json()
                job_id = result.get("task_id")
                
                if not job_id:
                    raise create_job_error(
                        "unknown",
                        "No task_id in response",
                        "submission_failed"
                    )
                
                # Track the job
                self.active_jobs[job_id] = JobResult(
                    job_id=job_id,
                    status=JobStatus.PENDING,
                    created_at=result.get("created_at")
                )
                
                self.logger.info(f"Submitted crawl job: {job_id}")
                return job_id
                
            except (KeyError, ValueError) as e:
                raise create_job_error(
                    "unknown",
                    f"Invalid response format: {e}",
                    "submission_failed"
                )

    @validate_required_params(
        job_id=lambda x: validate_job_id(x)
    )
    @handle_async_errors("job status check")
    async def get_job_status(self, job_id: str) -> JobResult:
        """
        Get the current status of a crawl job.
        
        Args:
            job_id: Valid UUID format job ID
            
        Returns:
            JobResult with current status and information
            
        Raises:
            ValidationError: If job_id is invalid
            JobError: If job not found or status check fails
            NetworkError: If HTTP request fails
        """
        self._ensure_session_available()
        
        async with self.session.get(f"{self.api_base_url}/crawl/job/{job_id}") as response:
            if response.status == 404:
                raise create_job_error(
                    job_id,
                    "Job not found",
                    "not_found"
                )
            
            if response.status != 200:
                error_text = await response.text()
                handle_http_response(
                    response.status,
                    error_text,
                    f"{self.api_base_url}/crawl/job/{job_id}",
                    "job status check"
                )
            
            try:
                data = await response.json()
                
                # Parse status - handle invalid status gracefully
                status_str = data.get("status", "PENDING")
                try:
                    status = JobStatus(status_str)
                except ValueError:
                    self.logger.warning(f"Unknown job status '{status_str}', defaulting to PENDING")
                    status = JobStatus.PENDING
                
                # Enhanced job result with URL-level tracking
                job_result = JobResult(
                    job_id=job_id,
                    status=status,
                    progress=data.get("progress", 0.0),
                    message=data.get("message", ""),
                    result=data.get("result"),
                    error=data.get("error"),
                    created_at=data.get("created_at"),
                    updated_at=data.get("updated_at"),
                    # Extract URL-level metrics if available
                    total_urls=data.get("total_urls", 0),
                    successful_urls=data.get("successful_urls", 0),
                    failed_urls=data.get("failed_urls", 0),
                    url_results=data.get("url_results", {})
                )
                
                # Update our tracking
                self.active_jobs[job_id] = job_result
                
                return job_result
                
            except ValueError as e:
                raise create_job_error(
                    job_id,
                    f"Invalid JSON response: {e}",
                    "invalid_response"
                )

    @validate_required_params(
        job_id=lambda x: validate_job_id(x),
        timeout=lambda x: validate_timeout(x, min_timeout=10.0, max_timeout=3600.0) if x is not None else None
    )
    @handle_async_errors("job completion wait")
    async def wait_for_job_completion(
        self, 
        job_id: str,
        timeout: Optional[float] = None
    ) -> JobResult:
        """
        Wait for a job to complete, polling its status.
        
        Args:
            job_id: Valid UUID format job ID
            timeout: Optional timeout in seconds (10-3600)
            
        Returns:
            Final JobResult when complete
            
        Raises:
            ValidationError: If parameters are invalid
            TimeoutError: If job doesn't complete within timeout
            JobError: If job fails during execution
        """
        start_time = time.time()
        attempts = 0
        
        while attempts < self.max_poll_attempts:
            # Check timeout
            if timeout and (time.time() - start_time) > timeout:
                raise create_timeout_error(
                    f"job {job_id} completion wait",
                    timeout
                )
            
            # Get current status
            job_result = await self.get_job_status(job_id)
            
            # Check if completed
            if job_result.status in [JobStatus.COMPLETED, JobStatus.FAILED, JobStatus.CANCELLED]:
                self.logger.info(f"Job {job_id} finished with status: {job_result.status}")
                
                # For failed jobs, raise an appropriate error
                if job_result.status == JobStatus.FAILED:
                    error_msg = job_result.error or "Job failed with no error message"
                    raise create_job_error(
                        job_id,
                        f"Job failed: {error_msg}",
                        job_result.status.value
                    )
                
                return job_result
            
            # Wait before next poll
            await asyncio.sleep(self.poll_interval)
            attempts += 1
        
        raise create_timeout_error(
            f"job {job_id} completion wait (max attempts)",
            self.max_poll_attempts * self.poll_interval
        )

    @validate_required_params(
        job_id=lambda x: validate_job_id(x)
    )
    async def cancel_job(self, job_id: str) -> bool:
        """
        Cancel a running job.
        
        Args:
            job_id: Valid UUID format job ID
            
        Returns:
            True if cancellation was successful
            
        Raises:
            ValidationError: If job_id is invalid
        """
        try:
            self._ensure_session_available()
            async with self.session.delete(f"{self.api_base_url}/crawl/job/{job_id}") as response:
                if response.status == 200:
                    self.logger.info(f"Job {job_id} cancelled successfully")
                    
                    # Update local tracking
                    if job_id in self.active_jobs:
                        self.active_jobs[job_id].status = JobStatus.CANCELLED
                    
                    return True
                else:
                    error_text = await response.text()
                    self.logger.warning(f"Failed to cancel job {job_id}: {response.status} - {error_text}")
                    return False
                    
        except Exception as e:
            # Log the error but don't raise - cancellation is often not critical
            error_handler.log_error(e, context={"job_id": job_id, "operation": "job_cancellation"})
            return False

    async def list_active_jobs(self) -> List[JobResult]:
        """
        List all tracked active jobs.
        
        Returns:
            List of JobResult objects for all tracked jobs
        """
        return list(self.active_jobs.values())

    async def cleanup_completed_jobs(self):
        """Remove completed jobs from tracking."""
        completed_jobs = [
            job_id for job_id, job in self.active_jobs.items()
            if job.status in [JobStatus.COMPLETED, JobStatus.FAILED, JobStatus.CANCELLED]
        ]
        
        for job_id in completed_jobs:
            del self.active_jobs[job_id]
        
        if completed_jobs:
            self.logger.info(f"Cleaned up {len(completed_jobs)} completed jobs")

    async def get_job_reliability_metrics(self, job_id: str) -> Dict[str, Any]:
        """
        Get reliability metrics for a specific job.
        
        Args:
            job_id: The job ID to get metrics for
            
        Returns:
            Dictionary containing job-specific reliability metrics
        """
        if job_id not in self.active_jobs:
            return {"error": "Job not found in tracking"}
            
        job_result = self.active_jobs[job_id]
        
        return {
            "job_id": job_id,
            "status": job_result.status.value,
            "url_tracking": {
                "total_urls": job_result.total_urls,
                "successful_urls": job_result.successful_urls,
                "failed_urls": job_result.failed_urls,
                "success_rate": job_result.success_rate,
                "failure_rate": 1.0 - job_result.success_rate if job_result.total_urls > 0 else 0.0
            },
            "performance": {
                "created_at": job_result.created_at,
                "updated_at": job_result.updated_at,
                "progress": job_result.progress
            },
            "reliability_indicators": {
                "is_partial_success": job_result.is_partial_success,
                "has_errors": bool(job_result.error),
                "summary": job_result.job_summary
            }
        }
    
    async def get_aggregate_reliability_metrics(self) -> Dict[str, Any]:
        """
        Get aggregate reliability metrics across all tracked jobs.
        
        Returns:
            Dictionary containing aggregate job reliability metrics
        """
        if not self.active_jobs:
            return {"message": "No jobs tracked", "metrics": {}}
        
        total_jobs = len(self.active_jobs)
        completed_jobs = sum(1 for job in self.active_jobs.values() 
                           if job.status == JobStatus.COMPLETED)
        failed_jobs = sum(1 for job in self.active_jobs.values() 
                        if job.status == JobStatus.FAILED)
        running_jobs = sum(1 for job in self.active_jobs.values() 
                         if job.status == JobStatus.RUNNING)
        
        # URL-level aggregates
        total_urls = sum(job.total_urls for job in self.active_jobs.values())
        successful_urls = sum(job.successful_urls for job in self.active_jobs.values())
        failed_urls = sum(job.failed_urls for job in self.active_jobs.values())
        
        return {
            "job_level_metrics": {
                "total_jobs": total_jobs,
                "completed_jobs": completed_jobs,
                "failed_jobs": failed_jobs,
                "running_jobs": running_jobs,
                "job_success_rate": completed_jobs / total_jobs if total_jobs > 0 else 0.0
            },
            "url_level_metrics": {
                "total_urls": total_urls,
                "successful_urls": successful_urls,
                "failed_urls": failed_urls,
                "url_success_rate": successful_urls / total_urls if total_urls > 0 else 0.0
            },
            "operational_status": {
                "active_jobs_count": running_jobs,
                "has_failures": failed_jobs > 0 or failed_urls > 0,
                "system_healthy": failed_jobs / total_jobs < 0.1 if total_jobs > 0 else True
            }
        }

    async def cleanup(self):
        """Clean up resources."""
        self.logger.info("Cleaning up job manager resources...")
        
        # Close HTTP session with proper error handling
        if self.session and not self.session.closed:
            try:
                # Wait a bit for any pending requests to complete
                await asyncio.sleep(0.1)
                await self.session.close()
                self.logger.debug("HTTP session closed successfully")
            except Exception as e:
                self.logger.warning(f"Error closing HTTP session: {e}")
            finally:
                self.session = None
        elif self.session:
            # Session already closed, just clear reference
            self.session = None
        
        # Clear active jobs tracking
        self.active_jobs.clear()
        
        self.logger.info("Job manager cleanup complete")
    
    def _ensure_session_available(self):
        """Ensure session is available for use."""
        if not self.session or self.session.closed:
            raise RuntimeError("Job manager session is not available. Call start() first.")
    
    def __del__(self):
        """Ensure session is closed on object destruction."""
        if hasattr(self, 'session') and self.session and not self.session.closed:
            try:
                # Try to close the session synchronously if possible
                import asyncio
                try:
                    loop = asyncio.get_event_loop()
                    if loop.is_running():
                        # If event loop is running, schedule cleanup
                        loop.create_task(self.session.close())
                    else:
                        # If no event loop, close synchronously
                        loop.run_until_complete(self.session.close())
                except RuntimeError:
                    # If no event loop available, create one
                    asyncio.run(self.session.close())
            except Exception:
                # Silently ignore cleanup errors in destructor
                pass



# Configuration helper
def get_job_manager_config() -> Dict[str, Any]:
    """Get job manager configuration from environment variables."""
    return {
        "api_host": os.getenv("CRAWL4AI_HOST", "localhost"),
        "api_port": int(os.getenv("CRAWL4AI_PORT", "11235")),
        "server_startup_timeout": int(os.getenv("CRAWL4AI_STARTUP_TIMEOUT", "60")),
        "max_poll_attempts": int(os.getenv("CRAWL4AI_MAX_POLL_ATTEMPTS", "600")),
        "poll_interval": float(os.getenv("CRAWL4AI_POLL_INTERVAL", "1.0")),
        "connection_retries": int(os.getenv("CRAWL4AI_CONNECTION_RETRIES", "3")),
        "connection_timeout": int(os.getenv("CRAWL4AI_CONNECTION_TIMEOUT", "30"))
    }
