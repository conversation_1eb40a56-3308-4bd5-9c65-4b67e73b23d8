"""
Reliability and resilience service for MCP crawling operations.

This module provides comprehensive reliability features including:
- Robust retry mechanisms with exponential backoff
- Configurable timeout management
- Health checks and connection validation
- Circuit breaker pattern for fault tolerance
- Comprehensive error handling and reporting
- Caching mechanisms for crawled content
- Progressive crawling strategies
- Monitoring and metrics collection
"""

import asyncio
import hashlib
import random
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple, Union
from dataclasses import dataclass, field
from enum import Enum
from urllib.parse import urlparse
import aiohttp
import json
import os
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CrawlErrorType(Enum):
    """Types of crawling errors for categorization and handling."""
    NETWORK_ERROR = "network_error"
    TIMEOUT_ERROR = "timeout_error"
    RATE_LIMIT_ERROR = "rate_limit_error"
    CONTENT_ERROR = "content_error"
    AUTHENTICATION_ERROR = "authentication_error"
    SERVER_ERROR = "server_error"
    CLIENT_ERROR = "client_error"
    UNKNOWN_ERROR = "unknown_error"


@dataclass
class CrawlResult:
    """Result of a crawl operation with comprehensive metadata."""
    url: str
    success: bool
    content: Optional[str] = None
    error: Optional[str] = None
    error_type: Optional[CrawlErrorType] = None
    retry_count: int = 0
    response_time_ms: int = 0
    timestamp: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class RetryConfig:
    """Configuration for retry mechanisms."""
    max_retries: int = 3
    base_delay: float = 1.0
    max_delay: float = 60.0
    exponential_base: float = 2.0
    jitter: bool = True
    retry_on_errors: List[CrawlErrorType] = field(default_factory=lambda: [
        CrawlErrorType.NETWORK_ERROR,
        CrawlErrorType.TIMEOUT_ERROR,
        CrawlErrorType.SERVER_ERROR
    ])


@dataclass
class TimeoutConfig:
    """Configuration for timeout management."""
    connection_timeout: float = 30.0
    read_timeout: float = 60.0
    total_timeout: float = 300.0
    dns_timeout: float = 10.0


@dataclass
class CircuitBreakerConfig:
    """Configuration for circuit breaker pattern."""
    failure_threshold: int = 5
    recovery_timeout: float = 60.0
    success_threshold: int = 3
    enabled: bool = True


@dataclass
class CacheConfig:
    """Configuration for caching mechanisms."""
    enabled: bool = True
    max_size: int = 1000
    ttl_seconds: int = 3600
    cache_dir: Optional[str] = None


class CircuitBreakerState(Enum):
    """States of the circuit breaker."""
    CLOSED = "closed"
    OPEN = "open"
    HALF_OPEN = "half_open"


class CircuitBreaker:
    """Circuit breaker implementation for fault tolerance."""
    
    def __init__(self, config: CircuitBreakerConfig):
        self.config = config
        self.state = CircuitBreakerState.CLOSED
        self.failure_count = 0
        self.success_count = 0
        self.last_failure_time = None
        self.lock = asyncio.Lock()
    
    async def call(self, func, *args, **kwargs):
        """Execute a function with circuit breaker protection."""
        if not self.config.enabled:
            return await func(*args, **kwargs)
        
        async with self.lock:
            if self.state == CircuitBreakerState.OPEN:
                if self._should_attempt_reset():
                    self.state = CircuitBreakerState.HALF_OPEN
                    self.success_count = 0
                else:
                    raise Exception("Circuit breaker is OPEN")
            
            try:
                result = await func(*args, **kwargs)
                await self._on_success()
                return result
            except Exception as e:
                await self._on_failure()
                raise e
    
    def _should_attempt_reset(self) -> bool:
        """Check if circuit breaker should attempt to reset."""
        if self.last_failure_time is None:
            return True
        
        time_since_failure = time.time() - self.last_failure_time
        return time_since_failure >= self.config.recovery_timeout
    
    async def _on_success(self):
        """Handle successful operation."""
        if self.state == CircuitBreakerState.HALF_OPEN:
            self.success_count += 1
            if self.success_count >= self.config.success_threshold:
                self.state = CircuitBreakerState.CLOSED
                self.failure_count = 0
        else:
            self.failure_count = 0
    
    async def _on_failure(self):
        """Handle failed operation."""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.config.failure_threshold:
            self.state = CircuitBreakerState.OPEN
            logger.warning(f"Circuit breaker opened after {self.failure_count} failures")


class CrawlCache:
    """Caching mechanism for crawled content."""
    
    def __init__(self, config: CacheConfig):
        self.config = config
        self.cache: Dict[str, Tuple[str, datetime]] = {}
        self.lock = asyncio.Lock()
    
    def _get_cache_key(self, url: str) -> str:
        """Generate cache key for URL."""
        return hashlib.md5(url.encode()).hexdigest()
    
    async def get(self, url: str) -> Optional[str]:
        """Get cached content for URL."""
        if not self.config.enabled:
            return None
        
        cache_key = self._get_cache_key(url)
        
        async with self.lock:
            if cache_key in self.cache:
                content, timestamp = self.cache[cache_key]
                if datetime.now() - timestamp < timedelta(seconds=self.config.ttl_seconds):
                    logger.info(f"Cache hit for {url}")
                    return content
                else:
                    # Remove expired entry
                    del self.cache[cache_key]
        
        return None
    
    async def set(self, url: str, content: str):
        """Store content in cache."""
        if not self.config.enabled:
            return
        
        cache_key = self._get_cache_key(url)
        
        async with self.lock:
            # Implement LRU eviction if cache is full
            if len(self.cache) >= self.config.max_size:
                # Remove oldest entry
                oldest_key = min(self.cache.keys(), key=lambda k: self.cache[k][1])
                del self.cache[oldest_key]
            
            self.cache[cache_key] = (content, datetime.now())
            logger.info(f"Cached content for {url}")


class HealthChecker:
    """Health check and connection validation service."""
    
    def __init__(self, timeout_config: TimeoutConfig):
        self.timeout_config = timeout_config
    
    async def check_url_health(self, url: str) -> Tuple[bool, Optional[str]]:
        """Check if a URL is accessible and healthy."""
        try:
            timeout = aiohttp.ClientTimeout(
                total=self.timeout_config.connection_timeout,
                connect=self.timeout_config.connection_timeout
            )
            
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.head(url) as response:
                    if response.status < 400:
                        return True, None
                    else:
                        return False, f"HTTP {response.status}"
        except asyncio.TimeoutError:
            return False, "Connection timeout"
        except Exception as e:
            return False, str(e)
    
    async def check_domain_health(self, domain: str) -> Tuple[bool, Optional[str]]:
        """Check if a domain is accessible."""
        urls_to_check = [
            f"https://{domain}",
            f"http://{domain}",
            f"https://{domain}/robots.txt",
            f"http://{domain}/robots.txt"
        ]
        
        for url in urls_to_check:
            is_healthy, error = await self.check_url_health(url)
            if is_healthy:
                return True, None
        
        return False, "Domain unreachable"


class MetricsCollector:
    """Metrics collection and monitoring service."""
    
    def __init__(self):
        self.metrics: Dict[str, Any] = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "retry_counts": {},
            "error_types": {},
            "response_times": [],
            "start_time": datetime.now()
        }
        self.lock = asyncio.Lock()
    
    async def record_request(self, result: CrawlResult):
        """Record metrics for a crawl request."""
        async with self.lock:
            self.metrics["total_requests"] += 1
            
            if result.success:
                self.metrics["successful_requests"] += 1
            else:
                self.metrics["failed_requests"] += 1
                
                # Record error type
                error_type = result.error_type.value if result.error_type else "unknown"
                self.metrics["error_types"][error_type] = self.metrics["error_types"].get(error_type, 0) + 1
            
            # Record retry count
            retry_key = str(result.retry_count)
            self.metrics["retry_counts"][retry_key] = self.metrics["retry_counts"].get(retry_key, 0) + 1
            
            # Record response time
            if result.response_time_ms > 0:
                self.metrics["response_times"].append(result.response_time_ms)
                # Keep only last 1000 response times
                if len(self.metrics["response_times"]) > 1000:
                    self.metrics["response_times"] = self.metrics["response_times"][-1000:]
    
    async def get_metrics(self) -> Dict[str, Any]:
        """Get current metrics."""
        async with self.lock:
            metrics = self.metrics.copy()
            
            # Calculate derived metrics
            if metrics["response_times"]:
                response_times = metrics["response_times"]
                metrics["avg_response_time"] = sum(response_times) / len(response_times)
                metrics["max_response_time"] = max(response_times)
                metrics["min_response_time"] = min(response_times)
            
            if metrics["total_requests"] > 0:
                metrics["success_rate"] = metrics["successful_requests"] / metrics["total_requests"]
                metrics["failure_rate"] = metrics["failed_requests"] / metrics["total_requests"]
            
            metrics["uptime_seconds"] = (datetime.now() - metrics["start_time"]).total_seconds()
            
            return metrics


class ReliableCrawler:
    """Main reliable crawling service with comprehensive error handling."""
    
    def __init__(
        self,
        retry_config: Optional[RetryConfig] = None,
        timeout_config: Optional[TimeoutConfig] = None,
        circuit_breaker_config: Optional[CircuitBreakerConfig] = None,
        cache_config: Optional[CacheConfig] = None
    ):
        self.retry_config = retry_config or RetryConfig()
        self.timeout_config = timeout_config or TimeoutConfig()
        self.circuit_breaker_config = circuit_breaker_config or CircuitBreakerConfig()
        self.cache_config = cache_config or CacheConfig()
        
        self.circuit_breaker = CircuitBreaker(self.circuit_breaker_config)
        self.cache = CrawlCache(self.cache_config)
        self.health_checker = HealthChecker(self.timeout_config)
        self.metrics = MetricsCollector()
    
    def _classify_error(self, error: Exception) -> CrawlErrorType:
        """Classify error type for appropriate handling."""
        error_str = str(error).lower()
        
        if isinstance(error, asyncio.TimeoutError):
            return CrawlErrorType.TIMEOUT_ERROR
        elif isinstance(error, aiohttp.ClientConnectorError):
            return CrawlErrorType.NETWORK_ERROR
        elif isinstance(error, aiohttp.ClientResponseError):
            if error.status == 429:
                return CrawlErrorType.RATE_LIMIT_ERROR
            elif error.status in [401, 403]:
                return CrawlErrorType.AUTHENTICATION_ERROR
            elif 400 <= error.status < 500:
                return CrawlErrorType.CLIENT_ERROR
            elif error.status >= 500:
                return CrawlErrorType.SERVER_ERROR
        elif "timeout" in error_str:
            return CrawlErrorType.TIMEOUT_ERROR
        elif "connection" in error_str:
            return CrawlErrorType.NETWORK_ERROR
        elif "rate limit" in error_str:
            return CrawlErrorType.RATE_LIMIT_ERROR
        
        return CrawlErrorType.UNKNOWN_ERROR
    
    def _should_retry(self, error_type: CrawlErrorType, retry_count: int) -> bool:
        """Determine if an error should trigger a retry."""
        if retry_count >= self.retry_config.max_retries:
            return False
        
        return error_type in self.retry_config.retry_on_errors
    
    async def _calculate_retry_delay(self, retry_count: int) -> float:
        """Calculate delay before retry with exponential backoff and jitter."""
        base_delay = self.retry_config.base_delay
        exponential_delay = base_delay * (self.retry_config.exponential_base ** retry_count)
        delay = min(exponential_delay, self.retry_config.max_delay)
        
        if self.retry_config.jitter:
            # Add random jitter (±25% of delay)
            jitter = delay * 0.25 * (2 * random.random() - 1)
            delay = max(0, delay + jitter)
        
        return delay
    
    async def _perform_health_check(self, url: str) -> Tuple[bool, Optional[str]]:
        """Perform pre-crawl health check."""
        try:
            parsed_url = urlparse(url)
            domain = parsed_url.netloc
            
            if not domain:
                return False, "Invalid URL format"
            
            is_healthy, error = await self.health_checker.check_domain_health(domain)
            return is_healthy, error
        except Exception as e:
            return False, str(e)
    
    async def _check_cache(self, url: str, start_time: float, retry_count: int) -> Optional[CrawlResult]:
        """Check cache for existing content and return result if found."""
        cached_content = await self.cache.get(url)
        if cached_content:
            result = CrawlResult(
                url=url,
                success=True,
                content=cached_content,
                retry_count=retry_count,
                response_time_ms=int((time.time() - start_time) * 1000),
                metadata={"from_cache": True}
            )
            await self.metrics.record_request(result)
            return result
        return None
    
    async def _perform_crawl_attempt(self, url: str, crawl_func, *args, **kwargs) -> str:
        """Perform a single crawl attempt through circuit breaker."""
        return await self.circuit_breaker.call(crawl_func, url, *args, **kwargs)
    
    async def _handle_crawl_success(self, url: str, content: str, start_time: float, retry_count: int) -> CrawlResult:
        """Handle successful crawl by caching result and creating CrawlResult."""
        # Cache successful result
        if content:
            await self.cache.set(url, content)
        
        result = CrawlResult(
            url=url,
            success=True,
            content=content,
            retry_count=retry_count,
            response_time_ms=int((time.time() - start_time) * 1000)
        )
        await self.metrics.record_request(result)
        return result
    
    async def _handle_crawl_failure(self, url: str, error: Exception, retry_count: int) -> CrawlErrorType:
        """Handle crawl failure by logging and classifying error."""
        error_type = self._classify_error(error)
        logger.warning(f"Crawl attempt {retry_count + 1} failed for {url}: {str(error)}")
        return error_type
    
    async def _create_failure_result(self, url: str, error: Exception, error_type: CrawlErrorType, start_time: float) -> CrawlResult:
        """Create final failure result after all retries exhausted."""
        result = CrawlResult(
            url=url,
            success=False,
            error=str(error),
            error_type=error_type,
            retry_count=self.retry_config.max_retries,
            response_time_ms=int((time.time() - start_time) * 1000)
        )
        await self.metrics.record_request(result)
        return result
    
    async def _crawl_with_retries(self, url: str, crawl_func, *args, **kwargs) -> CrawlResult:
        """Perform crawl operation with retry logic."""
        start_time = time.time()
        last_error = None
        last_error_type = None
        
        for retry_count in range(self.retry_config.max_retries + 1):
            try:
                # Check cache first
                cached_result = await self._check_cache(url, start_time, retry_count)
                if cached_result:
                    return cached_result
                
                # Perform actual crawl
                content = await self._perform_crawl_attempt(url, crawl_func, *args, **kwargs)
                
                # Handle success
                return await self._handle_crawl_success(url, content, start_time, retry_count)
                
            except Exception as e:
                last_error = e
                last_error_type = await self._handle_crawl_failure(url, e, retry_count)
                
                if not self._should_retry(last_error_type, retry_count):
                    break
                
                if retry_count < self.retry_config.max_retries:
                    delay = await self._calculate_retry_delay(retry_count)
                    logger.info(f"Retrying {url} in {delay:.2f} seconds...")
                    await asyncio.sleep(delay)
        
        # All retries failed
        return await self._create_failure_result(url, last_error, last_error_type, start_time)
    
    async def crawl_url(self, url: str, crawl_func, *args, **kwargs) -> CrawlResult:
        """Main crawl method with full reliability features."""
        logger.info(f"Starting crawl for {url}")
        
        # Perform health check
        if self.health_checker:
            is_healthy, health_error = await self._perform_health_check(url)
            if not is_healthy:
                logger.warning(f"Health check failed for {url}: {health_error}")
                result = CrawlResult(
                    url=url,
                    success=False,
                    error=f"Health check failed: {health_error}",
                    error_type=CrawlErrorType.NETWORK_ERROR
                )
                await self.metrics.record_request(result)
                return result
        
        # Perform crawl with retries
        result = await self._crawl_with_retries(url, crawl_func, *args, **kwargs)
        
        if result.success:
            logger.info(f"Successfully crawled {url} (retries: {result.retry_count})")
        else:
            logger.error(f"Failed to crawl {url} after {result.retry_count} retries: {result.error}")
        
        return result
    
    async def crawl_batch(self, urls: List[str], crawl_func, max_concurrent: int = 10, *args, **kwargs) -> List[CrawlResult]:
        """Crawl multiple URLs concurrently with reliability features."""
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def crawl_with_semaphore(url: str):
            async with semaphore:
                return await self.crawl_url(url, crawl_func, *args, **kwargs)
        
        logger.info(f"Starting batch crawl for {len(urls)} URLs")
        
        tasks = [crawl_with_semaphore(url) for url in urls]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle any exceptions from gather
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                error_result = CrawlResult(
                    url=urls[i],
                    success=False,
                    error=str(result),
                    error_type=self._classify_error(result)
                )
                await self.metrics.record_request(error_result)
                processed_results.append(error_result)
            else:
                processed_results.append(result)
        
        successful_count = sum(1 for r in processed_results if r.success)
        logger.info(f"Batch crawl completed: {successful_count}/{len(urls)} successful")
        
        return processed_results
    
    async def get_metrics(self) -> Dict[str, Any]:
        """Get current reliability metrics."""
        return await self.metrics.get_metrics()
    
    async def reset_circuit_breaker(self):
        """Manually reset circuit breaker."""
        async with self.circuit_breaker.lock:
            self.circuit_breaker.state = CircuitBreakerState.CLOSED
            self.circuit_breaker.failure_count = 0
            self.circuit_breaker.success_count = 0
            logger.info("Circuit breaker manually reset")
    
    async def clear_cache(self):
        """Clear all cached content."""
        async with self.cache.lock:
            self.cache.cache.clear()
            logger.info("Cache cleared")


def create_default_reliable_crawler() -> ReliableCrawler:
    """Create a ReliableCrawler with default configuration."""
    return ReliableCrawler(
        retry_config=RetryConfig(
            max_retries=3,
            base_delay=1.0,
            max_delay=60.0,
            exponential_base=2.0,
            jitter=True
        ),
        timeout_config=TimeoutConfig(
            connection_timeout=30.0,
            read_timeout=60.0,
            total_timeout=300.0
        ),
        circuit_breaker_config=CircuitBreakerConfig(
            failure_threshold=5,
            recovery_timeout=60.0,
            success_threshold=3,
            enabled=True
        ),
        cache_config=CacheConfig(
            enabled=True,
            max_size=1000,
            ttl_seconds=3600
        )
    )


def create_production_reliable_crawler() -> ReliableCrawler:
    """Create a ReliableCrawler optimized for production use."""
    return ReliableCrawler(
        retry_config=RetryConfig(
            max_retries=5,
            base_delay=2.0,
            max_delay=120.0,
            exponential_base=1.5,
            jitter=True
        ),
        timeout_config=TimeoutConfig(
            connection_timeout=45.0,
            read_timeout=120.0,
            total_timeout=600.0
        ),
        circuit_breaker_config=CircuitBreakerConfig(
            failure_threshold=10,
            recovery_timeout=120.0,
            success_threshold=5,
            enabled=True
        ),
        cache_config=CacheConfig(
            enabled=True,
            max_size=5000,
            ttl_seconds=7200
        )
    )