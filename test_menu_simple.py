#!/usr/bin/env python3
"""
Simple test for the menu system
"""

import sys
import asyncio
from pathlib import Path

# Add src directory to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from prompt_toolkit import Application
from prompt_toolkit.key_binding import KeyBindings
from prompt_toolkit.layout.containers import HSplit, Window
from prompt_toolkit.layout.controls import FormattedTextControl
from prompt_toolkit.layout.layout import Layout
from prompt_toolkit.formatted_text import FormattedText
from prompt_toolkit.keys import Keys
from prompt_toolkit.styles import Style

class SimpleMenu:
    def __init__(self):
        self.selected_index = 0
        self.menu_items = [
            "🔍 /search - search the knowledge base",
            "💻 /code - search for code examples",
            "🌐 /crawl - crawl URLs",
            "📂 /sources - list available sources",
            "🚪 /exit - exit the application"
        ]
        
        # Setup key bindings
        self.kb = KeyBindings()
        self._setup_key_bindings()
        
    def _setup_key_bindings(self):
        @self.kb.add(Keys.Up)
        def _(event):
            self.selected_index = max(0, self.selected_index - 1)
            self._update_display()
        
        @self.kb.add(Keys.Down)
        def _(event):
            self.selected_index = min(len(self.menu_items) - 1, self.selected_index + 1)
            self._update_display()
        
        @self.kb.add(Keys.Enter)
        def _(event):
            selected_item = self.menu_items[self.selected_index]
            if "/exit" in selected_item:
                event.app.exit()
            else:
                # For now, just show selection
                print(f"\nSelected: {selected_item}")
        
        @self.kb.add(Keys.ControlC)
        def _(event):
            event.app.exit()
    
    def _create_menu_text(self) -> FormattedText:
        text_parts = [
            ('class:title', 'AHA RAG v0.1'),
            ('', '\n\n')
        ]
        
        for i, item in enumerate(self.menu_items):
            if i == self.selected_index:
                text_parts.extend([
                    ('class:selected', f'> {item}'),
                    ('', '\n')
                ])
            else:
                text_parts.extend([
                    ('class:normal', f'  {item}'),
                    ('', '\n')
                ])
        
        text_parts.extend([
            ('', '\n'),
            ('class:help', 'Use arrow keys to navigate, Enter to select, Ctrl+C to exit')
        ])
        
        return FormattedText(text_parts)
    
    def _create_layout(self) -> Layout:
        return Layout(
            HSplit([
                Window(height=2),  # Top spacing
                Window(
                    FormattedTextControl(self._create_menu_text()),
                    wrap_lines=True
                ),
                Window(height=2),  # Bottom spacing
            ])
        )
    
    def _update_display(self):
        if hasattr(self, 'app') and self.app:
            self.app.layout = self._create_layout()
            self.app.invalidate()
    
    def _create_style(self):
        return Style.from_dict({
            'title': '#00D4FF bold',
            'selected': '#00D4FF bold',
            'normal': '#E8E8E8',
            'help': '#888888',
        })
    
    async def run(self):
        self.app = Application(
            layout=self._create_layout(),
            key_bindings=self.kb,
            style=self._create_style(),
            full_screen=True
        )
        
        try:
            await self.app.run_async()
        except Exception as e:
            print(f"Error: {e}")

async def main():
    menu = SimpleMenu()
    await menu.run()

if __name__ == "__main__":
    print("Testing simple menu navigation...")
    print("Use arrow keys to navigate, Enter to select, Ctrl+C to exit")
    asyncio.run(main())