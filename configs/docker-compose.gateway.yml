version: '3.8'

services:
  # Supergateway service for <PERSON> remote connections
  supergateway:
    image: supercorpai/supergateway:latest
    container_name: crawl4ai-supergateway
    command: ["--sse-to-stdio", "http://YOUR_SERVER_IP:8051/sse"]
    stdin_open: true
    tty: true
    networks:
      - mcp-network
    restart: unless-stopped
    environment:
      - DEBUG=1
    healthcheck:
      test: ["CMD", "echo", "health check"]
      interval: 30s
      timeout: 5s
      retries: 3

networks:
  mcp-network:
    external: true