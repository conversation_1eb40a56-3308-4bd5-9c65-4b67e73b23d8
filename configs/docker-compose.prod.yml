version: '3.8'

services:
  crawl4ai-rag-mcp:
    build:
      context: ..
      dockerfile: Dockerfile
      args:
        PORT: 8051
    container_name: crawl4ai-rag-mcp-prod
    ports:
      - "8051:8051"
    environment:
      - TRANSPORT=sse
      - HOST=0.0.0.0
      - PORT=8051
    env_file:
      - ../.env
    volumes:
      - crawl4ai_cache:/app/.crawl4ai_cache
      - pip_cache:/root/.cache/pip
    networks:
      - mcp-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8051/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.25'

  # Optional: Reverse proxy for HTTPS
  nginx:
    image: nginx:alpine
    container_name: crawl4ai-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.prod.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      crawl4ai-rag-mcp:
        condition: service_healthy
    networks:
      - mcp-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 5s
      retries: 3

volumes:
  crawl4ai_cache:
    driver: local
  pip_cache:
    driver: local

networks:
  mcp-network:
    driver: bridge