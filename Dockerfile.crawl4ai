FROM python:3.12-slim

# Set up working directory
WORKDIR /app

# Install system dependencies for crawl4ai and web browser
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    ca-certificates \
    chromium \
    chromium-driver \
    && rm -rf /var/lib/apt/lists/*

# Install uv package manager
RUN pip install uv

# Copy requirements and install crawl4ai dependencies
COPY pyproject.toml ./
RUN uv pip install --system crawl4ai[all] fastapi uvicorn

# Create required cache directories first
RUN mkdir -p /app/.crawl4ai_cache /app/logs

# Setup crawl4ai (before user creation to ensure proper permissions)
RUN crawl4ai-setup

# Create a simple HTTP server using FastAPI to wrap crawl4ai
COPY <<EOF /app/server.py
"""
Simple HTTP API server for Crawl4AI
Provides job management and crawling capabilities via REST API
"""
import asyncio
import json
import uuid
from typing import Dict, Any, List, Optional
from datetime import datetime
import uvicorn
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from crawl4ai import AsyncWeb<PERSON>rawler
from crawl4ai.async_crawler_strategy import AsyncCrawlerStrategy
from crawl4ai.async_configs import BrowserConfig, CrawlerRunConfig

# Pydantic models for API
class CrawlRequest(BaseModel):
    urls: List[str]
    crawler_config: Optional[Dict[str, Any]] = None
    browser_config: Optional[Dict[str, Any]] = None

class JobStatusResponse(BaseModel):
    task_id: str
    status: str
    progress: float = 0.0
    message: str = ""
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    created_at: str
    updated_at: str

# In-memory job storage (in production, use a proper database)
jobs: Dict[str, Dict[str, Any]] = {}

app = FastAPI(title="Crawl4AI API Server", version="1.0.0")

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "crawl4ai-server"}

@app.post("/crawl/job")
async def submit_crawl_job(request: CrawlRequest):
    """Submit a new crawl job"""
    task_id = str(uuid.uuid4())
    
    # Store job metadata
    jobs[task_id] = {
        "task_id": task_id,
        "status": "PENDING",
        "progress": 0.0,
        "message": "Job submitted",
        "result": None,
        "error": None,
        "created_at": datetime.now().isoformat(),
        "updated_at": datetime.now().isoformat(),
        "request": request.dict()
    }
    
    # Start background task
    asyncio.create_task(process_crawl_job(task_id, request))
    
    return {"task_id": task_id, "status": "PENDING", "created_at": jobs[task_id]["created_at"]}

@app.get("/crawl/job/{job_id}")
async def get_job_status(job_id: str) -> JobStatusResponse:
    """Get job status and results"""
    if job_id not in jobs:
        raise HTTPException(status_code=404, detail="Job not found")
    
    job = jobs[job_id]
    return JobStatusResponse(**job)

@app.delete("/crawl/job/{job_id}")
async def cancel_job(job_id: str):
    """Cancel a running job"""
    if job_id not in jobs:
        raise HTTPException(status_code=404, detail="Job not found")
    
    job = jobs[job_id]
    if job["status"] in ["COMPLETED", "FAILED", "CANCELLED"]:
        return {"message": f"Job {job_id} is already {job['status']}"}
    
    job["status"] = "CANCELLED"
    job["updated_at"] = datetime.now().isoformat()
    job["message"] = "Job cancelled by request"
    
    return {"message": f"Job {job_id} cancelled successfully"}

async def process_crawl_job(task_id: str, request: CrawlRequest):
    """Background task to process crawl job"""
    try:
        # Update job status
        jobs[task_id]["status"] = "RUNNING"
        jobs[task_id]["updated_at"] = datetime.now().isoformat()
        jobs[task_id]["message"] = "Crawling in progress"
        
        # Configure browser and crawler
        browser_config = BrowserConfig(**(request.browser_config or {}))
        crawler_config = CrawlerRunConfig(**(request.crawler_config or {}))
        
        results = []
        total_urls = len(request.urls)
        
        async with AsyncWebCrawler(config=browser_config) as crawler:
            for i, url in enumerate(request.urls):
                try:
                    # Update progress
                    progress = (i / total_urls) * 100
                    jobs[task_id]["progress"] = progress
                    jobs[task_id]["message"] = f"Crawling {url} ({i+1}/{total_urls})"
                    jobs[task_id]["updated_at"] = datetime.now().isoformat()
                    
                    # Perform crawl
                    result = await crawler.arun(url=url, config=crawler_config)
                    
                    # Store result
                    results.append({
                        "url": url,
                        "success": result.success,
                        "html": result.html if result.success else None,
                        "cleaned_html": result.cleaned_html if result.success else None,
                        "markdown": result.markdown if result.success else None,
                        "extracted_content": result.extracted_content if result.success else None,
                        "metadata": result.metadata if result.success else None,
                        "error": result.error_message if not result.success else None
                    })
                    
                except Exception as e:
                    results.append({
                        "url": url,
                        "success": False,
                        "error": str(e)
                    })
        
        # Mark job as completed
        jobs[task_id]["status"] = "COMPLETED"
        jobs[task_id]["progress"] = 100.0
        jobs[task_id]["message"] = f"Successfully crawled {len(results)} URLs"
        jobs[task_id]["result"] = {"crawl_results": results}
        jobs[task_id]["updated_at"] = datetime.now().isoformat()
        
    except Exception as e:
        # Mark job as failed
        jobs[task_id]["status"] = "FAILED"
        jobs[task_id]["error"] = str(e)
        jobs[task_id]["message"] = f"Job failed: {str(e)}"
        jobs[task_id]["updated_at"] = datetime.now().isoformat()

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=11235, workers=1)
EOF

# Health check for Crawl4AI API server
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:11235/health || exit 1

# Expose Crawl4AI API port
EXPOSE 11235

# Create user-accessible cache directories and copy root caches
RUN cp -r /root/.cache /app/.cache && \
    cp -r /root/.crawl4ai /app/.crawl4ai_db && \
    useradd -r -s /bin/false crawl4ai && \
    mkdir -p /home/<USER>
    chown -R crawl4ai:crawl4ai /app /home/<USER>

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PLAYWRIGHT_BROWSERS_PATH=/app/.cache/ms-playwright
ENV CRAWL4AI_CACHE_DIR=/app/.crawl4ai_cache

USER crawl4ai

# Command to run our custom HTTP API server
CMD ["python", "/app/server.py"]