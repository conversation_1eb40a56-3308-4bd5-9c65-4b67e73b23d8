# GPU Acceleration Guide

This guide explains how to use GPU acceleration for faster, cost-free embedding generation.

## Prerequisites

### 1. NVIDIA GPU Setup
- NVIDIA GPU with CUDA support (compute capability 3.5+)
- NVIDIA drivers installed
- CUDA toolkit installed (optional, Docker image includes runtime)

### 2. Docker GPU Support
```bash
# Install NVIDIA Container Toolkit
curl -fsSL https://nvidia.github.io/libnvidia-container/gpgkey | sudo gpg --dearmor -o /usr/share/keyrings/nvidia-container-toolkit-keyring.gpg
curl -s -L https://nvidia.github.io/libnvidia-container/stable/deb/nvidia-container-toolkit.list | \
  sed 's#deb https://#deb [signed-by=/usr/share/keyrings/nvidia-container-toolkit-keyring.gpg] https://#g' | \
  sudo tee /etc/apt/sources.list.d/nvidia-container-toolkit.list

sudo apt-get update && sudo apt-get install -y nvidia-container-toolkit
sudo systemctl restart docker
```

### 3. Verify GPU Access
```bash
nvidia-smi
docker run --rm --gpus all nvidia/cuda:12.1-runtime-ubuntu22.04 nvidia-smi
```

## Configuration

### Environment Variables
```bash
# Enable GPU acceleration (default: true)
USE_LOCAL_EMBEDDINGS=true

# Enable GPU device (default: true)
USE_GPU=true

# GPU batch size - adjust based on VRAM (default: 32)
GPU_BATCH_SIZE=32

# Local embedding model (default: all-MiniLM-L6-v2)
LOCAL_EMBEDDING_MODEL=all-MiniLM-L6-v2
```

### Available Models
| Model | Dimensions | Speed | Quality | VRAM Usage |
|-------|------------|-------|---------|------------|
| all-MiniLM-L6-v2 | 384 | Fast | Good | Low (~1GB) |
| all-mpnet-base-v2 | 768 | Medium | Better | Medium (~2GB) |
| all-MiniLM-L12-v2 | 384 | Medium | Better | Medium (~1.5GB) |

## Usage

### Method 1: Docker GPU Build
```bash
# Build GPU-enabled image
docker build -f Dockerfile.gpu -t mcp/crawl4ai-rag:gpu .

# Run with GPU support
docker run --gpus all --env-file .env -p 8051:8051 mcp/crawl4ai-rag:gpu
```

### Method 2: Docker Compose (Recommended)
```bash
# Start with GPU support
docker-compose -f docker-compose.gpu.yml up -d

# For older Docker versions
docker-compose -f docker-compose.gpu.yml --profile legacy up -d
```

### Method 3: Direct Python Execution
```bash
# Install dependencies
uv pip install -e .

# Set environment variables
export USE_LOCAL_EMBEDDINGS=true
export USE_GPU=true

# Run the server
python src/crawl4ai_mcp.py
```

## Performance Benefits

### Speed Comparison
- **OpenAI API**: ~2-5 seconds per batch (network dependent)
- **CPU Local**: ~1-3 seconds per batch
- **GPU Local**: ~0.1-0.5 seconds per batch

### Cost Savings
- **OpenAI API**: $0.00002 per 1K tokens (~$0.02 per 1M characters)
- **Local GPU**: Free after hardware cost

### Example Performance (RTX 4060 Ti)
```
Batch Size: 32 text chunks
- GPU: 0.2 seconds
- CPU: 2.1 seconds 
- OpenAI API: 3.5 seconds + network latency

Daily savings (10K embeddings): ~$2-5 in API costs
```

## Troubleshooting

### Common Issues

1. **GPU not detected**
   ```bash
   # Check NVIDIA drivers
   nvidia-smi
   
   # Check CUDA availability in Python
   python -c "import torch; print(torch.cuda.is_available())"
   ```

2. **Out of memory errors**
   ```bash
   # Reduce batch size
   export GPU_BATCH_SIZE=16
   
   # Or use smaller model
   export LOCAL_EMBEDDING_MODEL=all-MiniLM-L6-v2
   ```

3. **Docker GPU access denied**
   ```bash
   # Restart Docker after installing nvidia-container-toolkit
   sudo systemctl restart docker
   
   # Test GPU access
   docker run --rm --gpus all nvidia/cuda:12.1-runtime-ubuntu22.04 nvidia-smi
   ```

4. **Model download issues**
   ```bash
   # Pre-download models
   python -c "from sentence_transformers import SentenceTransformer; SentenceTransformer('all-MiniLM-L6-v2')"
   ```

### Monitoring GPU Usage
```bash
# Real-time GPU monitoring
watch -n 1 nvidia-smi

# Memory usage
nvidia-smi --query-gpu=memory.used,memory.free,memory.total --format=csv
```

## CPU Fallback

The system automatically falls back to CPU or OpenAI API if:
- No GPU is available
- GPU memory is insufficient 
- CUDA drivers are missing
- Local model fails to load

This ensures the system always works regardless of hardware configuration.

## Advanced Configuration

### Custom Models
```bash
# Use larger, higher-quality model
export LOCAL_EMBEDDING_MODEL=all-mpnet-base-v2
export GPU_BATCH_SIZE=16  # Reduce for larger models
```

### Hybrid Setup
```bash
# Use GPU for large batches, API for small ones
export USE_LOCAL_EMBEDDINGS=true
export OPENAI_API_KEY=your_key  # Fallback for failures
```

### Production Optimization
```bash
# Optimize for throughput
export GPU_BATCH_SIZE=64
export LOCAL_EMBEDDING_MODEL=all-MiniLM-L6-v2

# Optimize for quality
export GPU_BATCH_SIZE=16
export LOCAL_EMBEDDING_MODEL=all-mpnet-base-v2
```