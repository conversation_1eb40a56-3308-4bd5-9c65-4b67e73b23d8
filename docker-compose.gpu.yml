version: '3.8'

services:
  mcp-crawl4ai-rag-gpu:
    build:
      context: .
      dockerfile: Dockerfile.gpu
      args:
        - PORT=8051
    ports:
      - "8051:8051"
    env_file:
      - .env
    environment:
      - USE_LOCAL_EMBEDDINGS=true
      - USE_GPU=true
      - GPU_BATCH_SIZE=32
      - LOCAL_EMBEDDING_MODEL=all-MiniLM-L6-v2
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    volumes:
      - ./.cache:/app/.cache
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8051/sse"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

# For systems without docker-compose v3.8 GPU support, use this alternative:
  mcp-crawl4ai-rag-gpu-legacy:
    build:
      context: .
      dockerfile: Dockerfile.gpu
      args:
        - PORT=8052
    ports:
      - "8052:8052"
    env_file:
      - .env
    environment:
      - PORT=8052
      - USE_LOCAL_EMBEDDINGS=true
      - USE_GPU=true
      - GPU_BATCH_SIZE=32
      - LOCAL_EMBEDDING_MODEL=all-MiniLM-L6-v2
    runtime: nvidia
    volumes:
      - ./.cache:/app/.cache
    restart: unless-stopped
    profiles:
      - legacy