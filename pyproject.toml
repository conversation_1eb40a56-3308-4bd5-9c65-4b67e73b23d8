[project]
name = "crawl4ai-mcp"
version = "0.1.0"
description = "MCP server for integrating web crawling and RAG into AI agents and AI coding assistants"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "crawl4ai>=0.7.0",
    "fastmcp>=2.5.0",
    "fastapi>=0.115.0,<0.116.0",
    "pydantic>=2.7.0,<3.0.0",
    "supabase==2.15.1",
    "openai==1.71.0",
    "python-dotenv>=1.0.0",
    "neo4j>=5.28.1",
    "requests>=2.25.0",
    "rich>=13.0.0",
    "prompt-toolkit>=3.0.0",
    "aiohttp>=3.9.0",
]
