# The transport for the MCP server - either 'sse' or 'stdio' (defaults to sse if left empty)
TRANSPORT=

# Host to bind to if using sse as the transport (leave empty if using stdio)
# Set this to 0.0.0.0 if using Docker, otherwise set to localhost (if using uv)
HOST=

# Port to listen on if using sse as the transport (leave empty if using stdio)
PORT=

# Get your Open AI API Key by following these instructions -
# https://help.openai.com/en/articles/4936850-where-do-i-find-my-openai-api-key
# This is for the embedding model
OPENAI_API_KEY=

# Embedding Model Configuration
# For OpenAI API: text-embedding-3-small (1536 dims), text-embedding-3-large (3072 dims), text-embedding-ada-002 (1536 dims)
# For Local GPU: all-MiniLM-L6-v2 (384 dims), all-mpnet-base-v2 (768 dims)
EMBEDDING_MODEL=text-embedding-3-small
EMBEDDING_DIMENSIONS=1536

# Embedding Strategy Priority: Ollama > Local GPU > OpenAI API

# 1. OLLAMA EMBEDDINGS (Recommended for production with Ollama setup)
USE_OLLAMA_EMBEDDINGS=true       # Use Ollama for embeddings (highest priority)
OLLAMA_BASE_URL=http://************:11434   # Your Ollama server URL
OLLAMA_EMBEDDING_MODEL=nomic-embed-text      # Ollama embedding model (768 dims)

# 2. GPU Acceleration Settings (fallback if Ollama disabled)
USE_LOCAL_EMBEDDINGS=true  # Use local GPU model (faster, free) vs OpenAI API (slower, costs money)
USE_GPU=true               # Enable GPU acceleration for local embeddings
GPU_BATCH_SIZE=32          # Batch size for GPU processing (adjust based on VRAM)
LOCAL_EMBEDDING_MODEL=all-MiniLM-L6-v2  # Local model for GPU acceleration

# The LLM you want to use for summaries and contextual embeddings
# Generally this is a very cheap and fast LLM like gpt-4.1-nano
MODEL_CHOICE=

# RAG strategies - set these to "true" or "false" (default to "false")
# USE_CONTEXTUAL_EMBEDDINGS: Enhances embeddings with contextual information for better retrieval
USE_CONTEXTUAL_EMBEDDINGS=false

# USE_HYBRID_SEARCH: Combines vector similarity search with keyword search for better results
USE_HYBRID_SEARCH=false

# USE_AGENTIC_RAG: Enables code example extraction, storage, and specialized code search functionality
USE_AGENTIC_RAG=false

# USE_RERANKING: Applies cross-encoder reranking to improve search result relevance
USE_RERANKING=false

# USE_KNOWLEDGE_GRAPH: Enables AI hallucination detection and repository parsing tools
# Currently DISABLED - preparing for migration from Neo4j to Memgraph
# Set to false to disable knowledge graph functionality
USE_KNOWLEDGE_GRAPH=false

# REQUIRED: Supabase Configuration for RAG functionality
# Without these, the server will start but RAG tools will be disabled
# For Supabase Cloud: Get your SUPABASE_URL from https://supabase.com/dashboard/project/<your project ID>/settings/api
# For Self-hosted: Use your self-hosted Supabase URL (e.g., https://your-domain.com)
SUPABASE_URL=

# Get your SUPABASE_SERVICE_KEY from the API section of your Supabase project settings
# For Supabase Cloud: https://supabase.com/dashboard/project/<your project ID>/settings/api
# For Self-hosted: Use your service_role secret key
# On this page it is called the service_role secret.
SUPABASE_SERVICE_KEY=

# Graph Database Configuration for Knowledge Graph Tools
# DISABLED: Neo4j support (will be replaced with Memgraph)
# These variables are kept for backward compatibility but currently not used

# Graph DB connection URI - will support Memgraph bolt://localhost:7687 in future
# IMPORTANT: When using Docker, change localhost to host.docker.internal
# GRAPH_DB_URI=bolt://localhost:7687

# Graph DB username (usually 'neo4j' for Neo4j, 'memgraph' for Memgraph)
# GRAPH_DB_USER=neo4j

# Graph DB password for your database instance
# GRAPH_DB_PASSWORD=

# Future Memgraph Configuration (not yet implemented)
# MEMGRAPH_URI=bolt://localhost:7687
# MEMGRAPH_USER=
# MEMGRAPH_PASSWORD=

# Performance Configuration - Optimized crawling behavior
# These settings control timeout behavior, memory usage, and early termination

# Default timeout for smart_crawl_url operations (in minutes)
CRAWL_TIMEOUT_MINUTES=15

# Timeout per individual URL during batch crawling (in seconds)
URL_TIMEOUT_SECONDS=30

# Maximum content size per page (in characters, for memory management)
MAX_CONTENT_SIZE=50000

# Early termination threshold for batch operations (stop after N successful crawls)
EARLY_TERMINATION_THRESHOLD=50

# Memory management - maximum total content size before cleanup (in characters)
MEMORY_CLEANUP_THRESHOLD=50000000

# Logging Configuration
# LOG_LEVEL: Control the verbosity of server logs (DEBUG, INFO, WARNING, ERROR, CRITICAL)
# Default: INFO (recommended for production)
# Use DEBUG for troubleshooting, ERROR for minimal logging
LOG_LEVEL=INFO

# Job Management Configuration
# USE_JOB_MANAGEMENT: Enable asynchronous job processing for long-running crawl operations
# This prevents Claude Desktop timeout issues by using Crawl4AI's built-in HTTP API server
USE_JOB_MANAGEMENT=false

# Crawl4AI HTTP API Server Settings (used when USE_JOB_MANAGEMENT=true)
# CRAWL4AI_HOST: Host for the Crawl4AI HTTP API server (default: localhost)
CRAWL4AI_HOST=localhost

# CRAWL4AI_PORT: Port for the Crawl4AI HTTP API server (default: 11235)
CRAWL4AI_PORT=11235

# CRAWL4AI_STARTUP_TIMEOUT: Time to wait for server startup in seconds (default: 30)
CRAWL4AI_STARTUP_TIMEOUT=30

# CRAWL4AI_MAX_POLL_ATTEMPTS: Maximum attempts to poll job status (default: 600)
CRAWL4AI_MAX_POLL_ATTEMPTS=600

# CRAWL4AI_POLL_INTERVAL: Interval between job status polls in seconds (default: 1.0)
CRAWL4AI_POLL_INTERVAL=1.0