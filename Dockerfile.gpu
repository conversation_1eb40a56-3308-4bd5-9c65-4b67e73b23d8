FROM nvidia/cuda:latest

ARG PORT=8051

# Set up working directory
WORKDIR /app

# Install Python and system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    python3.12 \
    python3.12-dev \
    python3-pip \
    curl \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Create symbolic link for python
RUN ln -s /usr/bin/python3.12 /usr/bin/python

# Install uv package manager
RUN pip install uv

# Copy the entire project
COPY . .

# Install dependencies and setup crawl4ai
RUN uv pip install --system -e . && \
    crawl4ai-setup

# Create required cache directories
RUN mkdir -p .cache/embeddings .cache/crawl_results .cache/queries

# Health check for MCP server - check if SSE endpoint responds
HEALTHCHECK --interval=30s --timeout=5s --start-period=5s --retries=3 \
    CMD timeout 1s curl -s http://localhost:${PORT}/sse >/dev/null 2>&1 || test $? -eq 124

# Expose port
EXPOSE ${PORT}

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV NVIDIA_VISIBLE_DEVICES=all
ENV NVIDIA_DRIVER_CAPABILITIES=compute,utility

# Enable GPU by default
ENV USE_LOCAL_EMBEDDINGS=true
ENV USE_GPU=true
ENV GPU_BATCH_SIZE=32

# Command to run the MCP server
CMD ["python", "src/main.py"]