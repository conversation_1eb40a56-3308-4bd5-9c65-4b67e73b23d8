#!/usr/bin/env python3
"""
Basic test script for dashboard API endpoints
Tests the dashboard functionality without full pytest setup
"""

import asyncio
import aiohttp
import json
import time
from typing import Dict, Any

BASE_URL = "http://localhost:8051"

async def test_dashboard_endpoints():
    """Test the dashboard API endpoints"""
    print("🧪 Testing Dashboard API Endpoints")
    print("=" * 50)
    
    async with aiohttp.ClientSession() as session:
        
        # Test 1: Dashboard page loads
        print("1. Testing dashboard page...")
        try:
            async with session.get(f"{BASE_URL}/dashboard") as resp:
                if resp.status == 200:
                    content = await resp.text()
                    if "Embedding Model Evaluation Dashboard" in content:
                        print("   ✅ Dashboard page loads correctly")
                    else:
                        print("   ❌ Dashboard page content incorrect")
                else:
                    print(f"   ❌ Dashboard page failed: {resp.status}")
        except Exception as e:
            print(f"   ❌ Dashboard page error: {e}")
        
        # Test 2: Start benchmark (with mock data)
        print("\n2. Testing benchmark start...")
        try:
            form_data = aiohttp.FormData()
            form_data.add_field('test_name', 'API Test Run')
            form_data.add_field('embedding_model', 'ollama')
            form_data.add_field('url', 'https://httpbin.org/html')
            
            async with session.post(f"{BASE_URL}/api/benchmark/start", data=form_data) as resp:
                if resp.status == 200:
                    result = await resp.json()
                    if result.get('success'):
                        test_run_id = result.get('test_run_id')
                        print(f"   ✅ Benchmark started: {test_run_id}")
                        
                        # Test 3: Status endpoint
                        await test_status_endpoint(session, test_run_id)
                        
                        # Wait a bit for benchmark to complete
                        print("\n   ⏳ Waiting for benchmark to complete...")
                        await asyncio.sleep(5)
                        
                        # Test 4: Results endpoint
                        await test_results_endpoint(session, test_run_id)
                        
                    else:
                        print(f"   ❌ Benchmark start failed: {result}")
                else:
                    print(f"   ❌ Benchmark start failed: {resp.status}")
        except Exception as e:
            print(f"   ❌ Benchmark start error: {e}")

async def test_status_endpoint(session: aiohttp.ClientSession, test_run_id: str):
    """Test the status endpoint (SSE)"""
    print(f"\n3. Testing status endpoint...")
    try:
        # Test a few status updates
        for i in range(3):
            async with session.get(f"{BASE_URL}/api/benchmark/status/{test_run_id}") as resp:
                if resp.status == 200:
                    # Read a chunk of SSE data
                    chunk = await resp.content.read(100)
                    if chunk:
                        print(f"   ✅ Status endpoint responding (attempt {i+1})")
                        break
                    else:
                        await asyncio.sleep(1)
                else:
                    print(f"   ❌ Status endpoint failed: {resp.status}")
                    break
    except Exception as e:
        print(f"   ❌ Status endpoint error: {e}")

async def test_results_endpoint(session: aiohttp.ClientSession, test_run_id: str):
    """Test the results endpoint"""
    print(f"\n4. Testing results endpoint...")
    try:
        async with session.get(f"{BASE_URL}/api/benchmark/results/{test_run_id}") as resp:
            if resp.status == 200:
                result = await resp.json()
                if result.get('success'):
                    print("   ✅ Results endpoint working")
                    metrics = result.get('metrics', {})
                    print(f"   📊 Sample metrics: {list(metrics.keys())}")
                else:
                    print(f"   ❌ Results endpoint failed: {result}")
            elif resp.status == 404:
                print("   ⚠️ Results not found yet (test may still be running)")
            else:
                print(f"   ❌ Results endpoint failed: {resp.status}")
    except Exception as e:
        print(f"   ❌ Results endpoint error: {e}")

async def test_compare_endpoint(session: aiohttp.ClientSession):
    """Test the compare endpoint with dummy IDs"""
    print(f"\n5. Testing compare endpoint...")
    try:
        dummy_id1 = "00000000-0000-0000-0000-000000000001"
        dummy_id2 = "00000000-0000-0000-0000-000000000002"
        
        async with session.get(f"{BASE_URL}/api/benchmark/compare?test_run_1={dummy_id1}&test_run_2={dummy_id2}") as resp:
            if resp.status == 404:
                print("   ✅ Compare endpoint working (404 expected for dummy IDs)")
            elif resp.status == 200:
                result = await resp.json()
                print("   ✅ Compare endpoint working")
            else:
                print(f"   ❌ Compare endpoint failed: {resp.status}")
    except Exception as e:
        print(f"   ❌ Compare endpoint error: {e}")

def main():
    """Main test function"""
    print("🚀 Starting Dashboard API Tests")
    print(f"📡 Testing server at: {BASE_URL}")
    print(f"⏰ Started at: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        asyncio.run(test_dashboard_endpoints())
        print("\n" + "=" * 50)
        print("✅ Test suite completed!")
        print("💡 To run the dashboard:")
        print("   1. Start the MCP server: python src/main.py")
        print("   2. Visit: http://localhost:8051/dashboard")
        print("   3. Run a real benchmark test!")
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")

if __name__ == "__main__":
    main()