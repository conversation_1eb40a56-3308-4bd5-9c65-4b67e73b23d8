{
  "mcpServers": {
    "brave-search": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-brave-search"
      ],
      "env": {
        "BRAVE_API_KEY": "BSAI0YzdUNnXPQIK1crQi1CjbzvB_1V"
      }
    },
    "supabase": {
      "command": "node",
      "args": [
        "/home/<USER>/dev/tools/mcp_servers/selfhosted-supabase-mcp/dist/index.js",
        "--url",
        "http://*************:8000",
        "--anon-key",
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.ewogICJyb2xlIjogImFub24iLAogICJpc3MiOiAic3VwYWJhc2UiLAogICJpYXQiOiAxNzQxMDk1MDAwLAogICJleHAiOiAxODk4ODYxNDAwCn0.J1N7sT41EI4ORiRFvPm3FPDArs43dzEq9g9-iq7d3pY",
        "--service-key",
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.ewogICJyb2xlIjogInNlcnZpY2Vfcm9sZSIsCiAgImlzcyI6ICJzdXBhYmFzZSIsCiAgImlhdCI6IDE3NDEwOTUwMDAsCiAgImV4cCI6IDE4OTg4NjE0MDAKfQ.4OLZbhrbccD5vfy14HcWgYDgJsSzUXimxgcGA3OgaV8",
        "--jwt-secret",
        "d124QZSveM2X1rAdcDdO6gF5a6XOvnmlqRLa8whV",
        "--db-url",
        "***********************************************************************************"
      ]
    },
    "sequential-thinking": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-sequential-thinking@latest"
      ],
      "disabled": false
    },
    "aydb-mssql": {
      "command": "node",
      "args": [
        "/home/<USER>/dev/tools/mcp_servers/mssql-mcp-server/build/src/index.js"
      ],
      "env": {
        "MSSQL_HOST": "************",
        "MSSQL_PORT": "1433",
        "MSSQL_DATABASE": "AYDB",
        "MSSQL_USER": "sa",
        "MSSQL_PASSWORD": "dyA8gTKBhAb2ZV",
        "MSSQL_ENCRYPT": "false",
        "MSSQL_TRUST_SERVER_CERTIFICATE": "true"
      }
    },
    "puppeteer": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-puppeteer"
      ]
    },
    "magic": {
      "command": "npx",
      "args": [
        "-y",
        "@21st-dev/magic@latest",
        "API_KEY=\"5d1e394d35738ac591c84ad08f92978af4686447a94db9ca5ed8d5b36870941a\""
      ]
    },
    "context7": {
      "command": "npx",
      "args": [
        "-y",
        "@upstash/context7-mcp"
      ]
    }
}