services:
  # MCP Server - Lightweight API layer
  mcp-server:
    build:
      context: .
      dockerfile: Dockerfile.mcp
      args:
        PORT: 8051
    container_name: crawl4ai-rag-mcp-server
    ports:
      - "0.0.0.0:8051:8051"  # Bind to all interfaces to allow external connections
    depends_on:
      crawl4ai-worker:
        condition: service_healthy
    environment:
      # Server configuration
      - TRANSPORT=sse
      - HOST=0.0.0.0  # Inside container, but bound to localhost externally
      - PORT=8051
      
      # Fix transformers deprecation warning
      - HF_HOME=/app/.cache/huggingface
      
      # API Keys (set these in your .env file)
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - MODEL_CHOICE=${MODEL_CHOICE:-gpt-4o-mini}
      
      # RAG Strategy flags
      - USE_CONTEXTUAL_EMBEDDINGS=${USE_CONTEXTUAL_EMBEDDINGS:-false}
      - USE_HYBRID_SEARCH=${USE_HYBRID_SEARCH:-true}
      - USE_AGENTIC_RAG=${USE_AGENTIC_RAG:-false}
      - USE_RERANKING=${USE_RERANKING:-true}
      - USE_KNOWLEDGE_GRAPH=${USE_KNOWLEDGE_GRAPH:-false}
      
      # Supabase configuration
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_SERVICE_KEY=${SUPABASE_SERVICE_KEY}
      
      # Graph Database (disabled for now, prepared for Memgraph)
      - GRAPH_DB_URI=${GRAPH_DB_URI:-}
      - GRAPH_DB_USER=${GRAPH_DB_USER:-}
      - GRAPH_DB_PASSWORD=${GRAPH_DB_PASSWORD:-}
      
      # Future Memgraph configuration
      - MEMGRAPH_URI=${MEMGRAPH_URI:-}
      - MEMGRAPH_USER=${MEMGRAPH_USER:-}
      - MEMGRAPH_PASSWORD=${MEMGRAPH_PASSWORD:-}
      
      # Job Management Configuration - External Service
      - USE_JOB_MANAGEMENT=${USE_JOB_MANAGEMENT:-true}
      - CRAWL4AI_HOST=crawl4ai-worker  # Use service name for Docker networking
      - CRAWL4AI_PORT=${CRAWL4AI_PORT:-11235}
      - CRAWL4AI_STARTUP_TIMEOUT=${CRAWL4AI_STARTUP_TIMEOUT:-60}
      - CRAWL4AI_MAX_POLL_ATTEMPTS=${CRAWL4AI_MAX_POLL_ATTEMPTS:-600}
      - CRAWL4AI_POLL_INTERVAL=${CRAWL4AI_POLL_INTERVAL:-1.0}
      - CRAWL4AI_CONNECTION_RETRIES=${CRAWL4AI_CONNECTION_RETRIES:-3}
      - CRAWL4AI_CONNECTION_TIMEOUT=${CRAWL4AI_CONNECTION_TIMEOUT:-30}
    
    env_file:
      - .env
    
    volumes:
      # Shared cache for embeddings and queries only
      - crawl4ai_embeddings_cache:/app/.cache/embeddings
      - crawl4ai_queries_cache:/app/.cache/queries
    
    networks:
      - mcp-network
    
    restart: unless-stopped
    
    # Security configurations
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    cap_add:
      - NET_BIND_SERVICE  # Only if binding to privileged ports
    read_only: false  # Application needs to write logs and temporary files
    tmpfs:
      - /run
      - /var/run
    ulimits:
      nofile:
        soft: 1024
        hard: 1024
      nproc:
        soft: 64
        hard: 64
    mem_limit: 1g  # Reduced for MCP server only
    cpus: 0.5      # Reduced for MCP server only
    
    healthcheck:
      test: ["CMD", "bash", "-c", "timeout 1s curl -s http://localhost:8051/sse >/dev/null 2>&1 || test $? -eq 124"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 40s

  # Crawl4AI Worker - Dedicated crawling service
  crawl4ai-worker:
    build:
      context: .
      dockerfile: Dockerfile.crawl4ai
    container_name: crawl4ai-worker
    ports:
      - "127.0.0.1:11235:11235"  # Crawl4AI HTTP API server
    environment:
      - PYTHONUNBUFFERED=1
      - PYTHONDONTWRITEBYTECODE=1
      - CRAWL4AI_CACHE_DIR=/app/.crawl4ai_cache
    volumes:
      # Dedicated cache for crawl4ai worker
      - crawl4ai_cache:/app/.crawl4ai_cache
      - crawl4ai_logs:/app/logs
    networks:
      - mcp-network
    restart: unless-stopped
    
    # Security configurations
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    cap_add:
      - SYS_ADMIN  # Required for Chromium in container
    read_only: false  # Crawl4AI needs to write temporary files
    tmpfs:
      - /tmp
      - /var/tmp
    ulimits:
      nofile:
        soft: 2048
        hard: 2048
      nproc:
        soft: 128
        hard: 128
    mem_limit: 3g  # More memory for crawling operations
    cpus: 2.0      # More CPU for crawling operations
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11235/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Future: Memgraph database service (when we migrate from Neo4j)
  # memgraph:
  #   image: memgraph/memgraph:latest
  #   container_name: memgraph-db
  #   ports:
  #     - "7687:7687"
  #     - "7444:7444"
  #   environment:
  #     - MEMGRAPH_USER=${MEMGRAPH_USER:-memgraph}
  #     - MEMGRAPH_PASSWORD=${MEMGRAPH_PASSWORD:-memgraph}
  #   volumes:
  #     - memgraph_data:/var/lib/memgraph
  #     - memgraph_log:/var/log/memgraph
  #     - memgraph_etc:/etc/memgraph
  #   networks:
  #     - mcp-network
  #   restart: unless-stopped

volumes:
  # Crawl4AI worker dedicated volumes
  crawl4ai_cache:
    driver: local
  crawl4ai_logs:
    driver: local
  # Shared cache volumes between services
  crawl4ai_embeddings_cache:
    driver: local
  crawl4ai_queries_cache:
    driver: local
  pip_cache:
    driver: local
  # memgraph_data:
  #   driver: local
  # memgraph_log:
  #   driver: local
  # memgraph_etc:
  #   driver: local

networks:
  mcp-network:
    driver: bridge