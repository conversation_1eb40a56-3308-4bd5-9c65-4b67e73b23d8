"""
MCP server for web crawling with Crawl4AI.

This server provides tools to crawl websites using Crawl4AI, automatically detecting
the appropriate crawl method based on URL type (sitemap, txt file, or regular webpage).
Also includes AI hallucination detection and repository parsing tools using Neo4j knowledge graphs.
"""
from mcp.server.fastmcp import FastMCP, Context
from sentence_transformers import CrossEncoder
from contextlib import asynccontextmanager
from collections.abc import AsyncIterator
from dataclasses import dataclass
from typing import List, Dict, Any, Optional
from urllib.parse import urlparse, urldefrag
from datetime import datetime

@dataclass
class Crawl4AIContext:
    """Context object containing all initialized resources for the MCP server."""
    crawler: Any
    supabase_client: Any
    reranking_model: Optional[Any] = None
    knowledge_validator: Optional[Any] = None
    repo_extractor: Optional[Any] = None
    reliable_crawler: Optional[Any] = None
    job_manager: Optional[Any] = None

def get_resources():
    """Get global resources for MCP tools. This is a dependency function for FastMCP."""
    return Crawl4AIContext(
        crawler=_global_crawler,
        supabase_client=_global_supabase_client,
        reranking_model=_cached_reranking_model,
        knowledge_validator=_global_knowledge_validator,
        repo_extractor=_global_repo_extractor,
        reliable_crawler=_global_reliable_crawler,
        job_manager=_global_job_manager
    )

def create_standard_error_response(error: str, context: str = "", error_type: str = "error", details: Optional[Dict[str, Any]] = None) -> str:
    """
    Create a standardized error response for MCP tools.
    
    Args:
        error: The error message
        context: Additional context about where the error occurred
        error_type: Type of error (error, validation_error, network_error, etc.)
        details: Additional error details
        
    Returns:
        JSON string with standardized error format
    """
    import json
    error_response = {
        "success": False,
        "error": {
            "message": error,
            "type": error_type,
            "context": context,
            "timestamp": datetime.utcnow().isoformat() + "Z"
        }
    }
    
    if details:
        error_response["error"]["details"] = details
    
    return json.dumps(error_response, indent=2)

def create_standard_success_response(data: Dict[str, Any], message: str = "Operation completed successfully") -> str:
    """
    Create a standardized success response for MCP tools.
    
    Args:
        data: The response data
        message: Success message
        
    Returns:
        JSON string with standardized success format
    """
    import json
    success_response = {
        "success": True,
        "message": message,
        "timestamp": datetime.utcnow().isoformat() + "Z",
        "data": data
    }
    
    return json.dumps(success_response, indent=2)

def serialize_datetime_objects(obj: Any) -> Any:
    """
    Recursively convert datetime objects to ISO format strings for JSON serialization.
    
    Args:
        obj: Object that may contain datetime objects
        
    Returns:
        Object with datetime objects converted to ISO strings
    """
    if isinstance(obj, datetime):
        return obj.isoformat()
    elif isinstance(obj, dict):
        return {key: serialize_datetime_objects(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [serialize_datetime_objects(item) for item in obj]
    else:
        return obj
from xml.etree import ElementTree
from dotenv import load_dotenv
from supabase import Client
from pathlib import Path
import requests
import asyncio
import json
import os
import re
import concurrent.futures
import sys
import gc
import time
import signal
import logging

# Global cache for CrossEncoder model to prevent concurrent loading issues
_cached_reranking_model = None
_reranking_model_lock = None
_initialization_complete = False

# Global singleton instances for persistent resource management
_global_crawler = None
_global_supabase_client = None
_global_reliable_crawler = None
_global_knowledge_validator = None
_global_repo_extractor = None
_global_job_manager = None
_initialization_lock = None
_server_initialized = False

# Use HTTP client instead of direct crawl4ai imports for lightweight container
from crawl4ai_client import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode

# Add knowledge_graphs folder to path for importing knowledge graph modules
knowledge_graphs_path = Path(__file__).resolve().parent.parent / 'knowledge_graphs'
sys.path.append(str(knowledge_graphs_path))

from utils import (
    get_supabase_client, 
    add_documents_to_supabase, 
    search_documents,
    extract_code_blocks,
    generate_code_example_summary,
    add_code_examples_to_supabase,
    update_source_info,
    extract_source_summary,
    search_code_examples
)

# Import knowledge graph modules conditionally
KnowledgeGraphValidator = None
DirectNeo4jExtractor = None
AIScriptAnalyzer = None
HallucinationReporter = None

# Only import knowledge graph modules if functionality is enabled and available
try:
    if os.getenv("USE_KNOWLEDGE_GRAPH", "false") == "true":
        from knowledge_graph_validator import KnowledgeGraphValidator
        from parse_repo_into_neo4j import DirectNeo4jExtractor
        from ai_script_analyzer import AIScriptAnalyzer
        from hallucination_reporter import HallucinationReporter
except ImportError as e:
    print(f"ℹ️  Knowledge graph modules not available: {e}")
    print("ℹ️  Knowledge graph functionality will be disabled")

# Import reliability service
from reliability import (
    ReliableCrawler,
    RetryConfig,
    TimeoutConfig,
    CircuitBreakerConfig,
    CacheConfig,
    create_default_reliable_crawler,
    create_production_reliable_crawler
)

# Import job management
from job_manager import Crawl4AIJobManager, JobStatus, JobResult, get_job_manager_config

# Load environment variables from the project root .env file
project_root = Path(__file__).resolve().parent.parent
dotenv_path = project_root / '.env'

# Force override of existing environment variables
load_dotenv(dotenv_path, override=True)

# Configure logging to reduce connection error noise
# This reduces log noise from normal SSE client disconnections
logging.getLogger("anyio").setLevel(logging.CRITICAL)
logging.getLogger("uvicorn.error").setLevel(logging.WARNING)

# Log level can be controlled via environment variable for debugging
log_level = os.getenv("LOG_LEVEL", "INFO").upper()
if log_level in ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]:
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level))
    print(f"🔧 Log level set to: {log_level}")
else:
    print(f"⚠️ Invalid LOG_LEVEL '{log_level}', using default INFO")

# Helper functions for Neo4j validation and error handling
def validate_neo4j_connection() -> bool:
    """Check if Neo4j environment variables are configured."""
    return all([
        os.getenv("NEO4J_URI"),
        os.getenv("NEO4J_USER"),
        os.getenv("NEO4J_PASSWORD")
    ])

def format_neo4j_error(error: Exception) -> str:
    """Format Neo4j connection errors for user-friendly messages."""
    error_str = str(error).lower()
    if "authentication" in error_str or "unauthorized" in error_str:
        return "Neo4j authentication failed. Check NEO4J_USER and NEO4J_PASSWORD."
    elif "connection" in error_str or "refused" in error_str or "timeout" in error_str:
        return "Cannot connect to Neo4j. Check NEO4J_URI and ensure Neo4j is running."
    elif "database" in error_str:
        return "Neo4j database error. Check if the database exists and is accessible."
    else:
        return f"Neo4j error: {str(error)}"

def validate_script_path(script_path: str) -> Dict[str, Any]:
    """Validate script path and return error info if invalid."""
    if not script_path or not isinstance(script_path, str):
        return {"valid": False, "error": "Script path is required"}
    
    if not os.path.exists(script_path):
        return {"valid": False, "error": f"Script not found: {script_path}"}
    
    if not script_path.endswith('.py'):
        return {"valid": False, "error": "Only Python (.py) files are supported"}
    
    try:
        # Check if file is readable
        with open(script_path, 'r', encoding='utf-8') as f:
            f.read(1)  # Read first character to test
        return {"valid": True}
    except Exception as e:
        return {"valid": False, "error": f"Cannot read script file: {str(e)}"}

def validate_github_url(repo_url: str) -> Dict[str, Any]:
    """Validate GitHub repository URL."""
    if not repo_url or not isinstance(repo_url, str):
        return {"valid": False, "error": "Repository URL is required"}
    
    repo_url = repo_url.strip()
    
    # Basic GitHub URL validation
    if not ("github.com" in repo_url.lower() or repo_url.endswith(".git")):
        return {"valid": False, "error": "Please provide a valid GitHub repository URL"}
    
    # Check URL format
    if not (repo_url.startswith("https://") or repo_url.startswith("git@")):
        return {"valid": False, "error": "Repository URL must start with https:// or git@"}
    
    return {"valid": True, "repo_name": repo_url.split('/')[-1].replace('.git', '')}

# Create a dataclass for our application context
@dataclass
class Crawl4AIContext:
    """Context for the Crawl4AI MCP server."""
    crawler: AsyncWebCrawler
    supabase_client: Client
    reranking_model: Optional[CrossEncoder] = None
    knowledge_validator: Optional[Any] = None  # KnowledgeGraphValidator when available
    repo_extractor: Optional[Any] = None       # DirectNeo4jExtractor when available
    reliable_crawler: Optional[ReliableCrawler] = None  # ReliableCrawler for resilient operations
    job_manager: Optional[Crawl4AIJobManager] = None  # Job manager for async operations

async def _initialize_global_resources():
    """Initialize all global resources once."""
    global _global_crawler, _global_supabase_client, _global_reliable_crawler
    global _global_knowledge_validator, _global_repo_extractor, _global_job_manager
    global _cached_reranking_model, _reranking_model_lock, _server_initialized
    
    # Skip if already initialized
    if _server_initialized:
        return
    
    # Create browser configuration
    browser_config = BrowserConfig(
        headless=True,
        verbose=False
    )
    
    # Initialize the crawler
    print("🔄 Initializing web crawler...")
    _global_crawler = AsyncWebCrawler(config=browser_config)
    await _global_crawler.__aenter__()
    print("✅ Web crawler initialized")
    
    # Initialize Supabase client
    print("🔄 Initializing Supabase client...")
    _global_supabase_client = get_supabase_client()
    print("✅ Supabase client initialized")
    
    # Initialize cross-encoder model for reranking if enabled (with global caching to prevent race conditions)
    if _reranking_model_lock is None:
        _reranking_model_lock = asyncio.Lock()
    
    if os.getenv("USE_RERANKING", "false") == "true":
        print("🔄 Initializing reranking model...")
        async with _reranking_model_lock:
            if _cached_reranking_model is None:
                try:
                    print("🔄 Loading CrossEncoder model for reranking...")
                    # Load model in executor to prevent blocking
                    loop = asyncio.get_event_loop()
                    _cached_reranking_model = await loop.run_in_executor(
                        None, 
                        lambda: CrossEncoder("cross-encoder/ms-marco-MiniLM-L-6-v2", device="cpu")
                    )
                    print("✅ CrossEncoder model loaded successfully")
                except Exception as e:
                    print(f"❌ Failed to load reranking model: {e}")
                    _cached_reranking_model = None
            else:
                print("✅ Using cached CrossEncoder model")
    else:
        print("ℹ️  Reranking disabled")
    
    # Initialize Neo4j components if configured and enabled
    knowledge_graph_enabled = os.getenv("USE_KNOWLEDGE_GRAPH", "false") == "true"
    
    if knowledge_graph_enabled:
        print("🔄 Initializing knowledge graph components...")
        neo4j_uri = os.getenv("NEO4J_URI")
        neo4j_user = os.getenv("NEO4J_USER")
        neo4j_password = os.getenv("NEO4J_PASSWORD")
        
        if neo4j_uri and neo4j_user and neo4j_password:
            try:
                # Initialize knowledge graph validator
                _global_knowledge_validator = KnowledgeGraphValidator(neo4j_uri, neo4j_user, neo4j_password)
                await _global_knowledge_validator.initialize()
                print("✅ Knowledge graph validator initialized")
                
                # Initialize repository extractor
                _global_repo_extractor = DirectNeo4jExtractor(neo4j_uri, neo4j_user, neo4j_password)
                await _global_repo_extractor.initialize()
                print("✅ Repository extractor initialized")
                
            except Exception as e:
                print(f"❌ Failed to initialize Neo4j components: {format_neo4j_error(e)}")
                _global_knowledge_validator = None
                _global_repo_extractor = None
        else:
            print("ℹ️  Neo4j credentials not configured - knowledge graph tools will be unavailable")
    else:
        print("ℹ️  Knowledge graph functionality disabled - set USE_KNOWLEDGE_GRAPH=true to enable")
    
    # Initialize reliable crawler based on environment
    print("🔄 Initializing reliable crawler...")
    environment = os.getenv("ENVIRONMENT", "development").lower()
    
    if environment == "production":
        _global_reliable_crawler = create_production_reliable_crawler()
        print("✅ Production-optimized reliable crawler initialized")
    else:
        _global_reliable_crawler = create_default_reliable_crawler()
        print("✅ Development reliable crawler initialized")
    
    # Initialize job manager for async operations
    job_management_enabled = os.getenv("USE_JOB_MANAGEMENT", "false") == "true"
    if job_management_enabled:
        print("🔄 Initializing job manager...")
        try:
            job_config = get_job_manager_config()
            _global_job_manager = Crawl4AIJobManager(**job_config)
            await _global_job_manager.start()
            print("✅ Job manager initialized")
        except Exception as e:
            print(f"❌ Failed to initialize job manager: {e}")
            print("ℹ️  Job management disabled - long operations may timeout")
            _global_job_manager = None
    else:
        print("ℹ️  Job management disabled - set USE_JOB_MANAGEMENT=true to enable")
    
    # Mark server as initialized
    _server_initialized = True

async def _cleanup_global_resources():
    """Cleanup all global resources on server shutdown."""
    global _global_crawler, _global_knowledge_validator, _global_repo_extractor
    global _global_job_manager, _server_initialized
    
    if _global_crawler:
        try:
            await _global_crawler.__aexit__(None, None, None)
            print("✓ Web crawler closed")
        except Exception as e:
            print(f"Error closing web crawler: {e}")
    
    if _global_knowledge_validator:
        try:
            await _global_knowledge_validator.close()
            print("✓ Knowledge graph validator closed")
        except Exception as e:
            print(f"Error closing knowledge validator: {e}")
    
    if _global_repo_extractor:
        try:
            await _global_repo_extractor.close()
            print("✓ Repository extractor closed")
        except Exception as e:
            print(f"Error closing repository extractor: {e}")
    
    if _global_job_manager:
        try:
            await _global_job_manager.cleanup()
            print("✓ Job manager closed")
        except Exception as e:
            print(f"Error closing job manager: {e}")
    
    _server_initialized = False

@asynccontextmanager
async def crawl4ai_lifespan(server: FastMCP) -> AsyncIterator[Crawl4AIContext]:
    """
    Manages the Crawl4AI client lifecycle with singleton pattern to prevent reinitialization.
    Enhanced with robust connection handling and error recovery.
    
    Args:
        server: The FastMCP server instance
        
    Yields:
        Crawl4AIContext: The context containing the Crawl4AI crawler and Supabase client
    """
    global _global_crawler, _global_supabase_client, _global_reliable_crawler
    global _global_knowledge_validator, _global_repo_extractor, _global_job_manager
    global _initialization_lock, _server_initialized, _cached_reranking_model
    
    # Initialize lock if needed
    if _initialization_lock is None:
        _initialization_lock = asyncio.Lock()
    
    # Wait for server to be fully initialized with timeout protection
    max_wait_time = 30  # seconds
    start_time = asyncio.get_event_loop().time()
    
    while not _server_initialized:
        # Check if we've been waiting too long
        if asyncio.get_event_loop().time() - start_time > max_wait_time:
            raise RuntimeError(f"Server initialization timeout after {max_wait_time} seconds")
        
        try:
            async with asyncio.wait_for(_initialization_lock.acquire(), timeout=5.0):
                try:
                    if not _server_initialized:
                        print("🔄 Starting MCP server initialization...")
                        await _initialize_global_resources()
                        print("✅ MCP server initialization complete - ready to accept requests")
                    break
                finally:
                    _initialization_lock.release()
        except asyncio.TimeoutError:
            print("⚠️ Waiting for server initialization lock...")
            await asyncio.sleep(0.5)
            continue
        except Exception as e:
            print(f"❌ Error during server initialization: {e}")
            await asyncio.sleep(1.0)
            continue
    
    # Add connection health check
    if not all([_global_crawler, _global_supabase_client]):
        raise RuntimeError("Critical components not properly initialized")
    
    try:
        yield Crawl4AIContext(
            crawler=_global_crawler,
            supabase_client=_global_supabase_client,
            reranking_model=_cached_reranking_model,
            knowledge_validator=_global_knowledge_validator,
            repo_extractor=_global_repo_extractor,
            reliable_crawler=_global_reliable_crawler,
            job_manager=_global_job_manager
        )
    finally:
        # Keep resources alive for next request - don't cleanup here
        # Resources will be cleaned up when server shuts down
        pass

# Connection error handler for graceful handling of broken connections
class ConnectionErrorHandler:
    """Middleware to handle broken connections and initialization race conditions gracefully."""
    
    @staticmethod
    async def handle_connection_errors(func):
        """Wrapper to handle connection-related errors gracefully."""
        try:
            return await func()
        except Exception as e:
            error_str = str(e).lower()
            error_type = type(e).__name__
            
            # Handle broken connection errors gracefully
            if any(keyword in error_str for keyword in [
                'brokenresourceerror', 'connection', 'broken', 'closed', 'disconnected'
            ]) or error_type in ['BrokenResourceError', 'ConnectionResetError']:
                print(f"⚠️ Client connection issue (handled gracefully): {error_type}")
                return None  # Graceful handling
            
            # Handle initialization race conditions
            elif "received request before initialization" in error_str:
                print(f"⚠️ Initialization race condition detected, retrying...")
                await asyncio.sleep(0.5)  # Brief delay before retry
                raise e  # Let MCP framework handle retry
            
            else:
                # Re-raise other errors
                raise e

# Initialize FastMCP server with proper dependency injection
mcp = FastMCP(
    "mcp-crawl4ai-rag",
    description="MCP server for RAG and web crawling with Crawl4AI",
    lifespan=crawl4ai_lifespan,
    dependencies=[get_resources],  # Register the dependency function
    host=os.getenv("HOST", "0.0.0.0"),
    port=os.getenv("PORT", "8051")
)

# FastMCP-compatible dashboard imports
from starlette.requests import Request
from starlette.responses import HTMLResponse, JSONResponse, StreamingResponse
from starlette.templating import Jinja2Templates
from starlette.datastructures import FormData
import json
import uuid
import time
from typing import Optional
from dataclasses import dataclass, asdict

# Setup Jinja2 templates for dashboard
templates = Jinja2Templates(directory=str(Path(__file__).parent / "templates"))
import time
import uuid
import json
from typing import Optional
from dataclasses import dataclass, asdict

@dataclass
class BenchmarkTimer:
    """Timer for tracking benchmark performance metrics"""
    test_start: Optional[float] = None
    crawl_start: Optional[float] = None
    crawl_end: Optional[float] = None
    embedding_start: Optional[float] = None
    embedding_end: Optional[float] = None
    indexing_start: Optional[float] = None
    indexing_end: Optional[float] = None
    test_end: Optional[float] = None
    
    def start_test(self):
        self.test_start = time.time()
    
    def start_crawl(self):
        self.crawl_start = time.time()
    
    def end_crawl(self):
        self.crawl_end = time.time()
    
    def start_embedding(self):
        self.embedding_start = time.time()
    
    def end_embedding(self):
        self.embedding_end = time.time()
    
    def start_indexing(self):
        self.indexing_start = time.time()
    
    def end_indexing(self):
        self.indexing_end = time.time()
    
    def end_test(self):
        self.test_end = time.time()
    
    def calculate_durations(self) -> Dict[str, float]:
        """Calculate duration metrics from timestamps"""
        return {
            'crawl_duration_seconds': (self.crawl_end - self.crawl_start) if self.crawl_end and self.crawl_start else 0,
            'embedding_duration_seconds': (self.embedding_end - self.embedding_start) if self.embedding_end and self.embedding_start else 0,
            'total_duration_seconds': (self.test_end - self.test_start) if self.test_end and self.test_start else 0,
            'indexing_duration_seconds': (self.indexing_end - self.indexing_start) if self.indexing_end and self.indexing_start else 0
        }

# Global benchmark state tracking
_active_benchmarks: Dict[str, Dict] = {}

# Dashboard API Routes using FastMCP custom_route decorator
@mcp.custom_route("/dashboard", methods=["GET"])
async def dashboard(request: Request) -> HTMLResponse:
    """Main dashboard page with HTMX integration"""
    return templates.TemplateResponse("dashboard.html", {"request": request})

@mcp.custom_route("/api/benchmark/start", methods=["POST"])
async def start_benchmark(request: Request) -> JSONResponse:
    """Start new benchmark test run"""
    try:
        form = await request.form()
        url = form.get("url")
        embedding_model = form.get("embedding_model")
        test_name = form.get("test_name")
        
        if not all([url, embedding_model, test_name]):
            return JSONResponse({
                "success": False,
                "error": "Missing required fields: url, embedding_model, test_name"
            }, status_code=400)
        
        test_run_id = str(uuid.uuid4())
        
        # Initialize benchmark state
        benchmark_state = {
            "test_run_id": test_run_id,
            "test_name": test_name,
            "source_url": url,
            "embedding_model": embedding_model,
            "status": "running",
            "progress": 0,
            "current_step": "Initializing",
            "timer": BenchmarkTimer(),
            "started_at": time.time()
        }
        
        _active_benchmarks[test_run_id] = benchmark_state
        
        # Start the benchmark in background
        import asyncio
        asyncio.create_task(_run_benchmark_async(test_run_id, url, embedding_model, test_name))
        
        return JSONResponse({
            "success": True,
            "test_run_id": test_run_id,
            "message": "Benchmark started successfully"
        })
        
    except Exception as e:
        return JSONResponse({
            "success": False,
            "error": str(e)
        }, status_code=500)

@mcp.custom_route("/api/benchmark/status/{test_run_id}", methods=["GET"])
async def benchmark_status(request: Request) -> StreamingResponse:
    """SSE endpoint for real-time progress updates"""
    test_run_id = request.path_params.get("test_run_id")
    
    async def event_generator():
        while True:
            try:
                if test_run_id not in _active_benchmarks:
                    yield f"data: {json.dumps({'error': 'Test run not found'})}\n\n"
                    break
                
                status = _active_benchmarks[test_run_id]
                
                if status['status'] == 'completed':
                    yield f"data: {json.dumps({'progress': 100, 'message': 'Test completed', 'status': 'completed'})}\n\n"
                    break
                elif status['status'] == 'failed':
                    yield f"data: {json.dumps({'progress': 0, 'message': 'Test failed', 'status': 'failed', 'error': status.get('error', 'Unknown error')})}\n\n"
                    break
                
                progress_data = {
                    'progress': status['progress'],
                    'message': status['current_step'],
                    'status': status['status']
                }
                yield f"data: {json.dumps(progress_data)}\n\n"
                
                await asyncio.sleep(1)  # Poll every second
                
            except Exception as e:
                yield f"data: {json.dumps({'error': str(e)})}\n\n"
                break
    
    return StreamingResponse(
        event_generator(), 
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Cache-Control"
        }
    )

@mcp.custom_route("/api/benchmark/results/{test_run_id}", methods=["GET"])
async def benchmark_results(request: Request) -> JSONResponse:
    """Get benchmark results and comparison data"""
    test_run_id = request.path_params.get("test_run_id")
    
    try:
        # Query evaluation results from database
        supabase = get_supabase_client()
        
        # Get test run data
        test_run_response = supabase.table("eval_test_runs").select("*").eq("id", test_run_id).execute()
        if not test_run_response.data:
            return JSONResponse({"success": False, "error": "Test run not found"}, status_code=404)
        
        test_run = test_run_response.data[0]
        
        # Get search performance data
        search_results_response = supabase.table("eval_search_results").select("*").eq("test_run_id", test_run_id).execute()
        search_results = search_results_response.data if search_results_response.data else []
        
        # Calculate performance metrics
        metrics = _calculate_performance_metrics(test_run, search_results)
        
        return JSONResponse({
            "success": True,
            "test_run": test_run,
            "search_results": search_results,
            "metrics": metrics
        })
        
    except Exception as e:
        return JSONResponse({
            "success": False,
            "error": str(e)
        }, status_code=500)

@mcp.custom_route("/api/benchmark/compare", methods=["GET"])
async def compare_benchmarks(request: Request) -> JSONResponse:
    """Compare two benchmark results side-by-side"""
    query_params = request.query_params
    test_run_1 = query_params.get("test_run_1")
    test_run_2 = query_params.get("test_run_2")
    
    if not test_run_1 or not test_run_2:
        return JSONResponse({
            "success": False,
            "error": "Missing required query parameters: test_run_1, test_run_2"
        }, status_code=400)
    
    try:
        supabase = get_supabase_client()
        
        # Get both test runs
        test_runs_response = supabase.table("eval_test_runs").select("*").in_("id", [test_run_1, test_run_2]).execute()
        if not test_runs_response.data or len(test_runs_response.data) != 2:
            return JSONResponse({"success": False, "error": "One or both test runs not found"}, status_code=404)
        
        test_runs = {run['id']: run for run in test_runs_response.data}
        
        # Get search results for both
        search_results_response = supabase.table("eval_search_results").select("*").in_("test_run_id", [test_run_1, test_run_2]).execute()
        search_results = {}
        for result in search_results_response.data or []:
            run_id = result['test_run_id']
            if run_id not in search_results:
                search_results[run_id] = []
            search_results[run_id].append(result)
        
        # Calculate metrics for both
        metrics_1 = _calculate_performance_metrics(test_runs[test_run_1], search_results.get(test_run_1, []))
        metrics_2 = _calculate_performance_metrics(test_runs[test_run_2], search_results.get(test_run_2, []))
        
        # Calculate differences
        comparison = _calculate_comparison_metrics(metrics_1, metrics_2)
        
        return JSONResponse({
            "success": True,
            "test_run_1": {
                "data": test_runs[test_run_1],
                "metrics": metrics_1
            },
            "test_run_2": {
                "data": test_runs[test_run_2],
                "metrics": metrics_2
            },
            "comparison": comparison
        })
        
    except Exception as e:
        return JSONResponse({
            "success": False,
            "error": str(e)
        }, status_code=500)

async def _run_benchmark_async(test_run_id: str, url: str, embedding_model: str, test_name: str):
    """Run the benchmark asynchronously"""
    try:
        state = _active_benchmarks[test_run_id]
        timer = state["timer"]
        timer.start_test()
        
        # Set embedding strategy based on model choice
        original_strategy = os.getenv("EMBEDDING_STRATEGY")
        os.environ["EMBEDDING_STRATEGY"] = embedding_model
        
        # Update progress
        state["current_step"] = "Starting crawl"
        state["progress"] = 10
        
        # Start crawling
        timer.start_crawl()
        state["current_step"] = "Crawling pages"
        state["progress"] = 20
        
        # Use the existing smart_crawl_url function
        # Create proper Context instance with keyword arguments
        mock_ctx = Context(request_context=None, fastmcp=None)
        crawl_result = await smart_crawl_url(
            mock_ctx,  # Proper Context instance
            url=url,
            max_depth=2,
            max_concurrent=5,
            chunk_size=5000,
            use_async_job=False
        )
        
        timer.end_crawl()
        state["current_step"] = "Processing embeddings"
        state["progress"] = 60
        
        # Insert test run into database
        timer.start_embedding()
        await _insert_benchmark_data(test_run_id, test_name, url, embedding_model, timer)
        timer.end_embedding()
        
        state["current_step"] = "Generating test queries"
        state["progress"] = 80
        
        # Generate and run test queries
        await _generate_and_run_test_queries(test_run_id)
        
        state["current_step"] = "Completed"
        state["progress"] = 100
        state["status"] = "completed"
        timer.end_test()
        
        # Update final timing in database
        await _update_benchmark_timing(test_run_id, timer)
        
        # Restore original embedding strategy
        if original_strategy:
            os.environ["EMBEDDING_STRATEGY"] = original_strategy
        
    except Exception as e:
        state["status"] = "failed"
        state["error"] = str(e)
        print(f"Benchmark failed: {e}")

async def _insert_benchmark_data(test_run_id: str, test_name: str, url: str, embedding_model: str, timer: BenchmarkTimer):
    """Insert benchmark test run data into eval tables"""
    supabase = get_supabase_client()
    
    # Determine vector dimensions based on model
    vector_dims = 768 if embedding_model == "ollama" else 1536
    embedding_strategy = f"nomic-embed-text" if embedding_model == "ollama" else "text-embedding-3-small"
    
    # Insert test run record
    test_run_data = {
        "id": test_run_id,
        "test_name": test_name,
        "source_url": url,
        "embedding_model": embedding_model,
        "embedding_strategy": embedding_strategy,
        "vector_dimensions": vector_dims,
        "status": "running"
    }
    
    supabase.table("eval_test_runs").insert(test_run_data).execute()

async def _update_benchmark_timing(test_run_id: str, timer: BenchmarkTimer):
    """Update benchmark timing in database"""
    supabase = get_supabase_client()
    durations = timer.calculate_durations()
    
    update_data = {
        "crawl_duration_seconds": durations["crawl_duration_seconds"],
        "embedding_duration_seconds": durations["embedding_duration_seconds"],
        "total_duration_seconds": durations["total_duration_seconds"],
        "completed_at": "now()",
        "status": "completed"
    }
    
    supabase.table("eval_test_runs").update(update_data).eq("id", test_run_id).execute()

async def _generate_and_run_test_queries(test_run_id: str):
    """Generate test queries using LLM and execute them for performance testing"""
    # This would implement the query generation and search performance testing
    # For now, we'll add placeholder queries
    supabase = get_supabase_client()
    
    sample_queries = [
        {"query_text": "How to get started?", "query_type": "getting_started"},
        {"query_text": "What are the main features?", "query_type": "feature_overview"},
        {"query_text": "Installation and setup guide", "query_type": "installation"},
        {"query_text": "Common troubleshooting issues", "query_type": "troubleshooting"},
        {"query_text": "API reference and examples", "query_type": "api_reference"}
    ]
    
    for query_data in sample_queries:
        # Insert query
        query_response = supabase.table("eval_test_queries").insert({
            "test_run_id": test_run_id,
            "query_text": query_data["query_text"],
            "query_type": query_data["query_type"]
        }).execute()
        
        if query_response.data:
            query_id = query_response.data[0]["id"]
            
            # Execute search and measure performance
            start_time = time.time()
            # Mock search execution - in real implementation, this would call the search function
            search_duration_ms = (time.time() - start_time) * 1000
            
            # Insert search result metrics
            supabase.table("eval_search_results").insert({
                "test_run_id": test_run_id,
                "query_id": query_id,
                "search_duration_ms": search_duration_ms,
                "results_count": 5,  # Mock data
                "top_similarity_score": 0.85,  # Mock data
                "avg_similarity_score": 0.72   # Mock data
            }).execute()

def _calculate_performance_metrics(test_run: Dict, search_results: List[Dict]) -> Dict:
    """Calculate performance metrics from test run and search results"""
    if not search_results:
        return {
            "avg_search_duration_ms": 0,
            "avg_similarity_score": 0,
            "total_queries": 0
        }
    
    avg_search_duration = sum(r.get("search_duration_ms", 0) for r in search_results) / len(search_results)
    avg_similarity = sum(r.get("avg_similarity_score", 0) for r in search_results) / len(search_results)
    
    return {
        "avg_search_duration_ms": round(avg_search_duration, 2),
        "avg_similarity_score": round(avg_similarity, 4),
        "total_queries": len(search_results),
        "crawl_duration_seconds": test_run.get("crawl_duration_seconds", 0),
        "embedding_duration_seconds": test_run.get("embedding_duration_seconds", 0),
        "total_duration_seconds": test_run.get("total_duration_seconds", 0)
    }

def _calculate_comparison_metrics(metrics_1: Dict, metrics_2: Dict) -> Dict:
    """Calculate comparison between two sets of metrics"""
    def safe_divide(a, b):
        return (a / b - 1) * 100 if b != 0 else 0
    
    return {
        "search_speed_diff_percent": safe_divide(metrics_2["avg_search_duration_ms"], metrics_1["avg_search_duration_ms"]),
        "similarity_diff_percent": safe_divide(metrics_2["avg_similarity_score"], metrics_1["avg_similarity_score"]),
        "crawl_speed_diff_percent": safe_divide(metrics_2["crawl_duration_seconds"], metrics_1["crawl_duration_seconds"]),
        "embedding_speed_diff_percent": safe_divide(metrics_2["embedding_duration_seconds"], metrics_1["embedding_duration_seconds"]),
        "total_speed_diff_percent": safe_divide(metrics_2["total_duration_seconds"], metrics_1["total_duration_seconds"])
    }

def rerank_results(model: CrossEncoder, query: str, results: List[Dict[str, Any]], content_key: str = "content") -> List[Dict[str, Any]]:
    """
    Rerank search results using a cross-encoder model.
    
    Args:
        model: The cross-encoder model to use for reranking
        query: The search query
        results: List of search results
        content_key: The key in each result dict that contains the text content
        
    Returns:
        Reranked list of results
    """
    if not model or not results:
        return results
    
    try:
        # Extract content from results
        texts = [result.get(content_key, "") for result in results]
        
        # Create pairs of [query, document] for the cross-encoder
        pairs = [[query, text] for text in texts]
        
        # Get relevance scores from the cross-encoder
        scores = model.predict(pairs)
        
        # Add scores to results and sort by score (descending)
        for i, result in enumerate(results):
            result["rerank_score"] = float(scores[i])
        
        # Sort by rerank score
        reranked = sorted(results, key=lambda x: x.get("rerank_score", 0), reverse=True)
        
        return reranked
    except Exception as e:
        print(f"Error during reranking: {e}")
        return results

def is_sitemap(url: str) -> bool:
    """
    Check if a URL is a sitemap.
    
    Args:
        url: URL to check
        
    Returns:
        True if the URL is a sitemap, False otherwise
    """
    return url.endswith('sitemap.xml') or 'sitemap' in urlparse(url).path

def is_txt(url: str) -> bool:
    """
    Check if a URL is a text file.
    
    Args:
        url: URL to check
        
    Returns:
        True if the URL is a text file, False otherwise
    """
    return url.endswith('.txt')

def parse_sitemap(sitemap_url: str) -> List[str]:
    """
    Parse a sitemap and extract URLs.
    
    Args:
        sitemap_url: URL of the sitemap
        
    Returns:
        List of URLs found in the sitemap
    """
    resp = requests.get(sitemap_url)
    urls = []

    if resp.status_code == 200:
        try:
            tree = ElementTree.fromstring(resp.content)
            urls = [loc.text for loc in tree.findall('.//{*}loc')]
        except Exception as e:
            print(f"Error parsing sitemap XML: {e}")

    return urls

def smart_chunk_markdown(text: str, chunk_size: int = 5000) -> List[str]:
    """Split text into chunks, respecting code blocks and paragraphs."""
    chunks = []
    start = 0
    text_length = len(text)

    while start < text_length:
        # Calculate end position
        end = start + chunk_size

        # If we're at the end of the text, just take what's left
        if end >= text_length:
            chunks.append(text[start:].strip())
            break

        # Try to find a code block boundary first (```)
        chunk = text[start:end]
        code_block = chunk.rfind('```')
        if code_block != -1 and code_block > chunk_size * 0.3:
            end = start + code_block

        # If no code block, try to break at a paragraph
        elif '\n\n' in chunk:
            # Find the last paragraph break
            last_break = chunk.rfind('\n\n')
            if last_break > chunk_size * 0.3:  # Only break if we're past 30% of chunk_size
                end = start + last_break

        # If no paragraph break, try to break at a sentence
        elif '. ' in chunk:
            # Find the last sentence break
            last_period = chunk.rfind('. ')
            if last_period > chunk_size * 0.3:  # Only break if we're past 30% of chunk_size
                end = start + last_period + 1

        # Extract chunk and clean it up
        chunk = text[start:end].strip()
        if chunk:
            chunks.append(chunk)

        # Move start position for next chunk
        start = end

    return chunks

def extract_section_info(chunk: str) -> Dict[str, Any]:
    """
    Extracts headers and stats from a chunk.
    
    Args:
        chunk: Markdown chunk
        
    Returns:
        Dictionary with headers and stats
    """
    headers = re.findall(r'^(#+)\s+(.+)$', chunk, re.MULTILINE)
    header_str = '; '.join([f'{h[0]} {h[1]}' for h in headers]) if headers else ''

    return {
        "headers": header_str,
        "char_count": len(chunk),
        "word_count": len(chunk.split())
    }

def process_code_example(args):
    """
    Process a single code example to generate its summary.
    This function is designed to be used with concurrent.futures.
    
    Args:
        args: Tuple containing (code, context_before, context_after)
        
    Returns:
        The generated summary
    """
    code, context_before, context_after = args
    return generate_code_example_summary(code, context_before, context_after)

@mcp.tool()
async def crawl_single_page(url: str, resources: Crawl4AIContext = get_resources) -> str:
    """
    Crawl a single web page and store its content in Supabase.
    
    This tool is ideal for quickly retrieving content from a specific URL without following links.
    The content is stored in Supabase for later retrieval and querying.
    
    Args:
        ctx: The MCP server provided context
        url: URL of the web page to crawl
    
    Returns:
        Summary of the crawling operation and storage in Supabase
    """
    try:
        # Initialize variables at function start
        chunks = []
        code_examples = []
        code_blocks = None
        
        # Use the injected resources
        crawler = resources.crawler
        supabase_client = resources.supabase_client
        
        # Configure the crawl
        run_config = CrawlerRunConfig(cache_mode=CacheMode.BYPASS, stream=False)
        
        # Crawl the page
        result = await crawler.arun(url=url, config=run_config)
        
        if result.success and result.markdown:
            # Extract source_id
            parsed_url = urlparse(url)
            source_id = parsed_url.netloc or parsed_url.path
            
            # Chunk the content
            chunks = smart_chunk_markdown(result.markdown)
            
            # Prepare data for Supabase
            urls = []
            chunk_numbers = []
            contents = []
            metadatas = []
            total_word_count = 0
            
            for i, chunk in enumerate(chunks):
                urls.append(url)
                chunk_numbers.append(i)
                contents.append(chunk)
                
                # Extract metadata
                meta = extract_section_info(chunk)
                meta["chunk_index"] = i
                meta["url"] = url
                meta["source"] = source_id
                # Safe async task context handling
                try:
                    task = asyncio.current_task()
                    meta["crawl_time"] = task.get_name() if task else "unknown_task"
                except (RuntimeError, AttributeError):
                    meta["crawl_time"] = "sync_context"
                metadatas.append(meta)
                
                # Accumulate word count
                total_word_count += meta.get("word_count", 0)
            
            # Create url_to_full_document mapping
            url_to_full_document = {url: result.markdown}
            
            # Update source information FIRST (before inserting documents)
            source_summary = extract_source_summary(source_id, result.markdown[:5000])  # Use first 5000 chars for summary
            update_source_info(supabase_client, source_id, source_summary, total_word_count)
            
            # Add documentation chunks to Supabase (AFTER source exists)
            add_documents_to_supabase(supabase_client, urls, chunk_numbers, contents, metadatas, url_to_full_document)
            
            # Store counts before clearing memory
            chunks_count = len(chunks)
            
            # Clear large data structures to free memory
            url_to_full_document.clear()
            del contents, metadatas, chunks
            gc.collect()
            
            # Extract and process code examples only if enabled
            extract_code_examples = os.getenv("USE_AGENTIC_RAG", "false") == "true"
            if extract_code_examples:
                code_blocks = extract_code_blocks(result.markdown)
                if code_blocks:
                    code_urls = []
                    code_chunk_numbers = []
                    code_examples = []
                    code_summaries = []
                    code_metadatas = []
                    
                    # Process code examples in parallel
                    with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
                        # Prepare arguments for parallel processing
                        summary_args = [(block['code'], block['context_before'], block['context_after']) 
                                        for block in code_blocks]
                        
                        # Generate summaries in parallel
                        summaries = list(executor.map(process_code_example, summary_args))
                    
                    # Prepare code example data
                    for i, (block, summary) in enumerate(zip(code_blocks, summaries)):
                        code_urls.append(url)
                        code_chunk_numbers.append(i)
                        code_examples.append(block['code'])
                        code_summaries.append(summary)
                        
                        # Create metadata for code example
                        code_meta = {
                            "chunk_index": i,
                            "url": url,
                            "source": source_id,
                            "char_count": len(block['code']),
                            "word_count": len(block['code'].split())
                        }
                        code_metadatas.append(code_meta)
                    
                    # Add code examples to Supabase
                    add_code_examples_to_supabase(
                        supabase_client, 
                        code_urls, 
                        code_chunk_numbers, 
                        code_examples, 
                        code_summaries, 
                        code_metadatas
                    )
            
            return json.dumps({
                "success": True,
                "url": url,
                "chunks_stored": chunks_count,
                "code_examples_stored": len(code_blocks) if code_blocks else 0,
                "content_length": len(result.markdown),
                "total_word_count": total_word_count,
                "source_id": source_id,
                "links_count": {
                    "internal": len(result.links.get("internal", [])),
                    "external": len(result.links.get("external", []))
                }
            }, indent=2)
        else:
            return create_standard_error_response(
                error=result.error_message,
                context=f"crawl_single_page failed for URL: {url}",
                error_type="crawl_error",
                details={"url": url}
            )
    except Exception as e:
        return create_standard_error_response(
            error=str(e),
            context=f"crawl_single_page exception for URL: {url}",
            error_type="system_error",
            details={"url": url}
        )

@mcp.tool()
async def smart_crawl_url(url: str, max_depth: int = 3, max_concurrent: int = 10, chunk_size: int = 5000, use_async_job: bool = False, resources: Crawl4AIContext = get_resources) -> str:
    """
    Intelligently crawl a URL based on its type and store content in Supabase.
    
    This tool automatically detects the URL type and applies the appropriate crawling method:
    - For sitemaps: Extracts and crawls all URLs in parallel
    - For text files (llms.txt): Directly retrieves the content
    - For regular webpages: Recursively crawls internal links up to the specified depth
    
    All crawled content is chunked and stored in Supabase for later retrieval and querying.
    
    Args:
        ctx: The MCP server provided context
        url: URL to crawl (can be a regular webpage, sitemap.xml, or .txt file)
        max_depth: Maximum recursion depth for regular URLs (default: 3)
        max_concurrent: Maximum number of concurrent browser sessions (default: 10)
        chunk_size: Maximum size of each content chunk in characters (default: 5000)
        use_async_job: Whether to submit as async job for long operations (default: False)
    
    Returns:
        JSON string with crawl summary and storage information, or job submission details if use_async_job=True
    """
    try:
        # Check if async job mode is requested and available
        if use_async_job:
            job_manager = resources.job_manager
            if job_manager:
                # Submit as async job
                crawler_config = {
                    "cache_mode": "BYPASS",
                    "max_depth": max_depth,
                    "max_concurrent": max_concurrent,
                    "chunk_size": chunk_size
                }
                
                job_id = await job_manager.submit_crawl_job(
                    urls=[url],
                    crawler_config=crawler_config
                )
                
                return json.dumps({
                    "success": True,
                    "mode": "async_job",
                    "job_id": job_id,
                    "message": f"Smart crawl job submitted for {url}. Use check_job_status with job_id '{job_id}' to monitor progress.",
                    "url": url,
                    "parameters": {
                        "max_depth": max_depth,
                        "max_concurrent": max_concurrent,
                        "chunk_size": chunk_size
                    }
                }, indent=2)
            else:
                return json.dumps({
                    "success": False,
                    "error": "Async job requested but job management not available. Set USE_JOB_MANAGEMENT=true to enable."
                }, indent=2)
        
        # Use the injected resources
        crawler = resources.crawler
        supabase_client = resources.supabase_client
        
        # Determine the crawl strategy
        crawl_results = []
        crawl_type = None
        
        if is_txt(url):
            # For text files, use simple crawl
            crawl_results = await crawl_markdown_file(crawler, url)
            crawl_type = "text_file"
        elif is_sitemap(url):
            # For sitemaps, extract URLs and crawl in parallel
            sitemap_urls = parse_sitemap(url)
            if not sitemap_urls:
                return json.dumps({
                    "success": False,
                    "url": url,
                    "error": "No URLs found in sitemap"
                }, indent=2)
            crawl_results = await crawl_batch(crawler, sitemap_urls, max_concurrent=max_concurrent)
            crawl_type = "sitemap"
        else:
            # For regular URLs, use recursive crawl
            crawl_results = await crawl_recursive_internal_links(crawler, [url], max_depth=max_depth, max_concurrent=max_concurrent)
            crawl_type = "webpage"
        
        if not crawl_results:
            return json.dumps({
                "success": False,
                "url": url,
                "error": "No content found"
            }, indent=2)
        
        # Process results and store in Supabase
        urls = []
        chunk_numbers = []
        contents = []
        metadatas = []
        chunk_count = 0
        
        # Track sources and their content
        source_content_map = {}
        source_word_counts = {}
        
        # Process documentation chunks
        for doc in crawl_results:
            source_url = doc['url']
            md = doc['markdown']
            chunks = smart_chunk_markdown(md, chunk_size=chunk_size)
            
            # Extract source_id
            parsed_url = urlparse(source_url)
            source_id = parsed_url.netloc or parsed_url.path
            
            # Store content for source summary generation
            if source_id not in source_content_map:
                source_content_map[source_id] = md[:5000]  # Store first 5000 chars
                source_word_counts[source_id] = 0
            
            for i, chunk in enumerate(chunks):
                urls.append(source_url)
                chunk_numbers.append(i)
                contents.append(chunk)
                
                # Extract metadata
                meta = extract_section_info(chunk)
                meta["chunk_index"] = i
                meta["url"] = source_url
                meta["source"] = source_id
                meta["crawl_type"] = crawl_type
                # Safe async task context handling
                try:
                    task = asyncio.current_task()
                    meta["crawl_time"] = task.get_name() if task else "unknown_task"
                except (RuntimeError, AttributeError):
                    meta["crawl_time"] = "sync_context"
                metadatas.append(meta)
                
                # Accumulate word count
                source_word_counts[source_id] += meta.get("word_count", 0)
                
                chunk_count += 1
        
        # Create url_to_full_document mapping
        url_to_full_document = {}
        for doc in crawl_results:
            url_to_full_document[doc['url']] = doc['markdown']
        
        # Update source information for each unique source FIRST (before inserting documents)
        with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
            source_summary_args = [(source_id, content) for source_id, content in source_content_map.items()]
            source_summaries = list(executor.map(lambda args: extract_source_summary(args[0], args[1]), source_summary_args))
        
        for (source_id, _), summary in zip(source_summary_args, source_summaries):
            word_count = source_word_counts.get(source_id, 0)
            update_source_info(supabase_client, source_id, summary, word_count)
        
        # Add documentation chunks to Supabase (AFTER sources exist)
        batch_size = 20
        add_documents_to_supabase(supabase_client, urls, chunk_numbers, contents, metadatas, url_to_full_document, batch_size=batch_size)
        
        # Clear large data structures to free memory
        url_to_full_document.clear()
        del contents, metadatas, urls, chunk_numbers
        gc.collect()
        
        # Extract and process code examples from all documents only if enabled
        extract_code_examples_enabled = os.getenv("USE_AGENTIC_RAG", "false") == "true"
        if extract_code_examples_enabled:
            all_code_blocks = []
            code_urls = []
            code_chunk_numbers = []
            code_examples = []
            code_summaries = []
            code_metadatas = []
            
            # Extract code blocks from all documents
            for doc in crawl_results:
                source_url = doc['url']
                md = doc['markdown']
                code_blocks = extract_code_blocks(md)
                
                if code_blocks:
                    # Process code examples in parallel
                    with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
                        # Prepare arguments for parallel processing
                        summary_args = [(block['code'], block['context_before'], block['context_after']) 
                                        for block in code_blocks]
                        
                        # Generate summaries in parallel
                        summaries = list(executor.map(process_code_example, summary_args))
                    
                    # Prepare code example data
                    parsed_url = urlparse(source_url)
                    source_id = parsed_url.netloc or parsed_url.path
                    
                    for i, (block, summary) in enumerate(zip(code_blocks, summaries)):
                        code_urls.append(source_url)
                        code_chunk_numbers.append(len(code_examples))  # Use global code example index
                        code_examples.append(block['code'])
                        code_summaries.append(summary)
                        
                        # Create metadata for code example
                        code_meta = {
                            "chunk_index": len(code_examples) - 1,
                            "url": source_url,
                            "source": source_id,
                            "char_count": len(block['code']),
                            "word_count": len(block['code'].split())
                        }
                        code_metadatas.append(code_meta)
            
            # Add all code examples to Supabase
            if code_examples:
                add_code_examples_to_supabase(
                    supabase_client, 
                    code_urls, 
                    code_chunk_numbers, 
                    code_examples, 
                    code_summaries, 
                    code_metadatas,
                    batch_size=batch_size
                )
        
        return json.dumps({
            "success": True,
            "url": url,
            "crawl_type": crawl_type,
            "pages_crawled": len(crawl_results),
            "chunks_stored": chunk_count,
            "code_examples_stored": len(code_examples),
            "sources_updated": len(source_content_map),
            "urls_crawled": [doc['url'] for doc in crawl_results][:5] + (["..."] if len(crawl_results) > 5 else [])
        }, indent=2)
    except Exception as e:
        return json.dumps({
            "success": False,
            "url": url,
            "error": str(e)
        }, indent=2)

@mcp.tool()
async def get_available_sources(resources: Crawl4AIContext = get_resources) -> str:
    """
    Get all available sources from the sources table.
    
    This tool returns a list of all unique sources (domains) that have been crawled and stored
    in the database, along with their summaries and statistics. This is useful for discovering 
    what content is available for querying.

    Always use this tool before calling the RAG query or code example query tool
    with a specific source filter!
    
    Args:
        ctx: The MCP server provided context
    
    Returns:
        JSON string with the list of available sources and their details
    """
    try:
        # Use the injected Supabase client
        supabase_client = resources.supabase_client
        
        # Query the sources table directly
        result = supabase_client.from_('sources')\
            .select('*')\
            .order('source_id')\
            .execute()
        
        # Format the sources with their details
        sources = []
        if result.data:
            for source in result.data:
                sources.append({
                    "source_id": source.get("source_id"),
                    "summary": source.get("summary"),
                    "total_words": source.get("total_words"),
                    "created_at": source.get("created_at"),
                    "updated_at": source.get("updated_at")
                })
        
        return json.dumps({
            "success": True,
            "sources": sources,
            "count": len(sources)
        }, indent=2)
    except Exception as e:
        return json.dumps({
            "success": False,
            "error": str(e)
        }, indent=2)

@mcp.tool()
async def perform_rag_query(query: str, source: str = None, match_count: int = 5, resources: Crawl4AIContext = get_resources) -> str:
    """
    Perform a RAG (Retrieval Augmented Generation) query on the stored content.
    
    This tool searches the vector database for content relevant to the query and returns
    the matching documents. Optionally filter by source domain.
    Get the source by using the get_available_sources tool before calling this search!
    
    Args:
        ctx: The MCP server provided context
        query: The search query
        source: Optional source domain to filter results (e.g., 'example.com')
        match_count: Maximum number of results to return (default: 5)
    
    Returns:
        JSON string with the search results
    """
    try:
        # Use the injected Supabase client
        supabase_client = resources.supabase_client
        
        # Check if hybrid search is enabled
        use_hybrid_search = os.getenv("USE_HYBRID_SEARCH", "false") == "true"
        
        # Prepare filter if source is provided and not empty
        filter_metadata = None
        if source and source.strip():
            filter_metadata = {"source": source}
        
        if use_hybrid_search:
            # Hybrid search: combine vector and keyword search
            
            # 1. Get vector search results (get more to account for filtering)
            vector_results = search_documents(
                client=supabase_client,
                query=query,
                match_count=match_count * 2,  # Get double to have room for filtering
                filter_metadata=filter_metadata
            )
            
            # 2. Get keyword search results using ILIKE
            keyword_query = supabase_client.from_('crawled_pages')\
                .select('id, url, chunk_number, content, metadata, source_id')\
                .ilike('content', f'%{query}%')
            
            # Apply source filter if provided
            if source and source.strip():
                keyword_query = keyword_query.eq('source_id', source)
            
            # Execute keyword search
            keyword_response = keyword_query.limit(match_count * 2).execute()
            keyword_results = keyword_response.data if keyword_response.data else []
            
            # 3. Combine results with preference for items appearing in both
            seen_ids = set()
            combined_results = []
            
            # First, add items that appear in both searches (these are the best matches)
            vector_ids = {r.get('id') for r in vector_results if r.get('id')}
            for kr in keyword_results:
                if kr['id'] in vector_ids and kr['id'] not in seen_ids:
                    # Find the vector result to get similarity score
                    for vr in vector_results:
                        if vr.get('id') == kr['id']:
                            # Boost similarity score for items in both results
                            vr['similarity'] = min(1.0, vr.get('similarity', 0) * 1.2)
                            combined_results.append(vr)
                            seen_ids.add(kr['id'])
                            break
            
            # Then add remaining vector results (semantic matches without exact keyword)
            for vr in vector_results:
                if vr.get('id') and vr['id'] not in seen_ids and len(combined_results) < match_count:
                    combined_results.append(vr)
                    seen_ids.add(vr['id'])
            
            # Finally, add pure keyword matches if we still need more results
            for kr in keyword_results:
                if kr['id'] not in seen_ids and len(combined_results) < match_count:
                    # Convert keyword result to match vector result format
                    combined_results.append({
                        'id': kr['id'],
                        'url': kr['url'],
                        'chunk_number': kr['chunk_number'],
                        'content': kr['content'],
                        'metadata': kr['metadata'],
                        'source_id': kr['source_id'],
                        'similarity': 0.5  # Default similarity for keyword-only matches
                    })
                    seen_ids.add(kr['id'])
            
            # Use combined results
            results = combined_results[:match_count]
            
        else:
            # Standard vector search only
            results = search_documents(
                client=supabase_client,
                query=query,
                match_count=match_count,
                filter_metadata=filter_metadata
            )
        
        # Apply reranking if enabled (using existing resources variable)
        use_reranking = os.getenv("USE_RERANKING", "false") == "true"
        if use_reranking and resources.reranking_model:
            results = rerank_results(resources.reranking_model, query, results, content_key="content")
        
        # Format the results
        formatted_results = []
        for result in results:
            formatted_result = {
                "url": result.get("url"),
                "content": result.get("content"),
                "metadata": result.get("metadata"),
                "similarity": result.get("similarity")
            }
            # Include rerank score if available
            if "rerank_score" in result:
                formatted_result["rerank_score"] = result["rerank_score"]
            formatted_results.append(formatted_result)
        
        return json.dumps({
            "success": True,
            "query": query,
            "source_filter": source,
            "search_mode": "hybrid" if use_hybrid_search else "vector",
            "reranking_applied": use_reranking and resources.reranking_model is not None,
            "results": formatted_results,
            "count": len(formatted_results)
        }, indent=2)
    except Exception as e:
        return create_standard_error_response(
            error=str(e),
            context=f"Query operation failed for: {query}",
            error_type="query_error",
            details={"query": query}
        )

@mcp.tool()
async def search_code_examples(query: str, source_id: str = None, match_count: int = 5, resources: Crawl4AIContext = get_resources) -> str:
    """
    Search for code examples relevant to the query.
    
    This tool searches the vector database for code examples relevant to the query and returns
    the matching examples with their summaries. Optionally filter by source_id.
    Get the source_id by using the get_available_sources tool before calling this search!

    Use the get_available_sources tool first to see what sources are available for filtering.
    
    Args:
        ctx: The MCP server provided context
        query: The search query
        source_id: Optional source ID to filter results (e.g., 'example.com')
        match_count: Maximum number of results to return (default: 5)
    
    Returns:
        JSON string with the search results
    """
    # Check if code example extraction is enabled
    extract_code_examples_enabled = os.getenv("USE_AGENTIC_RAG", "false") == "true"
    if not extract_code_examples_enabled:
        return json.dumps({
            "success": False,
            "error": "Code example extraction is disabled. Perform a normal RAG search."
        }, indent=2)
    
    try:
        # Use the injected Supabase client
        supabase_client = resources.supabase_client
        
        # Check if hybrid search is enabled
        use_hybrid_search = os.getenv("USE_HYBRID_SEARCH", "false") == "true"
        
        # Prepare filter if source is provided and not empty
        filter_metadata = None
        if source_id and source_id.strip():
            filter_metadata = {"source": source_id}
        
        if use_hybrid_search:
            # Hybrid search: combine vector and keyword search
            
            # Import the search function from utils
            from utils import search_code_examples as search_code_examples_impl
            
            # 1. Get vector search results (get more to account for filtering)
            vector_results = search_code_examples_impl(
                client=supabase_client,
                query=query,
                match_count=match_count * 2,  # Get double to have room for filtering
                filter_metadata=filter_metadata
            )
            
            # 2. Get keyword search results using ILIKE on both content and summary
            keyword_query = supabase_client.from_('code_examples')\
                .select('id, url, chunk_number, content, summary, metadata, source_id')\
                .or_(f'content.ilike.%{query}%,summary.ilike.%{query}%')
            
            # Apply source filter if provided
            if source_id and source_id.strip():
                keyword_query = keyword_query.eq('source_id', source_id)
            
            # Execute keyword search
            keyword_response = keyword_query.limit(match_count * 2).execute()
            keyword_results = keyword_response.data if keyword_response.data else []
            
            # 3. Combine results with preference for items appearing in both
            seen_ids = set()
            combined_results = []
            
            # First, add items that appear in both searches (these are the best matches)
            vector_ids = {r.get('id') for r in vector_results if r.get('id')}
            for kr in keyword_results:
                if kr['id'] in vector_ids and kr['id'] not in seen_ids:
                    # Find the vector result to get similarity score
                    for vr in vector_results:
                        if vr.get('id') == kr['id']:
                            # Boost similarity score for items in both results
                            vr['similarity'] = min(1.0, vr.get('similarity', 0) * 1.2)
                            combined_results.append(vr)
                            seen_ids.add(kr['id'])
                            break
            
            # Then add remaining vector results (semantic matches without exact keyword)
            for vr in vector_results:
                if vr.get('id') and vr['id'] not in seen_ids and len(combined_results) < match_count:
                    combined_results.append(vr)
                    seen_ids.add(vr['id'])
            
            # Finally, add pure keyword matches if we still need more results
            for kr in keyword_results:
                if kr['id'] not in seen_ids and len(combined_results) < match_count:
                    # Convert keyword result to match vector result format
                    combined_results.append({
                        'id': kr['id'],
                        'url': kr['url'],
                        'chunk_number': kr['chunk_number'],
                        'content': kr['content'],
                        'summary': kr['summary'],
                        'metadata': kr['metadata'],
                        'source_id': kr['source_id'],
                        'similarity': 0.5  # Default similarity for keyword-only matches
                    })
                    seen_ids.add(kr['id'])
            
            # Use combined results
            results = combined_results[:match_count]
            
        else:
            # Standard vector search only
            from utils import search_code_examples as search_code_examples_impl
            
            results = search_code_examples_impl(
                client=supabase_client,
                query=query,
                match_count=match_count,
                filter_metadata=filter_metadata
            )
        
        # Apply reranking if enabled (using existing resources variable)
        use_reranking = os.getenv("USE_RERANKING", "false") == "true"
        if use_reranking and resources.reranking_model:
            results = rerank_results(resources.reranking_model, query, results, content_key="content")
        
        # Format the results
        formatted_results = []
        for result in results:
            formatted_result = {
                "url": result.get("url"),
                "code": result.get("content"),
                "summary": result.get("summary"),
                "metadata": result.get("metadata"),
                "source_id": result.get("source_id"),
                "similarity": result.get("similarity")
            }
            # Include rerank score if available
            if "rerank_score" in result:
                formatted_result["rerank_score"] = result["rerank_score"]
            formatted_results.append(formatted_result)
        
        return json.dumps({
            "success": True,
            "query": query,
            "source_filter": source_id,
            "search_mode": "hybrid" if use_hybrid_search else "vector",
            "reranking_applied": use_reranking and resources.reranking_model is not None,
            "results": formatted_results,
            "count": len(formatted_results)
        }, indent=2)
    except Exception as e:
        return create_standard_error_response(
            error=str(e),
            context=f"Query operation failed for: {query}",
            error_type="query_error",
            details={"query": query}
        )

@mcp.tool()
async def check_ai_script_hallucinations(script_path: str, resources: Crawl4AIContext = get_resources) -> str:
    """
    Check an AI-generated Python script for hallucinations using the knowledge graph.
    
    This tool analyzes a Python script for potential AI hallucinations by validating
    imports, method calls, class instantiations, and function calls against a Neo4j
    knowledge graph containing real repository data.
    
    The tool performs comprehensive analysis including:
    - Import validation against known repositories
    - Method call validation on classes from the knowledge graph
    - Class instantiation parameter validation
    - Function call parameter validation
    - Attribute access validation
    
    Args:
        ctx: The MCP server provided context
        script_path: Absolute path to the Python script to analyze
    
    Returns:
        JSON string with hallucination detection results, confidence scores, and recommendations
    """
    try:
        # Check if knowledge graph functionality is enabled
        knowledge_graph_enabled = os.getenv("USE_KNOWLEDGE_GRAPH", "false") == "true"
        if not knowledge_graph_enabled:
            return json.dumps({
                "success": False,
                "error": "Knowledge graph functionality is disabled. Set USE_KNOWLEDGE_GRAPH=true in environment."
            }, indent=2)
        
        # Get the knowledge validator from context
        # Using injected resources
        knowledge_validator = resources.knowledge_validator
        
        if not knowledge_validator:
            return json.dumps({
                "success": False,
                "error": "Knowledge graph validator not available. Check Neo4j configuration in environment variables."
            }, indent=2)
        
        # Validate script path
        validation = validate_script_path(script_path)
        if not validation["valid"]:
            return json.dumps({
                "success": False,
                "script_path": script_path,
                "error": validation["error"]
            }, indent=2)
        
        # Step 1: Analyze script structure using AST
        analyzer = AIScriptAnalyzer()
        analysis_result = analyzer.analyze_script(script_path)
        
        if analysis_result.errors:
            print(f"Analysis warnings for {script_path}: {analysis_result.errors}")
        
        # Step 2: Validate against knowledge graph
        validation_result = await knowledge_validator.validate_script(analysis_result)
        
        # Step 3: Generate comprehensive report
        reporter = HallucinationReporter()
        report = reporter.generate_comprehensive_report(validation_result)
        
        # Format response with comprehensive information
        return json.dumps({
            "success": True,
            "script_path": script_path,
            "overall_confidence": validation_result.overall_confidence,
            "validation_summary": {
                "total_validations": report["validation_summary"]["total_validations"],
                "valid_count": report["validation_summary"]["valid_count"],
                "invalid_count": report["validation_summary"]["invalid_count"],
                "uncertain_count": report["validation_summary"]["uncertain_count"],
                "not_found_count": report["validation_summary"]["not_found_count"],
                "hallucination_rate": report["validation_summary"]["hallucination_rate"]
            },
            "hallucinations_detected": report["hallucinations_detected"],
            "recommendations": report["recommendations"],
            "analysis_metadata": {
                "total_imports": report["analysis_metadata"]["total_imports"],
                "total_classes": report["analysis_metadata"]["total_classes"],
                "total_methods": report["analysis_metadata"]["total_methods"],
                "total_attributes": report["analysis_metadata"]["total_attributes"],
                "total_functions": report["analysis_metadata"]["total_functions"]
            },
            "libraries_analyzed": report.get("libraries_analyzed", [])
        }, indent=2)
        
    except Exception as e:
        return json.dumps({
            "success": False,
            "script_path": script_path,
            "error": f"Analysis failed: {str(e)}"
        }, indent=2)

@mcp.tool()
async def query_knowledge_graph(command: str, resources: Crawl4AIContext = get_resources) -> str:
    """
    Query and explore the Neo4j knowledge graph containing repository data.
    
    This tool provides comprehensive access to the knowledge graph for exploring repositories,
    classes, methods, functions, and their relationships. Perfect for understanding what data
    is available for hallucination detection and debugging validation results.
    
    **⚠️ IMPORTANT: Always start with the `repos` command first!**
    Before using any other commands, run `repos` to see what repositories are available
    in your knowledge graph. This will help you understand what data you can explore.
    
    ## Available Commands:
    
    **Repository Commands:**
    - `repos` - **START HERE!** List all repositories in the knowledge graph
    - `explore <repo_name>` - Get detailed overview of a specific repository
    
    **Class Commands:**  
    - `classes` - List all classes across all repositories (limited to 20)
    - `classes <repo_name>` - List classes in a specific repository
    - `class <class_name>` - Get detailed information about a specific class including methods and attributes
    
    **Method Commands:**
    - `method <method_name>` - Search for methods by name across all classes
    - `method <method_name> <class_name>` - Search for a method within a specific class
    
    **Custom Query:**
    - `query <cypher_query>` - Execute a custom Cypher query (results limited to 20 records)
    
    ## Knowledge Graph Schema:
    
    **Node Types:**
    - Repository: `(r:Repository {name: string})`
    - File: `(f:File {path: string, module_name: string})`
    - Class: `(c:Class {name: string, full_name: string})`
    - Method: `(m:Method {name: string, params_list: [string], params_detailed: [string], return_type: string, args: [string]})`
    - Function: `(func:Function {name: string, params_list: [string], params_detailed: [string], return_type: string, args: [string]})`
    - Attribute: `(a:Attribute {name: string, type: string})`
    
    **Relationships:**
    - `(r:Repository)-[:CONTAINS]->(f:File)`
    - `(f:File)-[:DEFINES]->(c:Class)`
    - `(c:Class)-[:HAS_METHOD]->(m:Method)`
    - `(c:Class)-[:HAS_ATTRIBUTE]->(a:Attribute)`
    - `(f:File)-[:DEFINES]->(func:Function)`
    
    ## Example Workflow:
    ```
    1. repos                                    # See what repositories are available
    2. explore pydantic-ai                      # Explore a specific repository
    3. classes pydantic-ai                      # List classes in that repository
    4. class Agent                              # Explore the Agent class
    5. method run_stream                        # Search for run_stream method
    6. method __init__ Agent                    # Find Agent constructor
    7. query "MATCH (c:Class)-[:HAS_METHOD]->(m:Method) WHERE m.name = 'run' RETURN c.name, m.name LIMIT 5"
    ```
    
    Args:
        ctx: The MCP server provided context
        command: Command string to execute (see available commands above)
    
    Returns:
        JSON string with query results, statistics, and metadata
    """
    try:
        # Check if knowledge graph functionality is enabled
        knowledge_graph_enabled = os.getenv("USE_KNOWLEDGE_GRAPH", "false") == "true"
        if not knowledge_graph_enabled:
            return json.dumps({
                "success": False,
                "error": "Knowledge graph functionality is disabled. Set USE_KNOWLEDGE_GRAPH=true in environment."
            }, indent=2)
        
        # Get Neo4j driver from context
        # Using injected resources
        repo_extractor = resources.repo_extractor
        if not repo_extractor or not repo_extractor.driver:
            return json.dumps({
                "success": False,
                "error": "Neo4j connection not available. Check Neo4j configuration in environment variables."
            }, indent=2)
        
        # Parse command
        command = command.strip()
        if not command:
            return json.dumps({
                "success": False,
                "command": "",
                "error": "Command cannot be empty. Available commands: repos, explore <repo>, classes [repo], class <name>, method <name> [class], query <cypher>"
            }, indent=2)
        
        parts = command.split()
        cmd = parts[0].lower()
        args = parts[1:] if len(parts) > 1 else []
        
        async with repo_extractor.driver.session() as session:
            # Route to appropriate handler
            if cmd == "repos":
                return await _handle_repos_command(session, command)
            elif cmd == "explore":
                if not args:
                    return json.dumps({
                        "success": False,
                        "command": command,
                        "error": "Repository name required. Usage: explore <repo_name>"
                    }, indent=2)
                return await _handle_explore_command(session, command, args[0])
            elif cmd == "classes":
                repo_name = args[0] if args else None
                return await _handle_classes_command(session, command, repo_name)
            elif cmd == "class":
                if not args:
                    return json.dumps({
                        "success": False,
                        "command": command,
                        "error": "Class name required. Usage: class <class_name>"
                    }, indent=2)
                return await _handle_class_command(session, command, args[0])
            elif cmd == "method":
                if not args:
                    return json.dumps({
                        "success": False,
                        "command": command,
                        "error": "Method name required. Usage: method <method_name> [class_name]"
                    }, indent=2)
                method_name = args[0]
                class_name = args[1] if len(args) > 1 else None
                return await _handle_method_command(session, command, method_name, class_name)
            elif cmd == "query":
                if not args:
                    return json.dumps({
                        "success": False,
                        "command": command,
                        "error": "Cypher query required. Usage: query <cypher_query>"
                    }, indent=2)
                cypher_query = " ".join(args)
                return await _handle_query_command(session, command, cypher_query)
            else:
                return json.dumps({
                    "success": False,
                    "command": command,
                    "error": f"Unknown command '{cmd}'. Available commands: repos, explore <repo>, classes [repo], class <name>, method <name> [class], query <cypher>"
                }, indent=2)
                
    except Exception as e:
        return json.dumps({
            "success": False,
            "command": command,
            "error": f"Query execution failed: {str(e)}"
        }, indent=2)


async def _handle_repos_command(session, command: str) -> str:
    """Handle 'repos' command - list all repositories"""
    query = "MATCH (r:Repository) RETURN r.name as name ORDER BY r.name"
    result = await session.run(query)
    
    repos = []
    async for record in result:
        repos.append(record['name'])
    
    return json.dumps({
        "success": True,
        "command": command,
        "data": {
            "repositories": repos
        },
        "metadata": {
            "total_results": len(repos),
            "limited": False
        }
    }, indent=2)


async def _handle_explore_command(session, command: str, repo_name: str) -> str:
    """Handle 'explore <repo>' command - get repository overview"""
    # Check if repository exists
    repo_check_query = "MATCH (r:Repository {name: $repo_name}) RETURN r.name as name"
    result = await session.run(repo_check_query, repo_name=repo_name)
    repo_record = await result.single()
    
    if not repo_record:
        return json.dumps({
            "success": False,
            "command": command,
            "error": f"Repository '{repo_name}' not found in knowledge graph"
        }, indent=2)
    
    # Get file count
    files_query = """
    MATCH (r:Repository {name: $repo_name})-[:CONTAINS]->(f:File)
    RETURN count(f) as file_count
    """
    result = await session.run(files_query, repo_name=repo_name)
    file_count = (await result.single())['file_count']
    
    # Get class count
    classes_query = """
    MATCH (r:Repository {name: $repo_name})-[:CONTAINS]->(f:File)-[:DEFINES]->(c:Class)
    RETURN count(DISTINCT c) as class_count
    """
    result = await session.run(classes_query, repo_name=repo_name)
    class_count = (await result.single())['class_count']
    
    # Get function count
    functions_query = """
    MATCH (r:Repository {name: $repo_name})-[:CONTAINS]->(f:File)-[:DEFINES]->(func:Function)
    RETURN count(DISTINCT func) as function_count
    """
    result = await session.run(functions_query, repo_name=repo_name)
    function_count = (await result.single())['function_count']
    
    # Get method count
    methods_query = """
    MATCH (r:Repository {name: $repo_name})-[:CONTAINS]->(f:File)-[:DEFINES]->(c:Class)-[:HAS_METHOD]->(m:Method)
    RETURN count(DISTINCT m) as method_count
    """
    result = await session.run(methods_query, repo_name=repo_name)
    method_count = (await result.single())['method_count']
    
    return json.dumps({
        "success": True,
        "command": command,
        "data": {
            "repository": repo_name,
            "statistics": {
                "files": file_count,
                "classes": class_count,
                "functions": function_count,
                "methods": method_count
            }
        },
        "metadata": {
            "total_results": 1,
            "limited": False
        }
    }, indent=2)


async def _handle_classes_command(session, command: str, repo_name: str = None) -> str:
    """Handle 'classes [repo]' command - list classes"""
    limit = 20
    
    if repo_name:
        query = """
        MATCH (r:Repository {name: $repo_name})-[:CONTAINS]->(f:File)-[:DEFINES]->(c:Class)
        RETURN c.name as name, c.full_name as full_name
        ORDER BY c.name
        LIMIT $limit
        """
        result = await session.run(query, repo_name=repo_name, limit=limit)
    else:
        query = """
        MATCH (c:Class)
        RETURN c.name as name, c.full_name as full_name
        ORDER BY c.name
        LIMIT $limit
        """
        result = await session.run(query, limit=limit)
    
    classes = []
    async for record in result:
        classes.append({
            'name': record['name'],
            'full_name': record['full_name']
        })
    
    return json.dumps({
        "success": True,
        "command": command,
        "data": {
            "classes": classes,
            "repository_filter": repo_name
        },
        "metadata": {
            "total_results": len(classes),
            "limited": len(classes) >= limit
        }
    }, indent=2)


async def _handle_class_command(session, command: str, class_name: str) -> str:
    """Handle 'class <name>' command - explore specific class"""
    # Find the class
    class_query = """
    MATCH (c:Class)
    WHERE c.name = $class_name OR c.full_name = $class_name
    RETURN c.name as name, c.full_name as full_name
    LIMIT 1
    """
    result = await session.run(class_query, class_name=class_name)
    class_record = await result.single()
    
    if not class_record:
        return json.dumps({
            "success": False,
            "command": command,
            "error": f"Class '{class_name}' not found in knowledge graph"
        }, indent=2)
    
    actual_name = class_record['name']
    full_name = class_record['full_name']
    
    # Get methods
    methods_query = """
    MATCH (c:Class)-[:HAS_METHOD]->(m:Method)
    WHERE c.name = $class_name OR c.full_name = $class_name
    RETURN m.name as name, m.params_list as params_list, m.params_detailed as params_detailed, m.return_type as return_type
    ORDER BY m.name
    """
    result = await session.run(methods_query, class_name=class_name)
    
    methods = []
    async for record in result:
        # Use detailed params if available, fall back to simple params
        params_to_use = record['params_detailed'] or record['params_list'] or []
        methods.append({
            'name': record['name'],
            'parameters': params_to_use,
            'return_type': record['return_type'] or 'Any'
        })
    
    # Get attributes
    attributes_query = """
    MATCH (c:Class)-[:HAS_ATTRIBUTE]->(a:Attribute)
    WHERE c.name = $class_name OR c.full_name = $class_name
    RETURN a.name as name, a.type as type
    ORDER BY a.name
    """
    result = await session.run(attributes_query, class_name=class_name)
    
    attributes = []
    async for record in result:
        attributes.append({
            'name': record['name'],
            'type': record['type'] or 'Any'
        })
    
    return json.dumps({
        "success": True,
        "command": command,
        "data": {
            "class": {
                "name": actual_name,
                "full_name": full_name,
                "methods": methods,
                "attributes": attributes
            }
        },
        "metadata": {
            "total_results": 1,
            "methods_count": len(methods),
            "attributes_count": len(attributes),
            "limited": False
        }
    }, indent=2)


async def _handle_method_command(session, command: str, method_name: str, class_name: str = None) -> str:
    """Handle 'method <name> [class]' command - search for methods"""
    if class_name:
        query = """
        MATCH (c:Class)-[:HAS_METHOD]->(m:Method)
        WHERE (c.name = $class_name OR c.full_name = $class_name)
          AND m.name = $method_name
        RETURN c.name as class_name, c.full_name as class_full_name,
               m.name as method_name, m.params_list as params_list, 
               m.params_detailed as params_detailed, m.return_type as return_type, m.args as args
        """
        result = await session.run(query, class_name=class_name, method_name=method_name)
    else:
        query = """
        MATCH (c:Class)-[:HAS_METHOD]->(m:Method)
        WHERE m.name = $method_name
        RETURN c.name as class_name, c.full_name as class_full_name,
               m.name as method_name, m.params_list as params_list, 
               m.params_detailed as params_detailed, m.return_type as return_type, m.args as args
        ORDER BY c.name
        LIMIT 20
        """
        result = await session.run(query, method_name=method_name)
    
    methods = []
    async for record in result:
        # Use detailed params if available, fall back to simple params
        params_to_use = record['params_detailed'] or record['params_list'] or []
        methods.append({
            'class_name': record['class_name'],
            'class_full_name': record['class_full_name'],
            'method_name': record['method_name'],
            'parameters': params_to_use,
            'return_type': record['return_type'] or 'Any',
            'legacy_args': record['args'] or []
        })
    
    if not methods:
        return json.dumps({
            "success": False,
            "command": command,
            "error": f"Method '{method_name}'" + (f" in class '{class_name}'" if class_name else "") + " not found"
        }, indent=2)
    
    return json.dumps({
        "success": True,
        "command": command,
        "data": {
            "methods": methods,
            "class_filter": class_name
        },
        "metadata": {
            "total_results": len(methods),
            "limited": len(methods) >= 20 and not class_name
        }
    }, indent=2)


async def _handle_query_command(session, command: str, cypher_query: str) -> str:
    """Handle 'query <cypher>' command - execute custom Cypher query"""
    try:
        # Execute the query with a limit to prevent overwhelming responses
        result = await session.run(cypher_query)
        
        records = []
        count = 0
        async for record in result:
            records.append(dict(record))
            count += 1
            if count >= 20:  # Limit results to prevent overwhelming responses
                break
        
        return json.dumps({
            "success": True,
            "command": command,
            "data": {
                "query": cypher_query,
                "results": records
            },
            "metadata": {
                "total_results": len(records),
                "limited": len(records) >= 20
            }
        }, indent=2)
        
    except Exception as e:
        return json.dumps({
            "success": False,
            "command": command,
            "error": f"Cypher query error: {str(e)}",
            "data": {
                "query": cypher_query
            }
        }, indent=2)


@mcp.tool()
async def parse_github_repository(repo_url: str, resources: Crawl4AIContext = get_resources) -> str:
    """
    Parse a GitHub repository into the Neo4j knowledge graph.
    
    This tool clones a GitHub repository, analyzes its Python files, and stores
    the code structure (classes, methods, functions, imports) in Neo4j for use
    in hallucination detection. The tool:
    
    - Clones the repository to a temporary location
    - Analyzes Python files to extract code structure
    - Stores classes, methods, functions, and imports in Neo4j
    - Provides detailed statistics about the parsing results
    - Automatically handles module name detection for imports
    
    Args:
        ctx: The MCP server provided context
        repo_url: GitHub repository URL (e.g., 'https://github.com/user/repo.git')
    
    Returns:
        JSON string with parsing results, statistics, and repository information
    """
    try:
        # Check if knowledge graph functionality is enabled
        knowledge_graph_enabled = os.getenv("USE_KNOWLEDGE_GRAPH", "false") == "true"
        if not knowledge_graph_enabled:
            return json.dumps({
                "success": False,
                "error": "Knowledge graph functionality is disabled. Set USE_KNOWLEDGE_GRAPH=true in environment."
            }, indent=2)
        
        # Get the repository extractor from context
        # Using injected resources
        repo_extractor = resources.repo_extractor
        
        if not repo_extractor:
            return json.dumps({
                "success": False,
                "error": "Repository extractor not available. Check Neo4j configuration in environment variables."
            }, indent=2)
        
        # Validate repository URL
        validation = validate_github_url(repo_url)
        if not validation["valid"]:
            return json.dumps({
                "success": False,
                "repo_url": repo_url,
                "error": validation["error"]
            }, indent=2)
        
        repo_name = validation["repo_name"]
        
        # Parse the repository (this includes cloning, analysis, and Neo4j storage)
        print(f"Starting repository analysis for: {repo_name}")
        await repo_extractor.analyze_repository(repo_url)
        print(f"Repository analysis completed for: {repo_name}")
        
        # Query Neo4j for statistics about the parsed repository
        async with repo_extractor.driver.session() as session:
            # Get comprehensive repository statistics
            stats_query = """
            MATCH (r:Repository {name: $repo_name})
            OPTIONAL MATCH (r)-[:CONTAINS]->(f:File)
            OPTIONAL MATCH (f)-[:DEFINES]->(c:Class)
            OPTIONAL MATCH (c)-[:HAS_METHOD]->(m:Method)
            OPTIONAL MATCH (f)-[:DEFINES]->(func:Function)
            OPTIONAL MATCH (c)-[:HAS_ATTRIBUTE]->(a:Attribute)
            WITH r, 
                 count(DISTINCT f) as files_count,
                 count(DISTINCT c) as classes_count,
                 count(DISTINCT m) as methods_count,
                 count(DISTINCT func) as functions_count,
                 count(DISTINCT a) as attributes_count
            
            // Get some sample module names
            OPTIONAL MATCH (r)-[:CONTAINS]->(sample_f:File)
            WITH r, files_count, classes_count, methods_count, functions_count, attributes_count,
                 collect(DISTINCT sample_f.module_name)[0..5] as sample_modules
            
            RETURN 
                r.name as repo_name,
                files_count,
                classes_count, 
                methods_count,
                functions_count,
                attributes_count,
                sample_modules
            """
            
            result = await session.run(stats_query, repo_name=repo_name)
            record = await result.single()
            
            if record:
                stats = {
                    "repository": record['repo_name'],
                    "files_processed": record['files_count'],
                    "classes_created": record['classes_count'],
                    "methods_created": record['methods_count'], 
                    "functions_created": record['functions_count'],
                    "attributes_created": record['attributes_count'],
                    "sample_modules": record['sample_modules'] or []
                }
            else:
                return json.dumps({
                    "success": False,
                    "repo_url": repo_url,
                    "error": f"Repository '{repo_name}' not found in database after parsing"
                }, indent=2)
        
        return json.dumps({
            "success": True,
            "repo_url": repo_url,
            "repo_name": repo_name,
            "message": f"Successfully parsed repository '{repo_name}' into knowledge graph",
            "statistics": stats,
            "ready_for_validation": True,
            "next_steps": [
                "Repository is now available for hallucination detection",
                f"Use check_ai_script_hallucinations to validate scripts against {repo_name}",
                "The knowledge graph contains classes, methods, and functions from this repository"
            ]
        }, indent=2)
        
    except Exception as e:
        return json.dumps({
            "success": False,
            "repo_url": repo_url,
            "error": f"Repository parsing failed: {str(e)}"
        }, indent=2)

async def crawl_markdown_file(crawler: AsyncWebCrawler, url: str) -> List[Dict[str, Any]]:
    """
    Crawl a .txt or markdown file.
    
    Args:
        crawler: AsyncWebCrawler instance
        url: URL of the file
        
    Returns:
        List of dictionaries with URL and markdown content
    """
    crawl_config = CrawlerRunConfig()

    result = await crawler.arun(url=url, config=crawl_config)
    if result.success and result.markdown:
        return [{'url': url, 'markdown': result.markdown}]
    else:
        print(f"Failed to crawl {url}: {result.error_message}")
        return []

@mcp.tool()
async def get_reliability_metrics(resources: Crawl4AIContext = get_resources) -> str:
    """
    Get current reliability metrics for the crawling service.
    
    This tool returns comprehensive metrics including:
    - Total requests processed
    - Success and failure rates
    - Retry statistics
    - Error type distribution
    - Response time statistics
    - Circuit breaker status
    - Cache hit rates
    
    Args:
        ctx: The MCP server provided context
    
    Returns:
        JSON string with detailed reliability metrics
    """
    try:
        # Using injected resources
        reliable_crawler = resources.reliable_crawler
        
        if not reliable_crawler:
            return json.dumps({
                "success": False,
                "error": "Reliable crawler not available"
            }, indent=2)
        
        metrics = await reliable_crawler.get_metrics()
        
        # Add circuit breaker status
        circuit_breaker_status = {
            "state": reliable_crawler.circuit_breaker.state.value,
            "failure_count": reliable_crawler.circuit_breaker.failure_count,
            "success_count": reliable_crawler.circuit_breaker.success_count,
            "last_failure_time": reliable_crawler.circuit_breaker.last_failure_time
        }
        
        # Add cache statistics
        cache_stats = {
            "enabled": reliable_crawler.cache.config.enabled,
            "size": len(reliable_crawler.cache.cache),
            "max_size": reliable_crawler.cache.config.max_size,
            "ttl_seconds": reliable_crawler.cache.config.ttl_seconds
        }
        
        # Get job-level reliability metrics if available
        job_metrics = {}
        job_manager = resources.job_manager
        if job_manager:
            try:
                job_metrics = await job_manager.get_aggregate_reliability_metrics()
            except Exception as e:
                job_metrics = {"error": f"Failed to get job metrics: {str(e)}"}

        # Prepare response data and serialize datetime objects
        response_data = {
            "success": True,
            "metrics": metrics,
            "circuit_breaker": circuit_breaker_status,
            "cache": cache_stats,
            "job_reliability": job_metrics,  # Added job-level reliability metrics
            "configuration": {
                "max_retries": reliable_crawler.retry_config.max_retries,
                "base_delay": reliable_crawler.retry_config.base_delay,
                "max_delay": reliable_crawler.retry_config.max_delay,
                "connection_timeout": reliable_crawler.timeout_config.connection_timeout,
                "read_timeout": reliable_crawler.timeout_config.read_timeout
            }
        }
        
        # Convert datetime objects to ISO strings for JSON serialization
        serialized_data = serialize_datetime_objects(response_data)
        
        return json.dumps(serialized_data, indent=2)
        
    except Exception as e:
        return json.dumps({
            "success": False,
            "error": str(e)
        }, indent=2)

@mcp.tool()
async def reset_reliability_state(resources: Crawl4AIContext = get_resources) -> str:
    """
    Reset reliability service state (circuit breaker and cache).
    
    This tool allows manual intervention to reset the reliability service state:
    - Resets circuit breaker to CLOSED state
    - Clears all cached content
    - Resets failure counters
    
    Use this tool when you need to recover from persistent failures or
    clear stale cached content.
    
    Args:
        ctx: The MCP server provided context
    
    Returns:
        JSON string with reset operation results
    """
    try:
        # Using injected resources
        reliable_crawler = resources.reliable_crawler
        
        if not reliable_crawler:
            return json.dumps({
                "success": False,
                "error": "Reliable crawler not available"
            }, indent=2)
        
        # Reset circuit breaker
        await reliable_crawler.reset_circuit_breaker()
        
        # Clear cache
        await reliable_crawler.clear_cache()
        
        return json.dumps({
            "success": True,
            "message": "Reliability state reset successfully",
            "actions_performed": [
                "Circuit breaker reset to CLOSED state",
                "Cache cleared",
                "Failure counters reset"
            ]
        }, indent=2)
        
    except Exception as e:
        return json.dumps({
            "success": False,
            "error": str(e)
        }, indent=2)

@mcp.tool()
async def test_url_health(url: str, resources: Crawl4AIContext = get_resources) -> str:
    """
    Test the health and accessibility of a URL before crawling.
    
    This tool performs comprehensive health checks including:
    - Connection validation
    - DNS resolution
    - HTTP response status
    - Response time measurement
    
    Use this tool to diagnose connectivity issues before attempting to crawl.
    
    Args:
        ctx: The MCP server provided context
        url: URL to test for health and accessibility
    
    Returns:
        JSON string with health check results
    """
    try:
        # Using injected resources
        reliable_crawler = resources.reliable_crawler
        
        if not reliable_crawler:
            return json.dumps({
                "success": False,
                "error": "Reliable crawler not available"
            }, indent=2)
        
        start_time = time.time()
        
        # Perform health check
        is_healthy, error = await reliable_crawler.health_checker.check_url_health(url)
        
        response_time_ms = int((time.time() - start_time) * 1000)
        
        # Parse URL for additional info
        from urllib.parse import urlparse
        parsed_url = urlparse(url)
        
        return json.dumps({
            "success": True,
            "url": url,
            "health_status": {
                "is_healthy": is_healthy,
                "error": error,
                "response_time_ms": response_time_ms
            },
            "url_analysis": {
                "scheme": parsed_url.scheme,
                "domain": parsed_url.netloc,
                "path": parsed_url.path,
                "is_secure": parsed_url.scheme == "https"
            },
            "recommendations": _get_health_recommendations(is_healthy, error, response_time_ms)
        }, indent=2)
        
    except Exception as e:
        return json.dumps({
            "success": False,
            "url": url,
            "error": str(e)
        }, indent=2)

def _get_health_recommendations(is_healthy: bool, error: Optional[str], response_time_ms: int) -> List[str]:
    """Generate health check recommendations."""
    recommendations = []
    
    if not is_healthy:
        if error:
            if "timeout" in error.lower():
                recommendations.append("Consider increasing timeout settings")
                recommendations.append("Check if the server is experiencing high load")
            elif "connection" in error.lower():
                recommendations.append("Verify the URL is correct and accessible")
                recommendations.append("Check network connectivity")
            elif "http" in error.lower():
                recommendations.append("Server returned an error status - check server health")
            else:
                recommendations.append("Unknown error - check URL validity and network connection")
    else:
        recommendations.append("URL is healthy and accessible")
        
        if response_time_ms > 5000:
            recommendations.append("Response time is slow - consider optimizing crawl settings")
        elif response_time_ms > 2000:
            recommendations.append("Response time is moderate - monitor for performance")
        else:
            recommendations.append("Good response time")
    
    return recommendations

@mcp.tool()
async def start_crawl_job(urls: List[str], crawler_config: Optional[Dict[str, Any]] = None, browser_config: Optional[Dict[str, Any]] = None, resources: Crawl4AIContext = get_resources) -> str:
    """
    Start an asynchronous crawl job for long-running operations.
    
    This tool submits a crawl job to the Crawl4AI job management system, allowing
    long-running crawl operations to execute asynchronously without timing out
    Claude Desktop. The job will run in the background and can be monitored
    using check_job_status and retrieved using get_job_result.
    
    Args:
        ctx: The MCP server provided context
        urls: List of URLs to crawl
        crawler_config: Optional crawler configuration (e.g., cache_mode, extraction_strategy)
        browser_config: Optional browser configuration (e.g., headless, user_agent)
    
    Returns:
        JSON string with job submission result including job_id for tracking
    """
    try:
        # Using injected resources
        job_manager = resources.job_manager
        
        if not job_manager:
            return json.dumps({
                "success": False,
                "error": "Job management not available. Set USE_JOB_MANAGEMENT=true to enable async processing."
            }, indent=2)
        
        # Submit the job
        job_id = await job_manager.submit_crawl_job(
            urls=urls,
            crawler_config=crawler_config,
            browser_config=browser_config
        )
        
        return json.dumps({
            "success": True,
            "job_id": job_id,
            "message": f"Crawl job submitted successfully. Use check_job_status with job_id '{job_id}' to monitor progress.",
            "urls_count": len(urls),
            "estimated_duration": "Variable based on content size and complexity"
        }, indent=2)
        
    except Exception as e:
        return json.dumps({
            "success": False,
            "error": f"Failed to start crawl job: {str(e)}"
        }, indent=2)

@mcp.tool()
async def check_job_status(job_id: str, resources: Crawl4AIContext = get_resources) -> str:
    """
    Check the status of an asynchronous crawl job.
    
    This tool monitors the progress of a previously submitted crawl job,
    providing real-time status updates including progress percentage,
    current status, and any error messages.
    
    Args:
        ctx: The MCP server provided context
        job_id: The job ID returned from start_crawl_job
    
    Returns:
        JSON string with current job status, progress, and information
    """
    try:
        # Using injected resources
        job_manager = resources.job_manager
        
        if not job_manager:
            return json.dumps({
                "success": False,
                "error": "Job management not available. Set USE_JOB_MANAGEMENT=true to enable async processing."
            }, indent=2)
        
        # Get job status
        job_result = await job_manager.get_job_status(job_id)
        
        return json.dumps({
            "success": True,
            "job_id": job_result.job_id,
            "status": job_result.status.value,
            "progress": job_result.progress,
            "message": job_result.message,
            "created_at": job_result.created_at,
            "updated_at": job_result.updated_at,
            "is_complete": job_result.status in [JobStatus.COMPLETED, JobStatus.FAILED, JobStatus.CANCELLED],
            "error": job_result.error,
            # Enhanced URL-level tracking information
            "url_tracking": {
                "total_urls": job_result.total_urls,
                "successful_urls": job_result.successful_urls,
                "failed_urls": job_result.failed_urls,
                "success_rate": job_result.success_rate,
                "is_partial_success": job_result.is_partial_success,
                "summary": job_result.job_summary
            },
            "url_results": job_result.url_results if job_result.url_results else {}
        }, indent=2)
        
    except ValueError as e:
        return json.dumps({
            "success": False,
            "job_id": job_id,
            "error": f"Job not found: {str(e)}"
        }, indent=2)
    except Exception as e:
        return json.dumps({
            "success": False,
            "job_id": job_id,
            "error": f"Failed to check job status: {str(e)}"
        }, indent=2)

@mcp.tool()
async def get_job_result(job_id: str, wait_for_completion: bool = False, timeout_seconds: Optional[int] = None, resources: Crawl4AIContext = get_resources) -> str:
    """
    Retrieve the result of a completed crawl job.
    
    This tool gets the final result of a crawl job. It can optionally wait
    for job completion if the job is still running. The result includes
    all crawled content, metadata, and processing statistics.
    
    Args:
        ctx: The MCP server provided context
        job_id: The job ID returned from start_crawl_job
        wait_for_completion: Whether to wait for the job to complete if still running
        timeout_seconds: Maximum time to wait for completion (default: no timeout)
    
    Returns:
        JSON string with complete job result including all crawled data
    """
    try:
        # Using injected resources
        job_manager = resources.job_manager
        
        if not job_manager:
            return json.dumps({
                "success": False,
                "error": "Job management not available. Set USE_JOB_MANAGEMENT=true to enable async processing."
            }, indent=2)
        
        # Wait for completion if requested
        if wait_for_completion:
            job_result = await job_manager.wait_for_job_completion(
                job_id=job_id,
                timeout=timeout_seconds
            )
        else:
            job_result = await job_manager.get_job_status(job_id)
        
        # Check if job is complete
        if job_result.status not in [JobStatus.COMPLETED, JobStatus.FAILED]:
            return json.dumps({
                "success": False,
                "job_id": job_id,
                "status": job_result.status.value,
                "message": "Job is not yet complete. Use wait_for_completion=true to wait for completion.",
                "progress": job_result.progress
            }, indent=2)
        
        # Return complete result
        response = {
            "success": job_result.status == JobStatus.COMPLETED,
            "job_id": job_result.job_id,
            "status": job_result.status.value,
            "progress": job_result.progress,
            "message": job_result.message,
            "created_at": job_result.created_at,
            "updated_at": job_result.updated_at
        }
        
        if job_result.status == JobStatus.COMPLETED and job_result.result:
            response["result"] = job_result.result
        elif job_result.status == JobStatus.FAILED and job_result.error:
            response["error"] = job_result.error
        
        return json.dumps(response, indent=2)
        
    except ValueError as e:
        return json.dumps({
            "success": False,
            "job_id": job_id,
            "error": f"Job not found: {str(e)}"
        }, indent=2)
    except Exception as e:
        return json.dumps({
            "success": False,
            "job_id": job_id,
            "error": f"Failed to get job result: {str(e)}"
        }, indent=2)

@mcp.tool()
async def cancel_crawl_job(job_id: str, resources: Crawl4AIContext = get_resources) -> str:
    """
    Cancel a running crawl job.
    
    This tool cancels a previously submitted crawl job that is currently
    running or pending. Once cancelled, the job cannot be resumed.
    
    Args:
        ctx: The MCP server provided context
        job_id: The job ID to cancel
    
    Returns:
        JSON string with cancellation result
    """
    try:
        # Using injected resources
        job_manager = resources.job_manager
        
        if not job_manager:
            return json.dumps({
                "success": False,
                "error": "Job management not available. Set USE_JOB_MANAGEMENT=true to enable async processing."
            }, indent=2)
        
        # Cancel the job
        cancelled = await job_manager.cancel_job(job_id)
        
        if cancelled:
            return json.dumps({
                "success": True,
                "job_id": job_id,
                "message": "Job cancelled successfully"
            }, indent=2)
        else:
            return json.dumps({
                "success": False,
                "job_id": job_id,
                "message": "Failed to cancel job. Job may already be complete or not found."
            }, indent=2)
        
    except Exception as e:
        return json.dumps({
            "success": False,
            "job_id": job_id,
            "error": f"Failed to cancel job: {str(e)}"
        }, indent=2)

@mcp.tool()
async def list_crawl_jobs(resources: Crawl4AIContext = get_resources) -> str:
    """
    List all active crawl jobs.
    
    This tool returns a list of all currently tracked crawl jobs,
    including their status, progress, and basic information.
    
    Args:
        ctx: The MCP server provided context
    
    Returns:
        JSON string with list of all active jobs
    """
    try:
        # Using injected resources
        job_manager = resources.job_manager
        
        if not job_manager:
            return json.dumps({
                "success": False,
                "error": "Job management not available. Set USE_JOB_MANAGEMENT=true to enable async processing."
            }, indent=2)
        
        # Get all active jobs
        active_jobs = await job_manager.list_active_jobs()
        
        jobs_list = []
        for job in active_jobs:
            jobs_list.append({
                "job_id": job.job_id,
                "status": job.status.value,
                "progress": job.progress,
                "message": job.message,
                "created_at": job.created_at,
                "updated_at": job.updated_at
            })
        
        return json.dumps({
            "success": True,
            "active_jobs": jobs_list,
            "total_jobs": len(jobs_list)
        }, indent=2)
        
    except Exception as e:
        return json.dumps({
            "success": False,
            "error": f"Failed to list jobs: {str(e)}"
        }, indent=2)

async def crawl_batch(crawler: AsyncWebCrawler, urls: List[str], max_concurrent: int = 10) -> List[Dict[str, Any]]:
    """
    Batch crawl multiple URLs in parallel.
    
    Args:
        crawler: AsyncWebCrawler instance
        urls: List of URLs to crawl
        max_concurrent: Maximum number of concurrent browser sessions (used for semaphore_count)
        
    Returns:
        List of dictionaries with URL and markdown content
    """
    # Configure crawl with semaphore_count for concurrency control
    crawl_config = CrawlerRunConfig(
        cache_mode=CacheMode.BYPASS, 
        stream=False,
        semaphore_count=max_concurrent  # Use semaphore_count for concurrency control
    )

    # Use the correct arun_many API (no dispatcher parameter)
    results = await crawler.arun_many(urls=urls, config=crawl_config)
    return [{'url': r.url, 'markdown': r.markdown} for r in results if r.success and r.markdown]

async def crawl_recursive_internal_links(crawler: AsyncWebCrawler, start_urls: List[str], max_depth: int = 3, max_concurrent: int = 10) -> List[Dict[str, Any]]:
    """
    Recursively crawl internal links from start URLs up to a maximum depth.
    
    Args:
        crawler: AsyncWebCrawler instance
        start_urls: List of starting URLs
        max_depth: Maximum recursion depth
        max_concurrent: Maximum number of concurrent browser sessions
        
    Returns:
        List of dictionaries with URL and markdown content
    """
    # Configure crawl with semaphore_count for concurrency control  
    run_config = CrawlerRunConfig(
        cache_mode=CacheMode.BYPASS, 
        stream=False,
        semaphore_count=max_concurrent  # Use semaphore_count for concurrency control
    )

    visited = set()

    def normalize_url(url):
        return urldefrag(url)[0]

    current_urls = set([normalize_url(u) for u in start_urls])
    results_all = []

    for depth in range(max_depth):
        urls_to_crawl = [normalize_url(url) for url in current_urls if normalize_url(url) not in visited]
        if not urls_to_crawl:
            break

        # Use the correct arun_many API (no dispatcher parameter)
        results = await crawler.arun_many(urls=urls_to_crawl, config=run_config)
        next_level_urls = set()

        for result in results:
            norm_url = normalize_url(result.url)
            visited.add(norm_url)

            if result.success and result.markdown:
                results_all.append({'url': result.url, 'markdown': result.markdown})
                for link in result.links.get("internal", []):
                    next_url = normalize_url(link["href"])
                    if next_url not in visited:
                        next_level_urls.add(next_url)

        current_urls = next_level_urls

    return results_all

async def initialize_global_components():
    """Initialize expensive components before server startup."""
    global _cached_reranking_model, _reranking_model_lock, _initialization_complete
    
    if _initialization_complete:
        return
    
    print("🔄 Pre-initializing global components...")
    
    # Initialize the lock if it doesn't exist
    if _reranking_model_lock is None:
        _reranking_model_lock = asyncio.Lock()
    
    # Pre-load the reranking model if enabled
    if os.getenv("USE_RERANKING", "false") == "true":
        async with _reranking_model_lock:
            if _cached_reranking_model is None:
                try:
                    print("🔄 Pre-loading CrossEncoder model...")
                    # Load model in executor to prevent blocking
                    loop = asyncio.get_event_loop()
                    _cached_reranking_model = await loop.run_in_executor(
                        None, 
                        lambda: CrossEncoder("cross-encoder/ms-marco-MiniLM-L-6-v2", device="cpu")
                    )
                    print("✅ CrossEncoder model pre-loaded successfully")
                except Exception as e:
                    print(f"❌ Failed to pre-load reranking model: {e}")
                    _cached_reranking_model = None
    
    _initialization_complete = True
    print("✅ Global components pre-initialization complete")

def setup_signal_handlers():
    """Setup signal handlers for graceful shutdown."""
    def signal_handler(signum, frame):
        print(f"\n🛑 Received signal {signum}, initiating graceful shutdown...")
        # Create a new event loop for cleanup since we might be in a signal handler
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(_cleanup_global_resources())
        finally:
            loop.close()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

async def main():
    global _server_initialized, _initialization_lock
    
    # Setup signal handlers for graceful shutdown
    setup_signal_handlers()
    
    # Pre-initialize expensive components AND core server resources
    await initialize_global_components()
    
    # Initialize lock if needed
    if _initialization_lock is None:
        _initialization_lock = asyncio.Lock()
    
    # Initialize core MCP server resources before starting to accept connections
    print("🔄 Pre-initializing MCP server resources...")
    async with _initialization_lock:
        if not _server_initialized:
            await _initialize_global_resources()
            print("✅ MCP server resources pre-initialized")
        else:
            print("✅ MCP server resources already initialized")
    
    # Add a small delay to ensure everything is fully ready
    await asyncio.sleep(0.5)
    print("🚀 Server fully initialized, ready to accept connections")
    
    connection_errors = 0
    max_connection_errors = 5
    
    try:
        transport = os.getenv("TRANSPORT", "sse")
        if transport == 'sse':
            print(f"🌐 Starting SSE server on {os.getenv('HOST', '0.0.0.0')}:{os.getenv('PORT', '8051')}")
            # Run the MCP server with sse transport
            await mcp.run_sse_async()
        else:
            print("📜 Starting stdio transport")
            # Run the MCP server with stdio transport
            await mcp.run_stdio_async()
    except KeyboardInterrupt:
        print("\n🛑 Keyboard interrupt received, shutting down...")
    except Exception as e:
        error_str = str(e).lower()
        error_type = type(e).__name__
        
        # Handle connection-related errors gracefully
        if any(keyword in error_str for keyword in [
            'brokenresourceerror', 'connection', 'broken', 'closed', 'disconnected'
        ]) or error_type in ['BrokenResourceError', 'ConnectionResetError']:
            connection_errors += 1
            print(f"⚠️ Connection error #{connection_errors} handled: {error_type}")
            
            if connection_errors < max_connection_errors:
                print(f"🔄 Server continuing (max {max_connection_errors} connection errors allowed)")
                # Don't exit, let the server continue running
            else:
                print(f"❌ Too many connection errors ({connection_errors}), shutting down")
                raise e
        else:
            print(f"❌ Server error: {e}")
            import traceback
            print(f"Traceback: {traceback.format_exc()}")
            raise e
    finally:
        # Cleanup resources
        print("🔄 Starting server cleanup...")
        await _cleanup_global_resources()
        print("💯 Server shutdown complete")

if __name__ == "__main__":
    asyncio.run(main())